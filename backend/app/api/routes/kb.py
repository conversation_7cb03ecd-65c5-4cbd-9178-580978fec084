from datetime import timed<PERSON><PERSON>
from uuid import UUID, uuid4

from celery.result import As<PERSON><PERSON><PERSON><PERSON>
from fastapi import APIRouter, BackgroundTasks, Depends, HTTPException
from fastapi.responses import JSONResponse
from sqlmodel.ext.asyncio.session import AsyncSession

from app.api.deps import CurrentUser, get_async_session
from app.core.config import settings
from app.logger import logger
from app.models import (
    AsyncTaskStatus,
    AvailableUsersCurrentWorkspace,
    DocumentKBCreate,
    DocumentsKBRead,
    DocumentType,
    KBCreate,
    KBRead,
    KBsRead,
    KBUpdate,
)
from app.repositories.kb import KBRepository
from app.repositories.object_storage.base import BaseStorageRepository
from app.repositories.object_storage.provider import get_object_storage_repository
from app.schemas.kb import (
    ConfirmUploadsRequest,
    PresignedUrlInfo,
    PresignedUrlRequest,
    PresignedUrlResponse,
    TaskStatusResponse,
    URLsUploadRequest,
)
from app.services.kb_service import KBService
from app.services.point_limit_service import PointLimitService
from app.tasks.kb_tasks import (
    delete_document_task,
    ingest_from_files_task,
    ingest_from_website_task,
)

router = APIRouter()


@router.post("/kbs", response_model=KBRead)
async def create_kb(
    request: KBCreate,
    current_user: CurrentUser,
    session: AsyncSession = Depends(get_async_session),
) -> KBRead:
    try:
        repo = KBRepository(session)
        kb_result = await repo.create(
            request,
            current_user.id,
            current_user.current_workspace_id,
        )
        if kb_result.failure:
            raise HTTPException(status_code=400, detail=kb_result.error)
        return kb_result.value
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error creating collection: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/kbs", response_model=KBsRead)
async def get_kbs(
    current_user: CurrentUser,
    session: AsyncSession = Depends(get_async_session),
    skip: int = 0,
    limit: int = 50,
) -> KBsRead:
    """Get all knowledge bases for the current user"""
    try:
        repo = KBRepository(session)
        kbs_result = await repo.get_kbs(
            current_user.current_workspace_id,
            current_user.id,
            skip,
            limit,
        )
        if kbs_result.failure:
            raise HTTPException(status_code=404, detail=kbs_result.error)
        return kbs_result.value
    except Exception as e:
        logger.error(f"Error getting collections: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/kbs/available-users", response_model=AvailableUsersCurrentWorkspace)
async def get_available_users(
    current_user: CurrentUser,
    session: AsyncSession = Depends(get_async_session),
) -> AvailableUsersCurrentWorkspace:
    """
    Get a list of users available for sharing knowledge bases within the current workspace.
    Returns users that are in the same workspace as the current user.
    """
    try:
        repo = KBRepository(session)
        users_result = await repo.get_all_users_in_workspace(
            current_user.current_workspace_id
        )
        if users_result.failure:
            raise HTTPException(status_code=404, detail=users_result.error)
        users = users_result.value

        return AvailableUsersCurrentWorkspace(
            data=[
                {"id": str(user.id), "email": user.email, "full_name": user.full_name}
                for user in users
            ],
            count=len(users),
        )
    except Exception as e:
        logger.error(f"Error fetching available users: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/kbs/point-usage")
async def get_point_usage(
    current_user: CurrentUser,
    session: AsyncSession = Depends(get_async_session),
) -> dict:
    """Get user's point usage across all knowledge bases"""
    try:
        point_service = PointLimitService(session)
        usage = await point_service.get_user_point_usage(
            current_user.id, current_user.current_workspace_id
        )
        return usage
    except Exception as e:
        logger.error(f"Error getting point usage: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/kbs/{kb_id}", response_model=KBRead)
async def get_kb_by_id(
    kb_id: UUID,
    current_user: CurrentUser,
    session: AsyncSession = Depends(get_async_session),
) -> KBRead:
    """Get a specific knowledge base by ID"""
    try:
        repo = KBRepository(session)
        kb_result = await repo.get(
            kb_id,
            current_user.id,
            current_user.current_workspace_id,
        )
        if kb_result.failure:
            raise HTTPException(status_code=404, detail=kb_result.error)
        kb = kb_result.value

        # Check if user has access to the KB
        if kb.owner_id != current_user.id and kb.access_level != "shared":
            # For shared KBs, check if user is in allowed_users
            if current_user.id not in kb.allowed_users:
                raise HTTPException(
                    status_code=403,
                    detail="You don't have access to this knowledge base",
                )

        return kb
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting knowledge base: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.put("/kbs/{kb_id}", response_model=KBRead)
async def update_kb(
    kb_id: UUID,
    request: KBUpdate,
    current_user: CurrentUser,
    session: AsyncSession = Depends(get_async_session),
) -> KBRead:
    try:
        repo = KBRepository(session)
        kb_result = await repo.update(
            kb_id,
            request,
            current_user.id,
            current_user.current_workspace_id,
        )
        if kb_result.failure:
            raise HTTPException(status_code=403, detail=kb_result.error)
        return kb_result.value
    except Exception as e:
        logger.error(f"Error updating collection: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/kbs/{kb_id}", response_model=dict)
async def delete_kb(
    kb_id: UUID,
    current_user: CurrentUser,
    session: AsyncSession = Depends(get_async_session),
    background_tasks: BackgroundTasks = BackgroundTasks(),
) -> JSONResponse:
    try:
        # 1. Delete the KB in the database
        kb_repo = KBRepository(session)
        kb_result = await kb_repo.delete(kb_id, current_user)
        if kb_result.failure:
            raise HTTPException(status_code=403, detail=kb_result.error)

        # 2. Delete the KB in the vector store (async)
        kb_service = KBService()
        background_tasks.add_task(kb_service.delete_collection, str(kb_id))

        # 3. Delete the KB in the object storage (async)
        # TODO: Consider move objects to a different archieve bucket
        # TODO: assign to @duc.bui
        os_repo: BaseStorageRepository = get_object_storage_repository()
        background_tasks.add_task(
            os_repo.delete_multiple_files,
            object_names=[f"{kb_id}/*"],
            bucket_name=settings.KB_BUCKET,
        )

        return JSONResponse(
            content={
                "status": "success",
                "message": "Knowledge base deleted successfully",
            },
        )
    except Exception as e:
        logger.error(f"Error deleting collection: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/kbs/{kb_id}/presigned-urls")
async def generate_presigned_urls(
    kb_id: UUID,
    request: PresignedUrlRequest,
    current_user: CurrentUser,
    session: AsyncSession = Depends(get_async_session),
) -> PresignedUrlResponse:
    """
    Generate presigned URLs for file uploads.

    This endpoint generates presigned URLs that clients can use to upload files
    directly to S3, bypassing the backend for better performance and scalability.
    """
    try:
        # Verify KB exists and user has access
        repo = KBRepository(session)
        kb_result = await repo.get(
            kb_id,
            current_user.id,
            current_user.current_workspace_id,
        )
        if kb_result.failure:
            raise HTTPException(status_code=404, detail=kb_result.error)
        kb = kb_result.value

        # Check if user has access to the KB
        if kb.owner_id != current_user.id and kb.access_level != "shared":
            if current_user.id not in kb.allowed_users:
                raise HTTPException(
                    status_code=403,
                    detail="You don't have access to this knowledge base",
                )

        os_repo: BaseStorageRepository = get_object_storage_repository()
        presigned_urls = []

        for file_info in request.files:
            # Additional server-side validation (defense in depth)
            from app.schemas.mime_type import (
                sanitize_filename,
                validate_mime_type_for_kb,
            )

            # Validate MIME type
            if not validate_mime_type_for_kb(file_info.content_type):
                raise HTTPException(
                    status_code=400,
                    detail=f"File type '{file_info.content_type}' is not allowed",
                )

            # Sanitize filename
            try:
                sanitized_filename = sanitize_filename(file_info.filename)
            except ValueError as e:
                raise HTTPException(
                    status_code=400, detail=f"Invalid filename: {str(e)}"
                )

            # Generate unique object name with sanitized filename
            object_name = f"{kb_id}/{uuid4()}_{sanitized_filename}"

            # Generate presigned URL with content type
            presigned_url = await os_repo.get_presigned_put_url(
                object_name=object_name,
                bucket_name=settings.KB_BUCKET,
                expires=3600,  # 1 hour expiration
                content_type=file_info.content_type,
            )

            presigned_urls.append(
                PresignedUrlInfo(
                    file_id=file_info.file_id,
                    filename=sanitized_filename,  # Use sanitized filename
                    storage_key=object_name,
                    presigned_url=presigned_url,
                )
            )

        return PresignedUrlResponse(
            kb_id=str(kb_id),
            presigned_urls=presigned_urls,
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error generating presigned URLs: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/kbs/{kb_id}/confirm-uploads", response_model=TaskStatusResponse)
async def confirm_file_uploads(
    kb_id: UUID,
    request: ConfirmUploadsRequest,
    current_user: CurrentUser,
    session: AsyncSession = Depends(get_async_session),
) -> TaskStatusResponse:
    """
    Confirm file uploads and start ingestion process.

    This endpoint should be called after files have been successfully uploaded
    using the presigned URLs to start the document ingestion process.
    """
    try:
        # Verify KB exists and user has access
        repo = KBRepository(session)
        kb_result = await repo.get(
            kb_id,
            current_user.id,
            current_user.current_workspace_id,
        )
        if kb_result.failure:
            raise HTTPException(status_code=404, detail=kb_result.error)
        kb = kb_result.value

        # Check if user has access to the KB
        if kb.owner_id != current_user.id and kb.access_level != "shared":
            if current_user.id not in kb.allowed_users:
                raise HTTPException(
                    status_code=403,
                    detail="You don't have access to this knowledge base",
                )

        # Verify files exist in storage
        os_repo: BaseStorageRepository = get_object_storage_repository()
        verified_files = []

        for file_info in request.uploaded_files:
            # Check if file exists in storage
            if await os_repo.file_exists(file_info.storage_key, settings.KB_BUCKET):
                verified_files.append(file_info)
            else:
                logger.warning(f"File not found in storage: {file_info.storage_key}")

        if not verified_files:
            raise HTTPException(
                status_code=400,
                detail="No uploaded files found in storage",
            )

        # Create document records
        docs_create = []
        for file_info in verified_files:
            doc_create = DocumentKBCreate(
                kb_id=str(kb_id),
                name=file_info.filename,
                type=DocumentType.FILE,
                object_name=file_info.storage_key,
                file_name=file_info.filename,
                file_type=file_info.content_type,
                embed_status=AsyncTaskStatus.IN_PROGRESS,
            )
            docs_create.append(doc_create)

        docs = await repo.create_documents(docs_create)

        # Start ingestion task
        task = ingest_from_files_task.delay(
            doc_ids=[str(doc.id) for doc in docs],
            kb_id=str(kb_id),
            user_id=str(current_user.id),
            workspace_id=str(current_user.current_workspace_id),
        )

        return TaskStatusResponse(
            task_id=task.id,
            status=AsyncTaskStatus.IN_PROGRESS,
            status_message=f"Ingestion started for {len(verified_files)} files",
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error confirming file uploads: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/kbs/{kb_id}/documents", response_model=TaskStatusResponse)
async def upload_urls(
    kb_id: UUID,
    request: URLsUploadRequest,
    current_user: CurrentUser,
    session: AsyncSession = Depends(get_async_session),
) -> TaskStatusResponse:
    try:
        urls: list[str] | None = request.urls
        deep_crawls: list[bool] | None = request.deep_crawls

        kb_repo = KBRepository(session)
        kb_result = await kb_repo.get(
            kb_id,
            current_user.id,
            current_user.current_workspace_id,
        )
        if kb_result.failure:
            raise HTTPException(status_code=404, detail=kb_result.error)
        kb = kb_result.value
        if kb.owner_id != current_user.id and kb.access_level != "shared":
            if current_user.id not in kb.allowed_users:
                raise HTTPException(
                    status_code=403,
                    detail="You don't have access to this knowledge base",
                )

        # Validate URLs before processing
        def validate_url(url: str) -> None:
            """Validate URL for security"""
            import re
            from urllib.parse import urlparse

            if not url or len(url) > 2048:
                raise ValueError("URL is empty or too long")

            if not url.startswith(("http://", "https://")):
                raise ValueError("URL must start with http:// or https://")

            try:
                parsed = urlparse(url)
                hostname = parsed.hostname

                if not hostname:
                    raise ValueError("Invalid URL hostname")

                # Block localhost and private IPs
                hostname_lower = hostname.lower()
                if (
                    hostname_lower in ["localhost", "127.0.0.1", "::1"]
                    or re.match(r"^10\.", hostname_lower)
                    or re.match(r"^192\.168\.", hostname_lower)
                    or re.match(r"^172\.(1[6-9]|2[0-9]|3[0-1])\.", hostname_lower)
                    or re.match(r"^169\.254\.", hostname_lower)
                ):
                    raise ValueError("Private or localhost URLs are not allowed")

                # Check for suspicious patterns
                suspicious_patterns = [
                    r"javascript:",
                    r"data:",
                    r"vbscript:",
                    r"<script",
                    r"onload=",
                    r"onerror=",
                ]
                for pattern in suspicious_patterns:
                    if re.search(pattern, url, re.IGNORECASE):
                        raise ValueError("URL contains suspicious content")

            except Exception as e:
                if isinstance(e, ValueError):
                    raise
                raise ValueError("Invalid URL format")

        doc_creates = []
        for url, deep_crawl in zip(urls, deep_crawls, strict=True):
            # Validate each URL
            try:
                validate_url(url)
            except ValueError as e:
                raise HTTPException(
                    status_code=400, detail=f"Invalid URL '{url}': {str(e)}"
                )

            document_create = DocumentKBCreate(
                name=url.split("/")[-1],
                kb_id=str(kb_id),
                url=url,
                deep_crawl=deep_crawl,
                type=DocumentType.URL,
                file_name=url.split("/")[-1],
                embed_status=AsyncTaskStatus.IN_PROGRESS,
            )
            doc_creates.append(document_create)
        docs = await kb_repo.create_documents(doc_creates)
        logger.info(f"Created {len(docs)} documents")
        doc_ids = [str(doc.id) for doc in docs]
        logger.info(f"Doc IDs: {doc_ids}")

        task = ingest_from_website_task.delay(
            doc_ids=doc_ids,
            kb_id=str(kb_id),
            user_id=str(current_user.id),
            workspace_id=str(current_user.current_workspace_id),
        )

        return TaskStatusResponse(
            task_id=task.id,
            status=AsyncTaskStatus.IN_PROGRESS,
            status_message="Task is pending",
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error adding documents: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get(
    "/kbs/{kb_id}/documents",
    response_model=DocumentsKBRead,
)
async def list_documents(
    kb_id: UUID,
    current_user: CurrentUser,
    session: AsyncSession = Depends(get_async_session),
    skip: int = 0,
    limit: int = 50,
    search: str | None = None,
) -> DocumentsKBRead:
    """
    List documents in a knowledge base.

    User must have access to the knowledge base (owner for personal knowledge bases,
    workspace member for workspace knowledge bases).
    """
    try:
        kb_repo = KBRepository(session)
        kb_result = await kb_repo.get(
            kb_id,
            current_user.id,
            current_user.current_workspace_id,
        )
        if kb_result.failure:
            raise HTTPException(status_code=404, detail=kb_result.error)

        return await kb_repo.list_documents(kb_id, skip, limit, search)
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error listing documents: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get(
    "/kbs/{kb_id}/documents/content",
)
async def get_document_content(
    kb_id: UUID,
    object_name: str,
    current_user: CurrentUser,
    session: AsyncSession = Depends(get_async_session),
) -> str:
    try:
        repo = KBRepository(session)
        kb_result = await repo.get(
            kb_id,
            current_user.id,
            current_user.current_workspace_id,
        )
        if kb_result.failure:
            raise HTTPException(status_code=404, detail=kb_result.error)

        os_repo: BaseStorageRepository = get_object_storage_repository()
        is_exists = await os_repo.file_exists(
            object_name, bucket_name=settings.KB_BUCKET
        )
        if not is_exists:
            raise HTTPException(status_code=404, detail="Document not found")

        presigned_url: str = await os_repo.get_presigned_url(
            object_name=object_name,
            bucket_name=settings.KB_BUCKET,
            expires=timedelta(seconds=3600),
        )

        return presigned_url

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting document content: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/kbs/{kb_id}/documents/{document_id}", response_model=dict)
async def delete_document(
    kb_id: UUID,
    document_id: str,
    object_name: str,
    current_user: CurrentUser,
    session: AsyncSession = Depends(get_async_session),
) -> JSONResponse:
    try:
        # Verify user has access to the knowledge base
        repo = KBRepository(session)
        kb_result = await repo.get(
            kb_id,
            current_user.id,
            current_user.current_workspace_id,
        )
        if kb_result.failure:
            raise HTTPException(status_code=404, detail=kb_result.error)

        # Quick check if document exists before starting task
        os_repo: BaseStorageRepository = get_object_storage_repository()

        # Check if document exists
        if not await os_repo.file_exists(object_name, bucket_name=settings.KB_BUCKET):
            raise HTTPException(
                status_code=404,
                detail="Document not found",
            )

        # Start the deletion task
        task = delete_document_task.delay(
            kb_id=str(kb_id),
            document_id=document_id,
            object_name=object_name,
        )

        return JSONResponse(
            content={
                "status": AsyncTaskStatus.PENDING,
                "message": "Document deletion started",
                "document_id": document_id,
                "task_id": task.id,
                "details": {
                    "kb_id": str(kb_id),
                    "document_name": document_id,
                    "action": "delete_document",
                },
            }
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error starting document deletion: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/tasks/{task_id}", response_model=TaskStatusResponse)
async def get_task_status(task_id: UUID) -> TaskStatusResponse:
    """
    Get the status of an asynchronous task.

    This endpoint returns the current status and progress of a Celery task,
    such as document ingestion.
    """
    try:
        # Get the Celery task result
        task_result = AsyncResult(str(task_id))

        # Initialize response with basic information
        response = {
            "task_id": str(task_id),
            "status": task_result.state,
            "progress": 0,
        }

        # Handle different task states
        if task_result.state == AsyncTaskStatus.PENDING:
            response["status_message"] = "Task is pending"

        elif task_result.state == AsyncTaskStatus.IN_PROGRESS:
            if task_result.info and isinstance(task_result.info, dict):
                meta = task_result.info
                response["progress"] = meta.get("progress", 0)
                response["status_message"] = meta.get("message", "Processing")

        elif task_result.state == AsyncTaskStatus.COMPLETED:
            response["progress"] = 100
            response["status_message"] = "Task completed successfully"

            # Add result info if available
            if task_result.result and isinstance(task_result.result, dict):
                response["result"] = task_result.result

        elif task_result.state == AsyncTaskStatus.FAILED:
            response["status_message"] = "Task failed"

            if task_result.info and isinstance(task_result.info, dict):
                meta = task_result.info
                response["error"] = meta.get("error", str(task_result.result))
            elif task_result.result:
                response["error"] = str(task_result.result)

        return TaskStatusResponse(**response)

    except Exception as e:
        logger.error(f"Error getting task status: {str(e)}")
        raise HTTPException(
            status_code=500, detail=f"Error retrieving task status: {str(e)}"
        )
