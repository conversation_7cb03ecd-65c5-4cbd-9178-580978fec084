# Import centralized definitions
from app.schemas.mime_type import EXT_TO_FILETYPE, MIME_TO_FILETYPE
from app.schemas.mime_type import FileType as CentralizedFileType

# Use centralized FileType for backward compatibility
FileType = CentralizedFileType


# Use centralized mapping for backward compatibility
STR_TO_FILETYPE = MIME_TO_FILETYPE


MIMETYPES_TO_EXCLUDE = [
    "text/x-markdown",
    "application/epub+zip",
    "text/x-csv",
    "application/csv",
    "application/x-csv",
    "text/comma-separated-values",
    "text/x-comma-separated-values",
]

FILETYPE_TO_MIMETYPE = {
    v: k for k, v in STR_TO_FILETYPE.items() if k not in MIMETYPES_TO_EXCLUDE
}

# NOTE: copy from unstructured.io
PLAIN_TEXT_EXTENSIONS = [
    ".txt",
    ".text",
    ".eml",
    ".md",
    ".rtf",
    ".html",
    ".rst",
    ".org",
    ".csv",
    ".tsv",
    ".tab",
    ".json",
]

FILETYPE_TO_EXT = {v: k for k, v in EXT_TO_FILETYPE.items()}

# Re-export centralized functions for backward compatibility
# (Functions are already imported from app.schemas.mime_type)
