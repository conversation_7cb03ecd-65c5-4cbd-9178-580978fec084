import logging

from celery.schedules import crontab

logger = logging.getLogger(__name__)


def get_tasks():
    return get_default_tasks()


def get_default_tasks():
    return {
        "scan_scheduled_tasks.scan_aws_resources": {
            "task": "scan_scheduled_tasks.scan_aws_resources",
            "schedule": crontab(minute="*/3"),
        },
        "scheduled_clear_metric_data_tasks.handler": {
            "task": "scheduled_clear_metric_data_tasks.handler",
            "schedule": crontab(minute=0, hour=0),  # Run at midnight (00:00)
        },
        "autonomous_agent_tasks.publisher": {
            "task": "autonomous_agent_tasks.publisher",
            "schedule": crontab(),
        },
        # Add default templates import task to run once on startup
        "app.tasks.template_tasks.import_default_templates_on_startup": {
            "task": "app.tasks.template_tasks.import_default_templates_on_startup",
            "schedule": None,  # No schedule - will run once on startup
            "options": {
                "one_off": True,  # Run only once
            },
        },
    }
