import hashlib
import json
from datetime import datetime
from typing import Any, Optional
from uuid import UUID

from sqlmodel import Session, select, update

from app.models import SeedVersion


class SeedService:
    def __init__(self, session: Session):
        self.session = session

    def calculate_checksum(self, data: Any) -> str:
        """Calculate a checksum for the seed data."""
        # Convert data to a stable string representation
        if isinstance(data, (dict, list)):
            data_str = json.dumps(data, sort_keys=True)
        else:
            data_str = str(data)
        return hashlib.sha256(data_str.encode()).hexdigest()

    def get_latest_version(self, seed_type: str) -> Optional[SeedVersion]:
        """Get the latest version of a seed type, regardless of active status."""
        return self.session.exec(
            select(SeedVersion)
            .where(SeedVersion.seed_type == seed_type)
            .order_by(SeedVersion.applied_at.desc())
        ).first()

    def is_seed_needed(self, seed_type: str, data: Any, version: str) -> bool:
        """
        Check if seed data needs to be applied.
        
        Returns True if:
        1. No version exists for this seed type
        2. The latest version (active or not) has a different checksum
        3. The version string is different from the latest version
        
        This prevents duplicate imports even if previous versions were deactivated.
        """
        latest = self.get_latest_version(seed_type)
        if not latest:
            return True

        current_checksum = self.calculate_checksum(data)
        # Check against the latest version regardless of active status
        return (
            latest.checksum != current_checksum or
            latest.version != version
        )

    def record_seed_version(
        self,
        seed_type: str,
        version: str,
        data: Any,
        description: Optional[str] = None
    ) -> SeedVersion:
        """Record a new seed version."""
        # Deactivate previous versions of this seed type
        self.session.exec(
            update(SeedVersion)
            .where(SeedVersion.seed_type == seed_type)
            .where(SeedVersion.is_active == True)
            .values(is_active=False)
        )

        # Create new version record
        seed_version = SeedVersion(
            seed_type=seed_type,
            version=version,
            checksum=self.calculate_checksum(data),
            description=description,
            applied_at=datetime.now(),
            is_active=True
        )
        self.session.add(seed_version)
        self.session.commit()
        self.session.refresh(seed_version)
        return seed_version 