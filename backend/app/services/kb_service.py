import asyncio
from typing import Any, Callable
from uuid import UUID

from llama_index.core import VectorStoreIndex
from llama_index.core.ingestion import Ingestion<PERSON>ipeline
from llama_index.core.node_parser import SentenceSplitter
from llama_index.core.postprocessor import SimilarityPostprocessor
from llama_index.core.vector_stores.types import (
    MetadataFilter,
    MetadataFilters,
    VectorStoreQueryMode,
)
from llama_index.embeddings.bedrock import BedrockEmbedding
from llama_index.llms.bedrock import Bedrock

# from llama_index.postprocessor.bedrock_rerank import AWSBedrockRerank
from llama_index.vector_stores.qdrant import QdrantVectorStore
from sqlmodel.ext.asyncio.session import AsyncSession

from app.core.config import settings
from app.core.db import async_engine
from app.core.qdrant import aclient, client
from app.logger import logger
from app.models import AsyncTaskStatus
from app.modules.knowledge_base.document_sources import (
    file_reader,
    website_reader,
)
from app.repositories.kb import KBRepository
from app.repositories.object_storage.provider import get_object_storage_repository
from app.services.point_limit_service import PointLimitError, PointLimitService


class KBService:
    _instance = None

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance

    def __init__(self):
        self.aqdrant_client = aclient
        self.qdrant_client = client
        self.embed_model = BedrockEmbedding(
            model_name=settings.EMBEDDING_MODEL_NAME,
            region_name=settings.BEDROCK_REGION_NAME,
        )
        self.node_parser = SentenceSplitter(
            chunk_size=settings.KB_CHUNK_SIZE,
            chunk_overlap=settings.KB_CHUNK_OVERLAP,
            include_metadata=True,
        )

    async def _get_vector_store(self, collection_name: str) -> QdrantVectorStore:
        return QdrantVectorStore(
            collection_name=collection_name,
            client=self.qdrant_client,
            aclient=self.aqdrant_client,
            parallel=10,
            enable_hybrid=True,
            fastembed_sparse_model="Qdrant/bm25",
            dense_vector_name="text-dense",
        )

    async def ingest_from_website(
        self,
        doc_ids: list[str],
        kb_id: str,
        call_back: Callable,
        user_id: UUID | None = None,
        workspace_id: UUID | None = None,
    ) -> dict[str, Any]:
        async with AsyncSession(async_engine) as session:
            kb_repo = KBRepository(session)
            os_repo = get_object_storage_repository()
            docs_to_ingest = await kb_repo.get_documents(UUID(kb_id), doc_ids)

            call_back(
                state=AsyncTaskStatus.IN_PROGRESS,
                meta={
                    "progress": 30,
                    "message": "Reading documents",
                },
            )
            docs, docs_to_ingest, children_docs = await website_reader.read(
                docs_to_ingest,
                call_back,
            )

            # Check point limit before proceeding with ingestion
            if user_id and workspace_id:
                point_service = PointLimitService(session)
                document_texts = [doc.text for doc in docs]
                (
                    can_add,
                    current_points,
                    estimated_new_points,
                    limit,
                ) = await point_service.check_point_limit_for_documents(
                    user_id, workspace_id, document_texts
                )

                if not can_add:
                    # Update documents to failed status
                    for doc_to_ingest in docs_to_ingest:
                        doc_to_ingest.embed_status = AsyncTaskStatus.FAILED
                    await kb_repo.update_documents(docs_to_ingest)

                    raise PointLimitError(current_points, estimated_new_points, limit)

            # Update status of docs_to_ingest and create children docs
            await kb_repo.update_documents(docs_to_ingest)
            await kb_repo.create_documents(children_docs)

            # Upload documents to object storage
            call_back(
                state=AsyncTaskStatus.IN_PROGRESS,
                meta={
                    "progress": 60,
                    "message": "Saving documents",
                },
            )
            for doc in docs:
                await os_repo.upload_bytes(
                    data=doc.text.encode(),
                    object_name=doc.metadata.get("object_name", ""),
                    bucket_name=settings.KB_BUCKET,
                )

            # Ingest documents into vector database
            vector_store = await self._get_vector_store(collection_name=kb_id)

            call_back(
                state=AsyncTaskStatus.IN_PROGRESS,
                meta={
                    "progress": 90,
                    "message": "Embedding documents",
                },
            )
            pipeline = IngestionPipeline(
                transformations=[
                    self.node_parser,
                    self.embed_model,
                ],
                vector_store=vector_store,
            )
            await pipeline.arun(documents=docs)
            return {"status": AsyncTaskStatus.COMPLETED}

    async def ingest_from_files(
        self,
        doc_ids: list[str],
        kb_id: str,
        call_back: Callable,
        user_id: UUID | None = None,
        workspace_id: UUID | None = None,
    ) -> dict[str, Any]:
        call_back(
            state=AsyncTaskStatus.IN_PROGRESS,
            meta={"progress": 30, "message": "Ingesting files"},
        )

        async with AsyncSession(async_engine) as session:
            kb_repo = KBRepository(session)
            docs_to_ingest = await kb_repo.get_documents(UUID(kb_id), doc_ids)
            documents, docs_to_ingest = await file_reader.read(docs_to_ingest)

            # Check point limit before proceeding with ingestion
            if user_id and workspace_id:
                point_service = PointLimitService(session)
                document_texts = [doc.text for doc in documents]
                (
                    can_add,
                    current_points,
                    estimated_new_points,
                    limit,
                ) = await point_service.check_point_limit_for_documents(
                    user_id, workspace_id, document_texts
                )

                if not can_add:
                    # Update documents to failed status
                    for doc_to_ingest in docs_to_ingest:
                        doc_to_ingest.embed_status = AsyncTaskStatus.FAILED
                    await kb_repo.update_documents(docs_to_ingest)

                    raise PointLimitError(current_points, estimated_new_points, limit)

            for doc_to_ingest in docs_to_ingest:
                doc_to_ingest.embed_status = AsyncTaskStatus.COMPLETED

            # Update status of docs_to_ingest
            await kb_repo.update_documents(docs_to_ingest)

            call_back(
                state=AsyncTaskStatus.IN_PROGRESS,
                meta={
                    "progress": 90,
                    "message": "Chunking and storing in vector database",
                },
            )
            vector_store = await self._get_vector_store(collection_name=kb_id)

            pipeline = IngestionPipeline(
                transformations=[
                    self.node_parser,
                    self.embed_model,
                ],
                vector_store=vector_store,
            )
            await pipeline.arun(documents=documents)
            return {"status": AsyncTaskStatus.COMPLETED}

    async def delete_collection(self, collection_name: str) -> bool:
        try:
            if not await self.aqdrant_client.collection_exists(
                collection_name=collection_name
            ):
                logger.warning(f"Collection {collection_name} not found in Qdrant")
                return False
            await self.aqdrant_client.delete_collection(collection_name=collection_name)
            return True
        except Exception as e:
            logger.error(
                f"Error deleting collection {collection_name} from Qdrant: {str(e)}"
            )
            return False

    async def delete_documents_by_document_id(
        self, collection_name: str, document_id: str
    ) -> bool:
        try:
            vector_store = await self._get_vector_store(collection_name)
            nodes_to_delete = await vector_store.aget_nodes(
                filters=MetadataFilters(
                    filters=[
                        MetadataFilter(
                            key="document_id",
                            value=document_id,
                        )
                    ]
                )
            )
            node_ids_to_delete = [node.node_id for node in nodes_to_delete]
            await vector_store.adelete_nodes(node_ids=node_ids_to_delete)
            return True

        except Exception as e:
            logger.error(f"Error deleting documents by document_id: {str(e)}")
            return False

    async def _collections_exist(self, kb_ids: list[str]) -> list[str]:
        return [
            kb_id
            for kb_id in kb_ids
            if await self.aqdrant_client.collection_exists(collection_name=kb_id)
        ]

    def _validate_search_query(self, query: str) -> str:
        """Validate and sanitize search query to prevent injection attacks"""
        if not query or not isinstance(query, str):
            raise ValueError("Search query must be a non-empty string")

        # Strip whitespace
        query = query.strip()

        if not query:
            raise ValueError("Search query cannot be empty")

        # Limit query length to prevent DoS
        max_query_length = 1000
        if len(query) > max_query_length:
            raise ValueError(
                f"Search query too long. Maximum length is {max_query_length} characters"
            )

        # Remove potentially dangerous characters that could be used for injection
        # Allow alphanumeric, spaces, common punctuation, and Unicode letters
        import re

        # This regex allows letters, numbers, spaces, and common punctuation
        if not re.match(
            r'^[\w\s\-.,!?()"\':;@#$%&+=\[\]{}|\\/<>~`]*$', query, re.UNICODE
        ):
            raise ValueError("Search query contains invalid characters")

        # Check for potential script injection patterns
        dangerous_patterns = [
            r"<script[^>]*>",
            r"javascript:",
            r"vbscript:",
            r"onload\s*=",
            r"onerror\s*=",
            r"eval\s*\(",
            r"document\.",
            r"window\.",
        ]

        query_lower = query.lower()
        for pattern in dangerous_patterns:
            if re.search(pattern, query_lower, re.IGNORECASE):
                raise ValueError("Search query contains potentially dangerous content")

        return query

    async def search(
        self,
        query: str,
        kb_ids: list[str],
    ) -> dict:
        # Validate and sanitize the search query
        try:
            query = self._validate_search_query(query)
        except ValueError as e:
            return {"response": f"Invalid search query: {str(e)}"}

        valid_kb_ids = await self._collections_exist(kb_ids)
        if not valid_kb_ids:
            return {"response": "No valid knowledge bases found for search"}

        semaphore = asyncio.Semaphore(4)

        async def search_single_kb(kb_id: str) -> dict:
            async with semaphore:
                try:
                    vector_store = await self._get_vector_store(kb_id)
                    if not vector_store:
                        return {"kb_id": kb_id, "error": "Vector store not found"}

                    index = VectorStoreIndex.from_vector_store(
                        vector_store=vector_store,
                        embed_model=self.embed_model,
                    )

                    similarity_postprocessor = SimilarityPostprocessor(
                        similarity_cutoff=settings.KB_SEARCH_SCORE_THRESHOLD,
                    )

                    query_engine = index.as_query_engine(
                        llm=Bedrock(
                            model=settings.SEARCH_MODEL_LLAMAINDEX,
                            region_name=settings.BEDROCK_REGION_NAME,
                        ),
                        similarity_top_k=settings.KB_SEARCH_LIMIT,
                        sparse_top_k=settings.SPARSE_TOP_K,
                        vector_store_query_mode=VectorStoreQueryMode.HYBRID,
                        node_postprocessors=[similarity_postprocessor],
                    )

                    # Retrieve nodes for this KB
                    nodes = query_engine.retrieve(query)
                    return {"kb_id": kb_id, "nodes": nodes, "success": True}

                except Exception as e:
                    logger.error(f"Error searching KB {kb_id}: {str(e)}")
                    return {"kb_id": kb_id, "error": str(e), "success": False}

        # Execute searches concurrently
        tasks = [search_single_kb(kb_id) for kb_id in valid_kb_ids]
        results = await asyncio.gather(*tasks, return_exceptions=True)

        # Aggregate and rank results
        return await self._aggregate_search_results(results, query)

    async def _aggregate_search_results(self, results: list[dict], query: str) -> dict:
        """Aggregate search results from multiple KBs"""
        all_nodes = []
        successful_kbs = []
        errors = []

        for result in results:
            if isinstance(result, Exception):
                errors.append(f"Search task failed: {str(result)}")
            elif result.get("success"):
                all_nodes.extend(result.get("nodes", []))
                successful_kbs.append(result["kb_id"])
            else:
                errors.append(
                    f"KB {result['kb_id']}: {result.get('error', 'Unknown error')}"
                )

        # Sort nodes by score (highest first) if available
        if all_nodes:
            try:
                all_nodes.sort(key=lambda x: getattr(x, "score", 0), reverse=True)
                # Limit to top results
                all_nodes = all_nodes[: settings.KB_SEARCH_LIMIT]
            except Exception as e:
                logger.warning(f"Error sorting nodes: {str(e)}")

        # Generate response from top nodes
        response_text = ""
        if all_nodes:
            try:
                # Use the first available query engine for synthesis
                if successful_kbs:
                    vector_store = await self._get_vector_store(successful_kbs[0])
                    index = VectorStoreIndex.from_vector_store(
                        vector_store=vector_store,
                        embed_model=self.embed_model,
                    )
                    query_engine = index.as_query_engine(
                        llm=Bedrock(
                            model=settings.SEARCH_MODEL_LLAMAINDEX,
                            region_name=settings.BEDROCK_REGION_NAME,
                        )
                    )
                    response = await query_engine.asynthesize(query, all_nodes)
                    response_text = response.response
            except Exception as e:
                logger.error(f"Error synthesizing response: {str(e)}")
                response_text = "Error generating response from search results"
        else:
            response_text = "No relevant results found"

        return {
            "response": response_text,
            "searched_kbs": len(successful_kbs),
            "total_nodes": len(all_nodes),
            "errors": errors if errors else None,
        }
