"""
Service for managing user point limits across all knowledge bases.
Each document chunk becomes a "point" in the vector database.
Users are limited to 10,000 total points across all their KBs.
"""

from uuid import UUID

from sqlmodel import select
from sqlmodel.ext.asyncio.session import AsyncSession

from app.core.config import settings
from app.core.qdrant import aclient
from app.logger import logger
from app.models import KB


class PointLimitService:
    """Service for managing and validating user point limits"""

    MAX_POINTS_PER_USER = settings.MAX_POINTS_PER_USER

    def __init__(self, session: AsyncSession):
        self.session = session
        self.qdrant_client = aclient

    async def get_user_kb_ids(self, user_id: UUID, workspace_id: UUID) -> list[str]:
        """Get all KB IDs that the user has access to"""
        try:
            query = select(KB.id).where(
                KB.allowed_users.any(user_id),
                KB.workspace_id == workspace_id,
                KB.is_deleted == False,
            )
            result = await self.session.exec(query)
            kb_ids = result.all()
            return [str(kb_id) for kb_id in kb_ids]
        except Exception as e:
            logger.error(f"Error getting user KB IDs: {str(e)}")
            raise

    async def count_points_in_collection(self, collection_name: str) -> int:
        """Count total points in a specific Qdrant collection"""
        try:
            # Check if collection exists
            collections = await self.qdrant_client.get_collections()
            collection_names = [col.name for col in collections.collections]

            if collection_name not in collection_names:
                return 0

            # Count points in the collection
            count_result = await self.qdrant_client.count(
                collection_name=collection_name
            )
            return count_result.count
        except Exception as e:
            logger.error(
                f"Error counting points in collection {collection_name}: {str(e)}"
            )
            # Return 0 if there's an error to avoid blocking operations
            return 0

    async def get_user_total_points(self, user_id: UUID, workspace_id: UUID) -> int:
        """Get total points across all user's KBs"""
        try:
            kb_ids = await self.get_user_kb_ids(user_id, workspace_id)
            total_points = 0

            for kb_id in kb_ids:
                points = await self.count_points_in_collection(kb_id)
                total_points += points
                logger.debug(f"KB {kb_id}: {points} points")

            logger.info(f"User {user_id} total points: {total_points}")
            return total_points
        except Exception as e:
            logger.error(f"Error getting user total points: {str(e)}")
            raise

    async def estimate_document_points(self, documents: list[str]) -> int:
        """
        Estimate how many points will be created from a list of document texts.
        Uses the same chunking logic as the ingestion pipeline.
        """
        from llama_index.core.node_parser import SentenceSplitter

        try:
            # Use the same node parser settings as KBService
            node_parser = SentenceSplitter(
                chunk_size=1024,
                chunk_overlap=256,
            )

            total_chunks = 0
            for doc_text in documents:
                # Create a mock document to get chunk count
                from llama_index.core import Document

                doc = Document(text=doc_text)
                nodes = node_parser.get_nodes_from_documents([doc])
                total_chunks += len(nodes)

            return total_chunks
        except Exception as e:
            logger.error(f"Error estimating document points: {str(e)}")
            # Return a conservative estimate if there's an error
            return len(documents) * 5  # Assume 5 chunks per document on average

    async def check_point_limit_for_documents(
        self, user_id: UUID, workspace_id: UUID, document_texts: list[str]
    ) -> tuple[bool, int, int, int]:
        """
        Check if adding new documents would exceed the user's point limit.

        Returns:
            (can_add, current_points, estimated_new_points, limit)
        """
        try:
            current_points = await self.get_user_total_points(user_id, workspace_id)
            estimated_new_points = await self.estimate_document_points(document_texts)
            total_after_addition = current_points + estimated_new_points

            can_add = total_after_addition <= self.MAX_POINTS_PER_USER

            logger.info(
                f"Point limit check - User: {user_id}, "
                f"Current: {current_points}, "
                f"Estimated new: {estimated_new_points}, "
                f"Total after: {total_after_addition}, "
                f"Limit: {self.MAX_POINTS_PER_USER}, "
                f"Can add: {can_add}"
            )

            return (
                can_add,
                current_points,
                estimated_new_points,
                self.MAX_POINTS_PER_USER,
            )
        except Exception as e:
            logger.error(f"Error checking point limit: {str(e)}")
            # In case of error, allow the operation to proceed to avoid blocking users
            return True, 0, 0, self.MAX_POINTS_PER_USER

    async def get_user_point_usage(self, user_id: UUID, workspace_id: UUID) -> dict:
        """Get detailed point usage information for a user"""
        try:
            kb_ids = await self.get_user_kb_ids(user_id, workspace_id)
            kb_usage = {}
            total_points = 0

            for kb_id in kb_ids:
                points = await self.count_points_in_collection(kb_id)
                kb_usage[kb_id] = points
                total_points += points

            return {
                "user_id": str(user_id),
                "total_points": total_points,
                "max_points": self.MAX_POINTS_PER_USER,
                "remaining_points": max(0, self.MAX_POINTS_PER_USER - total_points),
                "usage_percentage": round(
                    (total_points / self.MAX_POINTS_PER_USER) * 100, 2
                ),
                "kb_usage": kb_usage,
                "is_over_limit": total_points > self.MAX_POINTS_PER_USER,
            }
        except Exception as e:
            logger.error(f"Error getting user point usage: {str(e)}")
            raise


class PointLimitError(Exception):
    """Exception raised when user exceeds point limit"""

    def __init__(self, current_points: int, estimated_new_points: int, limit: int):
        self.current_points = current_points
        self.estimated_new_points = estimated_new_points
        self.limit = limit
        self.total_after = current_points + estimated_new_points

        message = (
            f"Point limit exceeded. "
            f"Current: {current_points}, "
            f"Estimated new: {estimated_new_points}, "
            f"Total would be: {self.total_after}, "
            f"Limit: {limit}"
        )
        super().__init__(message)
