"""
Centralized MIME type and file extension definitions for the application.
This module contains all supported file types and their corresponding MIME types.
"""

import re
from enum import Enum


class FileType(Enum):
    """Enumeration of supported file types"""

    # Documents
    PDF = "pdf"
    DOCX = "docx"
    DOC = "doc"
    TXT = "txt"
    MD = "md"
    HTML = "html"
    RTF = "rtf"
    ODT = "odt"

    # Spreadsheets
    CSV = "csv"
    TSV = "tsv"
    XLS = "xls"
    XLSX = "xlsx"

    # Presentations
    PPT = "ppt"
    PPTX = "pptx"
    PPTM = "pptm"

    # Images
    JPG = "jpg"
    JPEG = "jpeg"
    PNG = "png"
    TIFF = "tiff"

    # Audio/Video
    MP3 = "mp3"
    MP4 = "mp4"

    # Archives and Data
    JSON = "json"
    XML = "xml"
    ZIP = "zip"
    MBOX = "mbox"

    # E-books and Documents
    EPUB = "epub"

    # Email
    EML = "eml"
    MSG = "msg"

    # Notebooks
    IPYNB = "ipynb"

    # Korean Word Processor
    HWP = "hwp"

    # Other formats
    RST = "rst"
    ORG = "org"

    # Unknown/Empty
    UNK = "unk"
    EMPTY = "empty"


# File extension to FileType mapping
EXT_TO_FILETYPE: dict[str, FileType] = {
    # Documents
    ".pdf": FileType.PDF,
    ".docx": FileType.DOCX,
    ".doc": FileType.DOC,
    ".txt": FileType.TXT,
    ".md": FileType.MD,
    ".html": FileType.HTML,
    ".htm": FileType.HTML,
    ".rtf": FileType.RTF,
    ".odt": FileType.ODT,
    # Spreadsheets
    ".csv": FileType.CSV,
    ".tsv": FileType.TSV,
    ".tab": FileType.TSV,
    ".xls": FileType.XLS,
    ".xlsx": FileType.XLSX,
    # Presentations
    ".ppt": FileType.PPT,
    ".pptx": FileType.PPTX,
    ".pptm": FileType.PPTM,
    # Images
    ".jpg": FileType.JPG,
    ".jpeg": FileType.JPEG,
    ".png": FileType.PNG,
    ".tiff": FileType.TIFF,
    # Audio/Video
    ".mp3": FileType.MP3,
    ".mp4": FileType.MP4,
    # Archives and Data
    ".json": FileType.JSON,
    ".xml": FileType.XML,
    ".zip": FileType.ZIP,
    ".mbox": FileType.MBOX,
    # E-books
    ".epub": FileType.EPUB,
    # Email
    ".eml": FileType.EML,
    ".msg": FileType.MSG,
    # Notebooks
    ".ipynb": FileType.IPYNB,
    # Korean Word Processor
    ".hwp": FileType.HWP,
    # Other formats
    ".rst": FileType.RST,
    ".org": FileType.ORG,
    # Fallback
    None: FileType.UNK,
}

# MIME type to FileType mapping
MIME_TO_FILETYPE: dict[str, FileType] = {
    # Documents
    "application/pdf": FileType.PDF,
    "application/vnd.openxmlformats-officedocument.wordprocessingml.document": FileType.DOCX,
    "application/msword": FileType.DOC,
    "text/plain": FileType.TXT,
    "text/markdown": FileType.MD,
    "text/x-markdown": FileType.MD,
    "text/html": FileType.HTML,
    "application/rtf": FileType.RTF,
    "text/rtf": FileType.RTF,
    "application/vnd.oasis.opendocument.text": FileType.ODT,
    # Spreadsheets
    "text/csv": FileType.CSV,
    "application/csv": FileType.CSV,
    "application/x-csv": FileType.CSV,
    "text/comma-separated-values": FileType.CSV,
    "text/x-comma-separated-values": FileType.CSV,
    "text/x-csv": FileType.CSV,
    "text/tsv": FileType.TSV,
    "text/tab-separated-values": FileType.TSV,
    "application/vnd.ms-excel": FileType.XLS,
    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet": FileType.XLSX,
    # Presentations
    "application/vnd.ms-powerpoint": FileType.PPT,
    "application/vnd.openxmlformats-officedocument.presentationml.presentation": FileType.PPTX,
    "application/vnd.ms-powerpoint.presentation.macroEnabled.12": FileType.PPTM,
    # Images
    "image/jpeg": FileType.JPEG,
    "image/jpg": FileType.JPG,
    "image/png": FileType.PNG,
    "image/tiff": FileType.TIFF,
    # Audio/Video
    "audio/mpeg": FileType.MP3,
    "audio/mp3": FileType.MP3,
    "video/mp4": FileType.MP4,
    "application/mp4": FileType.MP4,
    # Archives and Data
    "application/json": FileType.JSON,
    "application/xml": FileType.XML,
    "text/xml": FileType.XML,
    "application/zip": FileType.ZIP,
    "application/mbox": FileType.MBOX,
    # E-books
    "application/epub+zip": FileType.EPUB,
    "application/epub": FileType.EPUB,
    # Email
    "message/rfc822": FileType.EML,
    "application/vnd.ms-outlook": FileType.MSG,
    "application/x-ole-storage": FileType.MSG,
    # Notebooks
    "application/x-ipynb+json": FileType.IPYNB,
    # Korean Word Processor
    "application/x-hwp": FileType.HWP,
    "application/haansofthwp": FileType.HWP,
    # Other formats
    "text/x-rst": FileType.RST,
    "text/org": FileType.ORG,
    # Empty files
    "inode/x-empty": FileType.EMPTY,
}

# Knowledge Base allowed file types (for security)
KB_ALLOWED_FILE_TYPES: set[FileType] = {
    # Documents
    FileType.PDF,
    FileType.DOCX,
    FileType.DOC,
    FileType.TXT,
    FileType.MD,
    FileType.HTML,
    FileType.RTF,
    # Data formats
    FileType.CSV,
    FileType.JSON,
    # Presentations
    FileType.PPT,
    FileType.PPTX,
    FileType.PPTM,
    # Images
    FileType.JPG,
    FileType.JPEG,
    FileType.PNG,
    # Audio/Video
    FileType.MP3,
    FileType.MP4,
    # E-books and notebooks
    FileType.EPUB,
    FileType.IPYNB,
    # Email archives
    FileType.MBOX,
    # Korean documents
    FileType.HWP,
}

# KB allowed MIME types (derived from allowed file types)
KB_ALLOWED_MIME_TYPES: set[str] = {
    mime_type
    for mime_type, file_type in MIME_TO_FILETYPE.items()
    if file_type in KB_ALLOWED_FILE_TYPES
}

# KB allowed extensions (derived from allowed file types)
KB_ALLOWED_EXTENSIONS: set[str] = {
    ext
    for ext, file_type in EXT_TO_FILETYPE.items()
    if file_type in KB_ALLOWED_FILE_TYPES and ext is not None
}

# Maximum file size for KB uploads (10MB)
KB_MAX_FILE_SIZE = 10 * 1024 * 1024


def validate_mime_type_for_kb(mime_type: str) -> bool:
    """Validate if MIME type is allowed for KB uploads"""
    if not mime_type:
        return False
    return mime_type.lower().strip() in KB_ALLOWED_MIME_TYPES


def validate_file_extension_for_kb(filename: str) -> bool:
    """Validate if file extension is allowed for KB uploads"""
    if not filename:
        return False

    import os

    _, ext = os.path.splitext(filename.lower())
    return ext in KB_ALLOWED_EXTENSIONS


def get_file_type_from_mime(mime_type: str) -> FileType:
    """Get FileType from MIME type with validation"""
    if not mime_type or not isinstance(mime_type, str):
        raise ValueError("MIME type must be a non-empty string")

    # Normalize MIME type (lowercase, strip whitespace)
    mime_type = mime_type.lower().strip()

    # Validate MIME type format
    if not re.match(
        r"^[a-z0-9][a-z0-9!#$&\-\^_]*\/[a-z0-9][a-z0-9!#$&\-\^_.]*$", mime_type
    ):
        raise ValueError(f"Invalid MIME type format: {mime_type}")

    if mime_type in MIME_TO_FILETYPE:
        return MIME_TO_FILETYPE[mime_type]

    raise ValueError(f"Unsupported MIME type: {mime_type}")


def get_file_type_from_extension(filename: str) -> FileType:
    """Get FileType from file extension"""
    if not filename:
        return FileType.UNK

    import os

    _, ext = os.path.splitext(filename.lower())
    return EXT_TO_FILETYPE.get(ext, FileType.UNK)


def sanitize_filename(filename: str) -> str:
    """Sanitize filename to prevent security issues"""
    if not filename:
        raise ValueError("Filename cannot be empty")

    # Remove dangerous characters
    dangerous_chars = ["<", ">", ":", '"', "|", "?", "*", "\0", "/", "\\"]
    sanitized = filename
    for char in dangerous_chars:
        sanitized = sanitized.replace(char, "_")

    # Remove leading/trailing whitespace and dots
    sanitized = sanitized.strip(". ")

    # Ensure filename is not empty after sanitization
    if not sanitized:
        raise ValueError("Filename becomes empty after sanitization")

    # Limit filename length
    if len(sanitized) > 255:
        name, ext = sanitized.rsplit(".", 1) if "." in sanitized else (sanitized, "")
        max_name_length = 255 - len(ext) - 1 if ext else 255
        sanitized = name[:max_name_length] + ("." + ext if ext else "")

    return sanitized


def get_supported_extensions_list() -> list[str]:
    """Get a list of all supported file extensions"""
    return [ext for ext in EXT_TO_FILETYPE.keys() if ext is not None]


def get_kb_supported_extensions_list() -> list[str]:
    """Get a list of KB-supported file extensions"""
    return list(KB_ALLOWED_EXTENSIONS)


def get_kb_supported_mime_types_list() -> list[str]:
    """Get a list of KB-supported MIME types"""
    return list(KB_ALLOWED_MIME_TYPES)
