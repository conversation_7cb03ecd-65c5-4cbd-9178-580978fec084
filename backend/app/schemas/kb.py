from typing import Any

from pydantic import BaseModel, Field, field_validator

from app.models import AsyncTaskStatus
from app.schemas.mime_type import (
    KB_ALLOWED_EXTENSIONS,
    KB_ALLOWED_MIME_TYPES,
    KB_MAX_FILE_SIZE,
    validate_file_extension_for_kb,
    validate_mime_type_for_kb,
)


class FileInfo(BaseModel):
    """Information about a file to generate a presigned URL for"""

    file_id: str = Field(..., description="Client-side ID for tracking this file")
    filename: str = Field(
        ..., description="Original filename", min_length=1, max_length=255
    )
    content_type: str = Field(..., description="File MIME type")
    file_size: int = Field(
        ..., description="File size in bytes", gt=0, le=KB_MAX_FILE_SIZE
    )

    @field_validator("filename")
    @classmethod
    def validate_filename(cls, v: str) -> str:
        """Validate filename and extension"""
        if not v or v.strip() != v:
            raise ValueError(
                "Filename cannot be empty or contain leading/trailing whitespace"
            )

        # Use centralized validation
        if not validate_file_extension_for_kb(v):
            allowed_exts = ", ".join(sorted(KB_ALLOWED_EXTENSIONS))
            raise ValueError(
                f"File extension not allowed. Allowed extensions: {allowed_exts}"
            )

        # Additional security check for dangerous characters
        dangerous_chars = ["<", ">", ":", '"', "|", "?", "*", "\0"]
        if any(char in v for char in dangerous_chars):
            raise ValueError("Filename contains invalid characters")

        return v

    @field_validator("content_type")
    @classmethod
    def validate_content_type(cls, v: str) -> str:
        """Validate MIME type"""
        if not validate_mime_type_for_kb(v):
            allowed_types = ", ".join(sorted(KB_ALLOWED_MIME_TYPES))
            raise ValueError(
                f"MIME type '{v}' not allowed. Allowed types: {allowed_types}"
            )
        return v

    @field_validator("file_size")
    @classmethod
    def validate_file_size(cls, v: int) -> int:
        """Validate file size"""
        if v <= 0:
            raise ValueError("File size must be greater than 0")
        if v > KB_MAX_FILE_SIZE:
            raise ValueError(
                f"File size {v} bytes exceeds maximum allowed size of {KB_MAX_FILE_SIZE} bytes (10MB)"
            )
        return v


class PresignedUrlRequest(BaseModel):
    """Request to generate presigned URLs for file uploads"""

    kb_id: str = Field(..., description="ID of the knowledge base to upload files to")
    files: list[FileInfo] = Field(
        ...,
        description="Information about files to generate presigned URLs for",
    )


class PresignedUrlInfo(BaseModel):
    """Information about a generated presigned URL"""

    file_id: str = Field(..., description="Client-side ID for tracking this file")
    filename: str = Field(..., description="Original filename")
    storage_key: str = Field(..., description="Storage key for the file")
    presigned_url: str = Field(..., description="Presigned URL for file upload")


class PresignedUrlResponse(BaseModel):
    """Response with presigned URLs for file uploads"""

    kb_id: str = Field(..., description="Knowledge base ID")
    presigned_urls: list[PresignedUrlInfo] = Field(
        ..., description="Generated presigned URLs"
    )


class UploadedFileInfo(BaseModel):
    """Information about a successfully uploaded file"""

    file_id: str = Field(..., description="Client-side ID for tracking this file")
    filename: str = Field(..., description="Original filename")
    storage_key: str = Field(..., description="Storage key for the uploaded file")
    content_type: str | None = Field(None, description="File MIME type")
    file_size: int | None = Field(None, description="File size in bytes")


class ConfirmUploadsRequest(BaseModel):
    """Request to confirm file uploads and start ingestion"""

    uploaded_files: list[UploadedFileInfo] = Field(
        ..., description="Information about successfully uploaded files"
    )


class FilesInfo(BaseModel):
    object_name: str
    name: str
    type: str


class URLsUploadRequest(BaseModel):
    # For website source
    urls: list[str] | None = Field(
        None, description="URLs to crawl (required if source_type is website)"
    )
    deep_crawls: list[bool] | None = Field(
        None, description="Whether to enable deep crawling for each URL"
    )


class TaskStatusResponse(BaseModel):
    """Response schema for task status operations"""

    task_id: str = Field(..., description="Celery task ID")
    status: AsyncTaskStatus = Field(
        ..., description="Task status (PENDING, PROGRESS, SUCCESS, FAILURE)"
    )
    progress: int = Field(0, description="Progress percentage (0-100)")
    result: dict[str, Any] | None = Field(None, description="Task result if completed")
    error: str | None = Field(None, description="Error message if failed")
    status_message: str | None = Field(
        None, description="Human-readable status message"
    )
