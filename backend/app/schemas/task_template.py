from uuid import UUID
from datetime import datetime

from pydantic import BaseModel, Field

from app.models import RunMode<PERSON><PERSON>, TaskCategoryEnum, TaskServiceEnum, TaskCouldEnum


class TaskTemplateCreate(BaseModel):
    task: str
    category: TaskCategoryEnum | None = Field(default=TaskCategoryEnum.OTHER)
    service: TaskServiceEnum | None = Field(default=TaskServiceEnum.OTHER)
    service_name: str | None = Field(default="")
    cloud: TaskCouldEnum
    run_mode: RunModeEnum
    schedule: str | None = None
    context: str


class TaskTemplateUpdate(BaseModel):
    task: str | None = None
    category: TaskCategoryEnum | None = None
    service: TaskServiceEnum | None = None
    run_mode: RunModeEnum | None = None
    schedule: str | None = None
    context: str | None = None


class TaskTemplateResponse(BaseModel):
    id: UUID
    task: str
    category: TaskCategoryEnum
    service: TaskServiceEnum
    service_name: str
    cloud: TaskCouldEnum
    run_mode: RunModeEnum
    schedule: str | None
    context: str
    is_default: bool
    created_at: datetime
    updated_at: datetime | None

    class Config:
        from_attributes = True


class TaskTemplateList(BaseModel):
    data: list[TaskTemplateResponse]
    total: int
