import secrets
import warnings
from typing import Annotated, Any, Literal

from pydantic import (
    AnyUrl,
    BeforeValidator,
    HttpUrl,
    PostgresDsn,
    RedisDsn,
    computed_field,
    model_validator,
    validator,
)
from pydantic_core import MultiHostUrl
from pydantic_settings import BaseSettings, SettingsConfigDict
from typing_extensions import Self


def parse_cors(v: Any) -> list[str] | str:
    if isinstance(v, str) and not v.startswith("["):
        return [i.strip() for i in v.split(",")]
    elif isinstance(v, list | str):
        return v
    raise ValueError(v)


class Settings(BaseSettings):
    model_config = SettingsConfigDict(
        # Use top level .env file (one level above ./backend/)
        env_file="../.env",
        env_ignore_empty=True,
        extra="ignore",
    )
    API_V1_STR: str = "/api/v1"
    APP_API_KEY: str | None = None
    SECRET_KEY: str = secrets.token_urlsafe(32)
    # 60 minutes * 24 hours * 8 days = 8 days
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 60 * 24 * 8
    FRONTEND_HOST: str = "http://localhost:5173"
    PAYMENT_ENABLED: bool | None = False
    PAYMENT_HOST: str | None = None
    ENVIRONMENT: Literal["local", "dev", "staging", "production"] = "local"

    # Object Storage Configuration
    OBJECT_STORAGE_OPTION: Literal["s3", "minio"] = "minio"
    IMAGES_BUCKET: str = "cloudthinker-images-bucket-environment-dev"
    KB_BUCKET: str = "cloudthinker-kb-bucket-environment-dev"

    # S3 Configuration
    S3_ACCESS_KEY: str | None = None
    S3_SECRET_KEY: str | None = None
    S3_REGION: str | None = None

    # MinIO Configuration
    MINIO_ENDPOINT: str = "minio:9000"
    MINIO_ACCESS_KEY: str = "minioadmin"
    MINIO_SECRET_KEY: str = "minioadmin"
    MINIO_SECURE: bool = False

    @computed_field  # type: ignore[prop-decorator]
    @property
    def storage_is_s3(self) -> bool:
        return self.OBJECT_STORAGE_OPTION == "s3"

    @computed_field  # type: ignore[prop-decorator]
    @property
    def storage_is_minio(self) -> bool:
        return self.OBJECT_STORAGE_OPTION == "minio"

    @computed_field  # type: ignore[prop-decorator]
    @property
    def current_storage_access_key(self) -> str:
        if self.storage_is_s3:
            return self.S3_ACCESS_KEY or ""
        return self.MINIO_ACCESS_KEY

    @computed_field  # type: ignore[prop-decorator]
    @property
    def current_storage_secret_key(self) -> str:
        if self.storage_is_s3:
            return self.S3_SECRET_KEY or ""
        return self.MINIO_SECRET_KEY

    @computed_field  # type: ignore[prop-decorator]
    @property
    def current_storage_bucket_name(self) -> str:
        if self.storage_is_s3:
            return self.S3_BUCKET_NAME or ""
        return self.MINIO_BUCKET_NAME

    BACKEND_CORS_ORIGINS: Annotated[
        list[AnyUrl] | str, BeforeValidator(parse_cors)
    ] = []

    @computed_field  # type: ignore[prop-decorator]
    @property
    def all_cors_origins(self) -> list[str]:
        return [str(origin).rstrip("/") for origin in self.BACKEND_CORS_ORIGINS] + [
            self.FRONTEND_HOST
        ]

    PROJECT_NAME: str
    SENTRY_DSN: HttpUrl | None = None
    POSTGRES_SERVER: str
    POSTGRES_PORT: int = 5432
    POSTGRES_USER: str
    POSTGRES_PASSWORD: str = ""
    POSTGRES_DB: str = ""
    LANGGRAPH_SCHEMA_NAME: str = "langgraph"

    # QDRANT Configuration
    QDRANT_HOST: str = "qdrant"
    QDRANT_PORT: int = 6333
    QDRANT_GRPC_PORT: int = 6334

    RECAPTCHA_V3_SECRET_KEY: str | None = None

    @computed_field  # type: ignore[prop-decorator]
    @property
    def SQLALCHEMY_DATABASE_URI(self) -> PostgresDsn:
        return MultiHostUrl.build(
            scheme="postgresql+psycopg",
            username=self.POSTGRES_USER,
            password=self.POSTGRES_PASSWORD,
            host=self.POSTGRES_SERVER,
            port=self.POSTGRES_PORT,
            path=self.POSTGRES_DB,
        )

    @computed_field  # type: ignore[prop-decorator]
    @property
    def KB_VECTOR_STORE_CONNECTION(self) -> PostgresDsn:
        return MultiHostUrl.build(
            scheme="postgresql+psycopg",
            username=self.POSTGRES_USER,
            password=self.POSTGRES_PASSWORD,
            host=self.POSTGRES_SERVER,
            port=self.POSTGRES_PORT,
            path=f"{self.POSTGRES_DB}?options=-c search_path={self.LANGGRAPH_SCHEMA_NAME}",
        )

    REDIS_SERVER: str
    REDIS_PORT: int = 6379
    REDIS_CELERY_DB: int = 0

    @computed_field  # type: ignore[prop-decorator]
    @property
    def REDIS_DATABASE_URI(self) -> RedisDsn:
        return MultiHostUrl.build(
            scheme="redis", host=self.REDIS_SERVER, port=self.REDIS_PORT
        )

    SMTP_TLS: bool = True
    SMTP_SSL: bool = False
    SMTP_PORT: int = 587
    SMTP_HOST: str | None = None
    SMTP_USER: str | None = None
    SMTP_PASSWORD: str | None = None
    # TODO: update type to EmailStr when sqlmodel supports it
    EMAILS_FROM_EMAIL: str | None = None
    EMAILS_FROM_NAME: str | None = None
    # Email address for the growth team to receive enterprise enquiry notifications
    GROWTH_TEAM_EMAIL: str | None = None

    @model_validator(mode="after")
    def _set_default_emails_from(self) -> Self:
        if not self.EMAILS_FROM_NAME:
            self.EMAILS_FROM_NAME = self.PROJECT_NAME
        return self

    EMAIL_RESET_TOKEN_EXPIRE_HOURS: int = 48

    @computed_field  # type: ignore[prop-decorator]
    @property
    def emails_enabled(self) -> bool:
        return bool(self.SMTP_HOST and self.EMAILS_FROM_EMAIL)

    # TODO: update type to EmailStr when sqlmodel supports it
    EMAIL_TEST_USER: str = "<EMAIL>"
    # TODO: update type to EmailStr when sqlmodel supports it
    FIRST_SUPERUSER: str
    FIRST_SUPERUSER_PASSWORD: str

    DIFY_API: str | None = None
    DIFY_API_KEY: str | None = None
    DIFY_USER_ID: str | None = None
    DIFY_APP_ANALYZE_API_KEY: str | None = None
    DIFY_APP_ANALYZE_APP_ID: str | None = None
    DIFY_APP_CLARIFY_API_KEY: str | None = None
    DIFY_APP_CLARIFY_APP_ID: str | None = None
    DIFY_APP_EXECUTE_API_KEY: str | None = None
    DIFY_APP_EXECUTE_APP_ID: str | None = None
    DIFY_APP_AGENT_ID: str | None = None

    EXECUTOR_HOST: str
    SCHEDULE_ENABLED: bool
    BEDROCK_REGION_NAME: str = "us-east-1"

    GOOGLE_CLIENT_ID: str | None = None
    GOOGLE_CLIENT_SECRET: str | None = None
    GOOGLE_LOGIN_CALLBACK: str | None = FRONTEND_HOST + "/google-callback"

    PERPLEXITY_API_KEY: str | None = None
    PERPLEXITY_MODEL: str = "sonar"  # or "sonar-pro"

    LANGFUSE_SECRET_KEY: str | None = None
    LANGFUSE_PUBLIC_KEY: str | None = None
    LANGFUSE_HOST: str | None = None

    def _check_default_secret(self, var_name: str, value: str | None) -> None:
        if value == "changethis":
            message = (
                f'The value of {var_name} is "changethis", '
                "for security, please change it, at least for deployments."
            )
            if self.ENVIRONMENT == "local":
                warnings.warn(message, stacklevel=1)
            else:
                raise ValueError(message)

    @model_validator(mode="after")
    def _enforce_non_default_secrets(self) -> Self:
        self._check_default_secret("SECRET_KEY", self.SECRET_KEY)
        self._check_default_secret("POSTGRES_PASSWORD", self.POSTGRES_PASSWORD)
        self._check_default_secret(
            "FIRST_SUPERUSER_PASSWORD", self.FIRST_SUPERUSER_PASSWORD
        )

        return self

    OLLAMA_BASE_URL: str | None = "https://ollama-server.builder-studio.com"
    DEFAULT_TOKEN_QUOTA_LIMIT: int | None = 1000000
    DEFAULT_USER_QUOTA_LIMIT: int | None = 20
    MAX_POINTS_PER_USER: int = 100000

    LANGGRAPH_API: str | None = "http://langgraph-api:8000"

    NETWORKING_MODEL: str | None = (
        "bedrock/us.anthropic.claude-3-5-sonnet-20241022-v2:0"
    )
    TITLE_GENERATION_MODEL: str | None = (
        "bedrock/us.anthropic.claude-3-5-haiku-20241022-v1:0"
    )

    KEY_LEARNING_EXTRACTION_MODEL: str | None = (
        "bedrock/us.anthropic.claude-3-5-haiku-20241022-v1:0"
    )

    KEY_LEARNING_DECISION_MODEL: str | None = (
        "bedrock/us.anthropic.claude-3-5-haiku-20241022-v1:0"
    )
    SEARCH_MODEL: str | None = "bedrock/us.anthropic.claude-3-5-haiku-20241022-v1:0"
    SEARCH_MODEL_LLAMAINDEX: str | None = "us.anthropic.claude-3-5-haiku-20241022-v1:0"

    EMBEDDING_MODEL_DIMS: int = 1024
    EMBEDDING_MODEL: str = "bedrock:cohere.embed-multilingual-v3"
    EMBEDDING_MODEL_NAME: str = "cohere.embed-multilingual-v3"

    MEMORY_MAX_RELATED_MEMORY_NODES: int = 5
    MEMORY_RELATEDNESS_THRESHOLD: float = 0.6

    MAX_KEY_LEARNING_TOOL_RESULTS: int = 10
    KB_CHUNK_SIZE: int = 1024
    KB_CHUNK_OVERLAP: int = 256
    KB_SEARCH_LIMIT: int = 5
    KB_SEARCH_SCORE_THRESHOLD: float = 0.5
    SPARSE_TOP_K: int = 5

    # Knowledge Base performance optimization
    LLAMA_PARSE_MODE: str = "parse_page_without_llm"
    KB_BATCH_SIZE: int = 50  # Batch size for document indexing
    KB_MAX_RETRIES: int = 5  # Maximum retries for failed indexing operations
    KB_MAX_BACKOFF: int = 30  # Maximum backoff time in seconds
    KB_CONNECTION_POOL_SIZE: int = 10  # Database connection pool size for KB operations
    KB_ASYNC_TIMEOUT: int = 60  # Timeout for async operations in seconds

    # Cohere Reranking
    KB_RERANK_MODEL_ID: str = "cohere.rerank-v3-5:0"
    KB_RERANK_REGION_NAME: str = "us-east-1"
    KB_RERANK_TOP_N: int = 5

    # Resource management settings
    KB_CLEANUP_INTERVAL: int = 3600  # Interval for cleanup operations in seconds

    # Crawling config
    MAX_DEPTH: int = 2
    MAX_PAGES: int = 20

    # MCP config
    MCP_API_KEY: str | None = None
    DEFAULT_MCP_TIMEOUT: int = 5  # seconds
    DEFAULT_MCP_SSE_READ_TIMEOUT: int = 60 * 5  # seconds
    DEFAULT_MCP_ENCODING: str = "utf-8"
    DEFAULT_MCP_ENCODING_ERROR_HANDLER: str = "strict"

    # Tool Response Limit
    TOOL_RESPONSE_LIMIT_TOKENS: int = 2500
    CHARACTER_PER_TOKEN: int = 4
    TOOL_RESPONSE_LIMIT_CHARACTERS: int = (
        TOOL_RESPONSE_LIMIT_TOKENS * CHARACTER_PER_TOKEN
    )

    # Networking Agent
    NETWORKING_AGENT_RETRY_COUNT: int = 3

    # Scheduler
    TASK_SCHEDULE_WINDOW_MINUTES: int = 2

    @validator("PAYMENT_HOST")
    def validate_payment_config(cls, v, values):
        if values.get("PAYMENT_ENABLED") and not v:
            raise ValueError("Payment configuration missing when PAYMENT_ENABLED=True")
        return v

    DISCORD_INVITE_LINK: str | None = None


settings = Settings()  # type: ignore
