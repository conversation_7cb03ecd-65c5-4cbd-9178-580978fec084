from uuid import UUID

from sqlalchemy import func
from sqlmodel import select
from sqlmodel.ext.asyncio.session import AsyncSession

from app.api.deps import AuthorizedUser
from app.logger import logger
from app.models import (
    KB,
    DocumentKB,
    DocumentKBCreate,
    DocumentKBRead,
    DocumentsKBRead,
    KBCreate,
    KBsRead,
    KBUpdate,
    KBUsageMode,
    User,
    UserWorkspace,
)

from .base import Result


class KBRepository:
    def __init__(self, session: AsyncSession) -> None:
        self.session = session

    async def create(
        self,
        kb_create: KBCreate,
        user_id: UUID,
        workspace_id: UUID,
    ) -> Result[KB]:
        try:
            kb_data = kb_create.model_dump()
            kb_data["owner_id"] = user_id
            kb_data["workspace_id"] = workspace_id

            # Ensure the owner is always in allowed_users
            provided_allowed_users = kb_data.get("allowed_users", [])
            if user_id not in provided_allowed_users:
                provided_allowed_users.append(user_id)
            kb_data["allowed_users"] = provided_allowed_users

            kb = KB(**kb_data)
            self.session.add(kb)
            await self.session.commit()
            await self.session.refresh(kb)
            return Result.Ok(kb)
        except Exception as e:
            await self.session.rollback()
            return Result.Fail(str(e))

    async def get(self, kb_id: UUID, user_id: UUID, workspace_id: UUID) -> Result[KB]:
        """
        Get a knowledge base by ID.
        The user must be in the allowed users list or be the owner of the knowledge base.
        """
        try:
            query = select(KB).where(
                KB.id == kb_id,
                KB.is_deleted == False,
                KB.workspace_id == workspace_id,
                KB.allowed_users.any(user_id),
            )
            result = await self.session.exec(query)
            kb = result.one_or_none()
            if not kb:
                return Result.Fail("Knowledge base not found")
            return Result.Ok(kb)
        except Exception as e:
            await self.session.rollback()
            return Result.Fail(str(e))

    async def get_kbs(
        self,
        workspace_id: UUID,
        user_id: UUID,
        skip: int = 0,
        limit: int = 50,
        mode: KBUsageMode | None = None,
    ) -> Result[KBsRead]:
        try:
            query = select(KB).where(
                # The owner is the first user to be added to the allowed users
                KB.allowed_users.any(user_id),
                KB.workspace_id == workspace_id,
                KB.is_deleted == False,
            )
            count_query = select(func.count()).select_from(query.subquery())
            query = query.offset(skip).limit(limit)

            if mode:
                query = query.where(KB.usage_mode == mode)

            result = await self.session.exec(query)
            result = result.all()

            count = await self.session.exec(count_query)
            count = count.one()

            return Result.Ok(KBsRead(data=result, count=count))
        except Exception as e:
            logger.error(f"Error getting knowledge bases: {str(e)}", exc_info=True)
            await self.session.rollback()
            return Result.Fail(str(e))

    async def update(
        self,
        kb_id: UUID,
        kb_update: KBUpdate,
        user_id: UUID,
        workspace_id: UUID,
    ) -> Result[KB]:
        """
        Update a knowledge base.
        The user must be the owner of the knowledge base.
        """
        try:
            kb_result = await self.get(kb_id, user_id, workspace_id)
            if kb_result.failure:
                return kb_result
            kb = kb_result.value
            if kb.owner_id != user_id:
                return Result.Fail("You are not the owner of this knowledge base")

            # Validate that the user is not removing themselves from allowed_users
            update_data = kb_update.model_dump()
            if (
                "allowed_users" in update_data
                and update_data["allowed_users"] is not None
            ):
                new_allowed_users = update_data["allowed_users"]
                # Check if the current user (owner) is trying to remove themselves
                if user_id not in new_allowed_users:
                    return Result.Fail(
                        "You cannot remove yourself from the allowed users list"
                    )

            for key, value in update_data.items():
                if value is not None:
                    setattr(kb, key, value)

            await self.session.commit()
            await self.session.refresh(kb)
            return Result.Ok(kb)
        except Exception as e:
            await self.session.rollback()
            return Result.Fail(str(e))

    async def delete(self, kb_id: UUID, user: AuthorizedUser) -> Result[bool]:
        """
        Delete a knowledge base.
        The user must be the owner of the knowledge base.
        """
        try:
            kb_result = await self.get(kb_id, user.id, user.current_workspace_id)
            if kb_result.failure:
                return kb_result
            kb = kb_result.value
            if kb.owner_id != user.id:
                return Result.Fail("You are not the owner of this knowledge base")

            kb.is_deleted = True
            await self.session.commit()
            return Result.Ok(True)
        except Exception as e:
            await self.session.rollback()
            logger.error(
                f"Error deleting knowledge base: {str(e)}",
                exc_info=True,
            )
            return Result.Fail(str(e))

    async def get_all_users_in_workspace(
        self, workspace_id: UUID
    ) -> Result[list[User]]:
        """
        Get all users in a workspace.
        The user must be in the workspace.
        """
        try:
            query = (
                select(User)
                .join(UserWorkspace)
                .where(
                    UserWorkspace.workspace_id == workspace_id,
                    User.is_active is True,
                )
            )
            result = await self.session.exec(query)
            return Result.Ok(result.all())
        except Exception as e:
            await self.session.rollback()
            return Result.Fail(str(e))

    async def create_documents(
        self, document_creates: list[DocumentKBCreate]
    ) -> list[DocumentKB]:
        try:
            documents = [
                DocumentKB.model_validate(document_create)
                for document_create in document_creates
            ]
            self.session.add_all(documents)
            await self.session.commit()
            for document in documents:
                await self.session.refresh(document)
            return documents
        except:
            await self.session.rollback()
            raise

    async def get_documents(self, kb_id: UUID, doc_ids: list[str]) -> list[DocumentKB]:
        try:
            query = select(DocumentKB).where(
                DocumentKB.kb_id == kb_id,
                DocumentKB.id.in_(doc_ids),  # type: ignore
            )
            result = await self.session.exec(query)
            return result.all()  # type: ignore
        except:
            await self.session.rollback()
            raise

    async def update_documents(self, documents: list[DocumentKB]) -> list[DocumentKB]:
        try:
            for document in documents:
                self.session.add(document)
            await self.session.commit()
            for document in documents:
                await self.session.refresh(document)
            return documents
        except:
            await self.session.rollback()
            raise

    async def list_documents(
        self, kb_id: UUID, skip: int = 0, limit: int = 50, search: str | None = None
    ) -> DocumentsKBRead:
        try:
            # Flat pagination: treat all documents equally regardless of hierarchy
            base_query = (
                select(DocumentKB)
                .where(
                    DocumentKB.kb_id == kb_id,
                    DocumentKB.is_deleted == False,
                )
                .order_by(DocumentKB.created_at.desc())  # Consistent ordering
            )

            # Create count query before applying search filter
            count_query = select(func.count()).select_from(base_query.subquery())

            if search:
                search_condition = DocumentKB.name.ilike(f"%{search}%")
                base_query = base_query.where(search_condition)
                # Update count query to include search filter
                count_query = select(func.count()).select_from(base_query.subquery())

            # Apply pagination to all documents
            paginated_query = base_query.offset(skip).limit(limit)

            result = await self.session.exec(paginated_query)
            documents = result.all()

            count_result = await self.session.exec(count_query)
            count = count_result.one()

            # Convert to DocumentKBRead and set children to empty list for flat pagination
            documents_read = []
            for doc in documents:
                doc_dict = doc.model_dump()
                doc_dict[
                    "children"
                ] = []  # Set children to empty list for flat pagination
                documents_read.append(DocumentKBRead(**doc_dict))

            # Debug logging
            logger.info(
                f"KB {kb_id} pagination: skip={skip}, limit={limit}, returned={len(documents_read)}, total_count={count}"
            )

            return DocumentsKBRead(data=documents_read, count=count)
        except:
            await self.session.rollback()
            raise

    async def delete_document(self, document_id: str) -> None:
        try:
            statement = select(DocumentKB).where(DocumentKB.id == document_id)
            result = await self.session.exec(statement)
            document = result.one_or_none()
            if document:
                await self.session.delete(document)
                await self.session.commit()
        except:
            await self.session.rollback()
            raise
