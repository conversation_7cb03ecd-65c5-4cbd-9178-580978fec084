import asyncio
from functools import wraps
from typing import Any

import celery

from app.api.deps import get_async_session
from app.core.celery_app import HIGH_PRIORITY, celery_app
from app.core.config import settings
from app.logger import logger
from app.models import AsyncTaskStatus
from app.repositories.kb import KBRepository
from app.repositories.object_storage.provider import get_object_storage_repository
from app.services.kb_service import KBService


def async_to_sync(f):
    """Decorator to convert async functions to sync for Celery tasks."""

    @wraps(f)
    def wrapper(*args, **kwargs):
        try:
            loop = asyncio.get_event_loop()
        except RuntimeError:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
        return loop.run_until_complete(f(*args, **kwargs))

    return wrapper


@celery_app.task(
    name="kb_tasks.ingest_from_website",
    bind=True,
    priority=HIGH_PRIORITY,
    autoretry_for=(Exception,),
    retry_kwargs={"max_retries": 3, "countdown": 60},
    retry_backoff=True,
    retry_backoff_max=300,
    retry_jitter=True,
)
def ingest_from_website_task(
    self: celery.Task,
    doc_ids: list[str],
    kb_id: str,
    user_id: str | None = None,
    workspace_id: str | None = None,
) -> dict[str, Any]:
    self.update_state(
        state="PROGRESS",
        meta={
            "progress": 0,
            "message": "Ingesting website content",
            "source_type": "website",
        },
    )

    try:
        kb_service: KBService = KBService()

        @async_to_sync
        async def run_ingestion(
            doc_ids: list[str],
            kb_id: str,
        ):
            from uuid import UUID

            return await kb_service.ingest_from_website(
                doc_ids=doc_ids,
                kb_id=kb_id,
                call_back=self.update_state,
                user_id=UUID(user_id) if user_id else None,
                workspace_id=UUID(workspace_id) if workspace_id else None,
            )

        result = run_ingestion(doc_ids, kb_id)

        return {
            "task_id": self.request.id,
            "status": AsyncTaskStatus.COMPLETED,
            "result": result,
        }

    except Exception as e:
        from app.services.point_limit_service import PointLimitError

        error_message = str(e)
        if isinstance(e, PointLimitError):
            error_message = f"Point limit exceeded: {error_message}"

        logger.error(f"Error in ingest_from_website_task: {error_message}")
        self.update_state(
            state="FAILURE",
            meta={
                "progress": 0,
                "status": AsyncTaskStatus.FAILED,
                "error": error_message,
            },
        )
        raise


@celery_app.task(name="kb_tasks.batch_ingest_files", bind=True, priority=HIGH_PRIORITY)
def ingest_from_files_task(
    self: celery.Task,
    doc_ids: list[str],
    kb_id: str,
    user_id: str | None = None,
    workspace_id: str | None = None,
) -> dict[str, Any]:
    self.update_state(
        state="PROGRESS",
        meta={
            "progress": 0,
            "message": "Starting ingestion",
        },
    )

    try:
        kb_service = KBService()

        @async_to_sync
        async def run_ingestion(doc_ids: list[str], kb_id: str):
            from uuid import UUID

            return await kb_service.ingest_from_files(
                doc_ids=doc_ids,
                call_back=self.update_state,
                kb_id=kb_id,
                user_id=UUID(user_id) if user_id else None,
                workspace_id=UUID(workspace_id) if workspace_id else None,
            )

        result = run_ingestion(doc_ids, kb_id)

        return {
            "task_id": self.request.id,
            "status": AsyncTaskStatus.COMPLETED,
            "result": result,
        }

    except Exception as e:
        from app.services.point_limit_service import PointLimitError

        error_message = str(e)
        if isinstance(e, PointLimitError):
            error_message = f"Point limit exceeded: {error_message}"

        logger.error(f"Error in ingest_from_files_task: {error_message}")
        self.update_state(
            state="FAILURE",
            meta={
                "progress": 0,
                "status": AsyncTaskStatus.FAILED,
                "error": error_message,
            },
        )
        raise


@celery_app.task(
    name="kb_tasks.delete_document",
    bind=True,
    priority=HIGH_PRIORITY,
    autoretry_for=(Exception,),
    retry_kwargs={"max_retries": 3, "countdown": 60},
    retry_backoff=True,
    retry_backoff_max=300,
    retry_jitter=True,
)
def delete_document_task(
    self: celery.Task,
    kb_id: str,
    document_id: str,
    object_name: str,
) -> dict[str, Any]:
    try:

        @async_to_sync
        async def run_deletion():
            # 1. Delete the document from the object storage
            os_repo = get_object_storage_repository()
            await os_repo.delete_file(
                object_name=object_name, bucket_name=settings.KB_BUCKET
            )

            # 2. Delete the document from the vector store
            kb_service = KBService()
            await kb_service.delete_documents_by_document_id(
                collection_name=kb_id,
                document_id=document_id,
            )

            # 3. Delete the document from the database
            async for session in get_async_session():
                kb_repo = KBRepository(session)
                await kb_repo.delete_document(document_id)

        result = run_deletion()

        return {
            "task_id": self.request.id,
            "status": AsyncTaskStatus.COMPLETED,
            "result": result,
        }

    except Exception as e:
        logger.error(f"Error in delete_document_task: {str(e)}")
        raise
