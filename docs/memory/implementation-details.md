# Memory Feature Implementation Details

## Overview

This document provides a deep technical dive into the Memory feature implementation, covering algorithms, data structures, and internal mechanisms.

## Memory Extraction Pipeline

### 1. Conversation Analysis

**Location**: `MemoryService._should_extract_learnings()`

The system first determines if a conversation is worth extracting memories from:

```python
async def _should_extract_learnings(self, message: str) -> bool:
    """Determine if conversation contains extractable content."""
    
    # Use LLM to analyze conversation content
    decision_messages = [
        {"role": "system", "content": EXTRACTION_DECISION_PROMPT},
        {"role": "user", "content": message}
    ]
    
    model = load_chat_model(settings.KEY_LEARNING_DECISION_MODEL)
    decision = await model.with_structured_output(ExtractionDecision).ainvoke(
        decision_messages,
        config={"run_name": "Memory Extraction Decision"}
    )
    
    return decision.should_extract
```

**Decision Criteria**:
- Technical content related to cloud services
- Problem-solving activities
- Tool usage and command execution
- More than simple Q&A or greetings

### 2. Memory Node Extraction

**Location**: `MemoryService._extract_memory_node()`

Converts conversation messages into structured memory nodes:

```python
async def _extract_memory_node(self, model: BaseChatModel, messages: list[AnyMessage]) -> MemoryNode:
    """Extract structured memory from conversation messages."""
    
    # Convert messages to string format
    oai_messages = convert_to_openai_messages(messages)
    messages_str = "\n".join([f"{msg['role']}: {msg['content']}" for msg in oai_messages])
    
    # Use structured output to extract memory
    extraction_messages = [
        {"role": "system", "content": EXTRACTION_PROMPT},
        {"role": "user", "content": messages_str}
    ]
    
    memory_node = await model.with_structured_output(MemoryNode).ainvoke(
        extraction_messages,
        config={"run_name": "Memory Node Extraction"}
    )
    
    return memory_node
```

**Extraction Focus**:
- Tool call sequences and optimizations
- Error patterns and solutions
- Parameter combinations that work
- Conditional logic for different scenarios

### 3. Embedding Generation

**Location**: `MemoryService._generate_embedding_with_retry()`

Generates vector embeddings with retry logic:

```python
async def _generate_embedding_with_retry(self, text: str) -> list[float]:
    """Generate embeddings with exponential backoff retry."""
    
    for attempt in range(MAX_EMBEDDING_RETRIES):
        try:
            embedding = await self.embed_model.aget_text_embedding(text)
            return embedding
        except Exception as e:
            if attempt == MAX_EMBEDDING_RETRIES - 1:
                raise e
            
            wait_time = RETRY_DELAY * (2 ** attempt)
            await asyncio.sleep(wait_time)
            logger.warning(f"Embedding generation failed, retrying in {wait_time}s")
```

**Embedding Model**: Cohere Embed Multilingual v3
- **Dimensions**: 1024
- **Language Support**: Multilingual
- **Use Case**: Optimized for semantic search

## Memory Evolution Algorithm

### 1. Similarity Search

**Location**: `MemoryService.extract_key_learnings_by_role()`

Finds related memories using vector similarity:

```python
# Create vector store index
index = VectorStoreIndex.from_vector_store(
    vector_store=vector_store,
    embed_model=self.embed_model,
)

# Search for related memories
query_engine = index.as_query_engine(
    llm=Bedrock(model=settings.SEARCH_MODEL_LLAMAINDEX),
    similarity_top_k=settings.MEMORY_MAX_RELATED_MEMORY_NODES,
    similarity_cutoff=settings.MEMORY_RELATEDNESS_THRESHOLD,
)

related_memory_nodes = query_engine.retrieve(current_memory_node_json)
```

**Search Parameters**:
- **Top K**: Maximum 5 related memories
- **Similarity Threshold**: 0.6 (cosine similarity)
- **Distance Metric**: Cosine distance

### 2. Evolution Decision

**Location**: Memory evolution analysis in extraction pipeline

Uses LLM to determine how memories should evolve:

```python
evolution_inputs = [
    SystemMessage(content=f"Analyzing memory evolution with {neighbor_count} related nodes"),
    HumanMessage(content=f"Current: {current_memory_node_json}\nRelated: {related_nodes}")
]

memory_evolution = await model.with_structured_output(MemoryEvolution).ainvoke(
    evolution_inputs,
    config={"run_name": "Memory Evolution"}
)
```

**Evolution Types**:

1. **Combine**: Merge similar memories into improved version
   ```python
   if memory_evolution.evolution_type == "combine":
       # Delete old memories
       nodes_to_delete.extend(memory_evolution.combined_node.combined_from)
       # Store new combined memory
       current_vector_store_node.text = memory_evolution.combined_node.new_node.model_dump_json()
   ```

2. **Connect**: Link related but distinct memories
   ```python
   elif memory_evolution.evolution_type == "connect":
       # Add connection links to current memory
       current_memory_node.links = memory_evolution.connections
   ```

3. **Standalone**: Store independently
   ```python
   else:  # standalone
       # Store as-is without modifications
       pass
   ```

### 3. Vector Store Operations

**Collection Management**:

```python
async def _get_vector_store(self, collection_name: str):
    """Get or create vector store collection."""
    
    if not await self.aqdrant_client.collection_exists(collection_name):
        await self.aqdrant_client.create_collection(
            collection_name=collection_name,
            vectors_config={
                "text-dense": qdrant_models.VectorParams(
                    size=settings.EMBEDDING_MODEL_DIMS,  # 1024
                    distance=Distance.COSINE,
                ),
            },
        )
    
    return QdrantVectorStore(
        collection_name=collection_name,
        client=self.qdrant_client,
        aclient=self.aqdrant_client,
        parallel=10,
        dense_vector_name="text-dense",
    )
```

**Storage Operations**:
- **Add**: Store new memory with embedding
- **Delete**: Remove outdated memories
- **Update**: Modify existing memory content

## Search Implementation

### Semantic Search

**Location**: `MemoryService.search_memory()`

Implements semantic search for memory retrieval:

```python
async def search_memory(self, query: str, role: str, workspace_id: str) -> str:
    """Search memories using semantic similarity."""
    
    collection_name = get_template(workspace_id=workspace_id, agent_role=role)
    vector_store = await self._get_vector_store(collection_name)
    
    # Create search index
    index = VectorStoreIndex.from_vector_store(
        vector_store=vector_store,
        embed_model=self.embed_model,
    )
    
    # Execute search
    query_engine = index.as_query_engine(
        llm=Bedrock(model=settings.SEARCH_MODEL_LLAMAINDEX),
        similarity_top_k=settings.MEMORY_MAX_RELATED_MEMORY_NODES,
        similarity_cutoff=settings.MEMORY_RELATEDNESS_THRESHOLD,
    )
    
    nodes = query_engine.retrieve(query)
    
    # Format results
    return self._format_search_results(nodes)
```

### Result Formatting

```python
def _format_search_results(self, nodes) -> str:
    """Format search results for agent consumption."""
    
    results = ""
    for index, node in enumerate(nodes):
        if node.get_score() < settings.MEMORY_RELATEDNESS_THRESHOLD:
            continue
            
        score = node.get_score()
        node_data = json.loads(node.text)
        
        result = f"""Memory {index + 1}.
Score: {score}
Tags: {node_data.get("tags", "No tags")}
Task: {node_data.get("task", "No task")}
Solution: {node_data.get("solution", "No solution")}
"""
        results += result
    
    return results if results else "No related memory nodes found"
```

## Data Structures

### Memory Node Schema

```python
class MemoryNode(BaseModel):
    """Core memory structure."""
    
    tags: list[str] | None = Field(
        default=None,
        metadata={"description": "Categorization tags"}
    )
    task: str | None = Field(
        default=None,
        metadata={"description": "Problem or task description"}
    )
    solution: str | None = Field(
        default=None,
        metadata={"description": "Detailed solution approach"}
    )
    links: list[str] | None = Field(
        default=None,
        metadata={"description": "References to related memories"}
    )
```

### Memory Evolution Schema

```python
class MemoryEvolution(BaseModel):
    """Memory evolution decision structure."""
    
    should_evolve: bool = Field(
        description="Whether memory requires evolution"
    )
    evolution_type: Literal["combine", "connect", "standalone"] | None = Field(
        description="Type of evolution to perform"
    )
    combined_node: CombinedNode | None = Field(
        description="New synthesized node for combine operations"
    )
    connections: list[str] | None = Field(
        description="Memory IDs to connect with"
    )
    rationale: str | None = Field(
        description="Explanation for evolution decision"
    )
```

### Vector Store Document

```python
# Document structure in Qdrant
Document(
    text=memory_node_json,           # Serialized MemoryNode
    metadata={"role": agent_role},   # Agent role for filtering
    embedding=embedding_vector,      # 1024-dimensional vector
    node_id=uuid_string             # Unique identifier
)
```

## Collection Naming Strategy

### Template Function

```python
def get_template(workspace_id: str, agent_role: str) -> str:
    """Generate collection name for memory storage."""
    return f"memories-{workspace_id}-{agent_role}"
```

**Examples**:
- `memories-123e4567-e89b-12d3-a456-************-aws_cost_optimizer`
- `memories-123e4567-e89b-12d3-a456-************-security_analyst`

**Benefits**:
- **Workspace Isolation**: Each workspace has separate collections
- **Role Separation**: Different agent types have dedicated storage
- **Scalability**: Distributed across multiple collections
- **Security**: Natural access control boundaries

## Background Processing

### Celery Task Implementation

```python
@celery_app.task(name="memory_tasks.extract_key_learnings", priority=NORMAL_PRIORITY)
def extract_key_learnings_task(conversation_id: str) -> dict:
    """Background task for memory extraction."""
    
    @async_to_sync
    async def extract_learnings():
        async with AsyncSession(async_engine) as session:
            conv_uuid = uuid.UUID(conversation_id)
            memory_handler = MemoryService(session)
            return await memory_handler.extract_key_learnings_by_role(conv_uuid)
    
    result = extract_learnings()
    
    if result.get("success", False):
        logger.info(f"Successfully extracted memories from {conversation_id}")
    else:
        logger.error(f"Failed to extract memories: {result.get('error')}")
    
    return result
```

### Task Triggering

```python
def trigger_memory_extraction(self, conversation_id: uuid.UUID) -> None:
    """Trigger background memory extraction."""
    
    try:
        from app.tasks.memory_tasks import extract_key_learnings_task
        
        # Queue background task
        task = extract_key_learnings_task.delay(str(conversation_id))
        
        logger.debug(f"Triggered memory extraction for {conversation_id} (task: {task.id})")
    except Exception as e:
        logger.error(f"Failed to trigger memory extraction: {str(e)}")
```

## Error Handling

### Retry Mechanisms

```python
# Embedding generation with exponential backoff
for attempt in range(MAX_EMBEDDING_RETRIES):
    try:
        embedding = await self.embed_model.aget_text_embedding(text)
        return embedding
    except Exception as e:
        if attempt == MAX_EMBEDDING_RETRIES - 1:
            raise e
        wait_time = RETRY_DELAY * (2 ** attempt)
        await asyncio.sleep(wait_time)
```

### Graceful Degradation

```python
# Handle missing collections gracefully
if not client.collection_exists(collection_name):
    return f"{agent_role} has no memories yet, please dont call this tool."

# Handle empty search results
if len(nodes) == 0:
    return "No related memory nodes found"
```

### Error Logging

```python
try:
    # Memory operation
    pass
except Exception as e:
    logger.error(f"Memory operation failed: {str(e)}")
    logger.exception("Detailed error information:")
    raise HTTPException(status_code=500, detail=str(e))
```

## Performance Optimizations

### Async Operations

All heavy operations use async/await:
- Database queries
- Vector store operations
- LLM API calls
- Embedding generation

### Connection Pooling

```python
# Qdrant client with connection pooling
aclient = qdrant_client.AsyncQdrantClient(
    host=settings.QDRANT_HOST,
    port=settings.QDRANT_PORT,
    grpc_port=settings.QDRANT_GRPC_PORT,
    prefer_grpc=True,  # Better performance
)
```

### Parallel Processing

```python
# Vector store with parallel processing
QdrantVectorStore(
    collection_name=collection_name,
    client=client,
    aclient=aclient,
    parallel=10,  # Parallel operations
)
```

### Caching Strategies

- **Embedding Caching**: Reuse embeddings for similar content
- **Collection Caching**: Cache collection existence checks
- **Model Loading**: Reuse loaded models across requests

## Security Implementation

### Workspace Isolation

```python
# Collection names include workspace ID
collection_name = f"memories-{workspace_id}-{agent_role}"

# API filtering by current user's workspace
current_user.current_workspace_id
```

### Role-Based Filtering

```python
# Metadata filtering in vector store
MetadataFilters(
    filters=[
        MetadataFilter(key="role", value=agent_role)
    ]
)
```

### Data Sanitization

- Only structured memory nodes stored (no raw conversations)
- Sensitive data filtered during extraction
- User-controlled deletion capabilities

## Monitoring and Observability

### Langfuse Integration

```python
# Track LLM calls
config = {
    "run_name": "Memory Node Extraction",
    "callbacks": [langfuse_handler],
}

memory_node = await model.with_structured_output(MemoryNode).ainvoke(
    messages, config=config
)
```

### Structured Logging

```python
logger.info(f"Extracted memory node: {memory_node.model_dump_json(indent=2)}")
logger.info(f"Found {len(related_memory_nodes)} related memory nodes")
logger.debug(f"Getting MFU memories for collection: {collection_name}")
```

### Metrics Collection

- Memory extraction success rates
- Search query performance
- Vector store operation timing
- LLM API call latency
