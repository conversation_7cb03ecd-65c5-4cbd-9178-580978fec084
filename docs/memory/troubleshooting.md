# Memory Feature Troubleshooting

## Common Issues and Solutions

### Memory Extraction Issues

#### Memories Not Being Created

**Symptoms**:
- Conversations complete but no memories appear in dashboard
- Background tasks show no memory extraction activity
- Agent tools report "no memories yet"

**Possible Causes & Solutions**:

1. **Conversation Content Not Substantive**
   ```
   Problem: Conversations lack technical problem-solving content
   Solution: Ensure conversations include:
   - Specific technical problems
   - Tool usage and commands
   - Error resolution steps
   - Detailed solutions
   ```

2. **Background Task Failures**
   ```bash
   # Check Celery worker status
   celery -A app.core.celery_app worker --loglevel=info
   
   # Check Redis connection
   redis-cli ping
   
   # Verify task queue
   celery -A app.core.celery_app inspect active
   ```

3. **LLM Model Configuration Issues**
   ```bash
   # Verify model settings
   echo $KEY_LEARNING_DECISION_MODEL
   echo $BEDROCK_REGION_NAME
   
   # Test AWS credentials
   aws bedrock list-foundation-models --region us-east-1
   ```

4. **Database Connection Problems**
   ```bash
   # Check PostgreSQL connection
   psql -h $POSTGRES_SERVER -U $POSTGRES_USER -d $POSTGRES_DB -c "SELECT 1;"
   
   # Verify conversation data exists
   psql -c "SELECT COUNT(*) FROM conversation;"
   ```

#### Memory Extraction Fails Silently

**Symptoms**:
- Task appears to complete but no memories created
- No error messages in logs
- Memory count remains zero

**Debugging Steps**:

1. **Enable Debug Logging**
   ```python
   # Add to logging configuration
   logging.getLogger("app.services.memory").setLevel(logging.DEBUG)
   ```

2. **Check Extraction Decision**
   ```python
   # Test extraction decision manually
   from app.services.memory.memory_service import MemoryService
   
   memory_service = MemoryService(session)
   should_extract = await memory_service._should_extract_learnings(message_content)
   print(f"Should extract: {should_extract}")
   ```

3. **Verify Conversation Messages**
   ```sql
   -- Check conversation has messages
   SELECT c.id, COUNT(m.id) as message_count
   FROM conversation c
   LEFT JOIN message m ON c.id = m.conversation_id
   GROUP BY c.id
   ORDER BY c.created_at DESC
   LIMIT 10;
   ```

### Vector Store Issues

#### Qdrant Connection Failures

**Symptoms**:
- "Connection refused" errors
- Timeout errors during memory operations
- Vector store operations fail

**Solutions**:

1. **Check Qdrant Service Status**
   ```bash
   # Test Qdrant HTTP API
   curl http://localhost:6333/collections
   
   # Check service logs
   docker logs qdrant_container
   
   # Verify ports are open
   netstat -tlnp | grep 6333
   ```

2. **Configuration Issues**
   ```bash
   # Verify environment variables
   echo $QDRANT_HOST
   echo $QDRANT_PORT
   echo $QDRANT_GRPC_PORT
   
   # Test gRPC connection
   grpcurl -plaintext localhost:6334 list
   ```

3. **Network Connectivity**
   ```bash
   # Test from application container
   docker exec app_container ping qdrant
   
   # Check Docker network
   docker network ls
   docker network inspect bridge
   ```

#### Collection Creation Failures

**Symptoms**:
- "Failed to create collection" errors
- Collections not appearing in Qdrant
- Vector operations fail with collection not found

**Solutions**:

1. **Check Collection Naming**
   ```python
   # Verify collection name format
   from app.services.memory.template import get_template
   
   collection_name = get_template("workspace-id", "agent-role")
   print(f"Collection name: {collection_name}")
   ```

2. **Verify Vector Configuration**
   ```python
   # Check embedding dimensions match
   print(f"Embedding dims: {settings.EMBEDDING_MODEL_DIMS}")
   
   # Test embedding generation
   embedding = await embed_model.aget_text_embedding("test")
   print(f"Generated embedding dims: {len(embedding)}")
   ```

3. **Manual Collection Creation**
   ```python
   # Create collection manually for testing
   from qdrant_client import QdrantClient
   from qdrant_client.models import Distance, VectorParams
   
   client = QdrantClient(host="localhost", port=6333)
   client.create_collection(
       collection_name="test-collection",
       vectors_config=VectorParams(size=1024, distance=Distance.COSINE)
   )
   ```

### Embedding Generation Issues

#### Embedding API Failures

**Symptoms**:
- "Failed to generate embedding" errors
- Timeout errors during embedding generation
- Inconsistent embedding dimensions

**Solutions**:

1. **AWS Bedrock Configuration**
   ```bash
   # Verify AWS credentials
   aws sts get-caller-identity
   
   # Test Bedrock access
   aws bedrock list-foundation-models --region us-east-1
   
   # Check embedding model availability
   aws bedrock get-foundation-model --model-identifier cohere.embed-multilingual-v3
   ```

2. **Retry Configuration**
   ```python
   # Increase retry settings
   MAX_EMBEDDING_RETRIES = 5
   RETRY_DELAY = 3
   
   # Add exponential backoff
   wait_time = RETRY_DELAY * (2 ** attempt)
   ```

3. **Alternative Embedding Models**
   ```bash
   # Try different embedding model
   EMBEDDING_MODEL=bedrock:amazon.titan-embed-text-v1
   EMBEDDING_MODEL_DIMS=1536
   ```

### Search and Retrieval Issues

#### Search Returns No Results

**Symptoms**:
- Memory search tool returns "No related memory nodes found"
- Memories exist but aren't found by search
- Low similarity scores for relevant content

**Solutions**:

1. **Check Similarity Threshold**
   ```bash
   # Lower threshold for more results
   MEMORY_RELATEDNESS_THRESHOLD=0.4  # Default: 0.6
   
   # Increase search limit
   MEMORY_MAX_RELATED_MEMORY_NODES=10  # Default: 5
   ```

2. **Verify Search Query**
   ```python
   # Test search manually
   from app.services.memory.memory_service import MemoryService
   
   memory_service = MemoryService()
   results = await memory_service.search_memory(
       query="your search query",
       role="agent_role",
       workspace_id="workspace_id"
   )
   print(results)
   ```

3. **Check Collection Contents**
   ```python
   # List collection contents
   from qdrant_client import QdrantClient
   
   client = QdrantClient(host="localhost", port=6333)
   points = client.scroll(collection_name="your-collection", limit=10)
   print(f"Found {len(points[0])} points in collection")
   ```

#### Poor Search Quality

**Symptoms**:
- Irrelevant memories returned
- Good memories not found
- Inconsistent search results

**Solutions**:

1. **Improve Memory Quality**
   - Delete low-quality memories
   - Ensure consistent tagging
   - Use specific task descriptions
   - Include detailed solutions

2. **Adjust Search Parameters**
   ```bash
   # Fine-tune similarity threshold
   MEMORY_RELATEDNESS_THRESHOLD=0.7  # More selective
   
   # Adjust search model
   SEARCH_MODEL_LLAMAINDEX=us.anthropic.claude-3-5-sonnet-20241022-v2:0
   ```

3. **Reindex Collections**
   ```python
   # Delete and recreate collection if needed
   client.delete_collection("collection-name")
   # Trigger memory re-extraction
   ```

### Performance Issues

#### Slow Memory Operations

**Symptoms**:
- Long delays during memory extraction
- Timeouts during search operations
- High CPU/memory usage

**Solutions**:

1. **Optimize Vector Store Settings**
   ```python
   # Increase parallel processing
   QdrantVectorStore(
       collection_name=collection_name,
       parallel=20,  # Increase from 10
   )
   ```

2. **Tune Database Connections**
   ```bash
   # Increase connection pool size
   KB_CONNECTION_POOL_SIZE=20  # Default: 10
   KB_ASYNC_TIMEOUT=120       # Default: 60
   ```

3. **Optimize Background Processing**
   ```bash
   # Increase Celery workers
   CELERY_WORKER_CONCURRENCY=8  # Default: 4
   
   # Adjust task timeouts
   CELERY_TASK_SOFT_TIME_LIMIT=600  # 10 minutes
   ```

#### Memory Usage Issues

**Symptoms**:
- High memory consumption
- Out of memory errors
- Slow garbage collection

**Solutions**:

1. **Limit Batch Sizes**
   ```bash
   # Reduce batch processing
   MEMORY_MAX_RELATED_MEMORY_NODES=3  # Default: 5
   KB_BATCH_SIZE=25                   # Default: 50
   ```

2. **Optimize Embedding Caching**
   ```python
   # Clear embedding cache periodically
   embed_model.clear_cache()
   
   # Use smaller embedding models
   EMBEDDING_MODEL=bedrock:amazon.titan-embed-text-v1
   ```

### API and Frontend Issues

#### Memory Dashboard Not Loading

**Symptoms**:
- Empty memory table
- Loading spinner never stops
- API errors in browser console

**Solutions**:

1. **Check API Endpoints**
   ```bash
   # Test memory API directly
   curl -X POST "http://localhost:8000/api/v1/memory/" \
     -H "Authorization: Bearer $JWT_TOKEN" \
     -H "Content-Type: application/json" \
     -d '{"agent_roles": null, "limit": 10}'
   ```

2. **Verify Authentication**
   ```javascript
   // Check JWT token in browser
   localStorage.getItem('auth-token')
   
   // Verify token expiration
   jwt-decode token_string
   ```

3. **Check Network Requests**
   ```
   Open browser DevTools → Network tab
   Look for failed API requests
   Check response status and error messages
   ```

#### Memory Deletion Fails

**Symptoms**:
- Delete button doesn't work
- Memories reappear after deletion
- API errors during deletion

**Solutions**:

1. **Check API Permissions**
   ```bash
   # Test delete endpoint
   curl -X DELETE "http://localhost:8000/api/v1/memory/?id=memory-id&agent_role=role" \
     -H "Authorization: Bearer $JWT_TOKEN"
   ```

2. **Verify Memory ID Format**
   ```python
   # Ensure valid UUID format
   import uuid
   try:
       uuid.UUID(memory_id)
       print("Valid UUID")
   except ValueError:
       print("Invalid UUID format")
   ```

## Diagnostic Commands

### System Health Check

```bash
#!/bin/bash
# Memory system health check script

echo "=== Memory System Health Check ==="

# Check Qdrant
echo "Checking Qdrant..."
curl -s http://localhost:6333/collections | jq '.result.collections[] | select(.name | startswith("memories-"))'

# Check Redis
echo "Checking Redis..."
redis-cli ping

# Check PostgreSQL
echo "Checking PostgreSQL..."
psql -h $POSTGRES_SERVER -U $POSTGRES_USER -d $POSTGRES_DB -c "SELECT COUNT(*) FROM conversation;"

# Check AWS Bedrock
echo "Checking AWS Bedrock..."
aws bedrock list-foundation-models --region us-east-1 --query 'modelSummaries[?contains(modelId, `cohere.embed`)]'

# Check Celery
echo "Checking Celery..."
celery -A app.core.celery_app inspect ping

echo "=== Health Check Complete ==="
```

### Memory Statistics

```python
# Get memory statistics
from app.core.qdrant import client
from app.services.memory.template import get_template

def get_memory_stats(workspace_id: str, agent_role: str):
    collection_name = get_template(workspace_id, agent_role)
    
    if not client.collection_exists(collection_name):
        return {"error": "Collection does not exist"}
    
    info = client.get_collection(collection_name)
    points = client.scroll(collection_name, limit=1, with_payload=False)
    
    return {
        "collection_name": collection_name,
        "total_points": info.points_count,
        "vector_size": info.config.params.vectors.size,
        "distance": info.config.params.vectors.distance,
        "sample_points": len(points[0])
    }
```

### Log Analysis

```bash
# Search for memory-related errors
grep -i "memory\|extraction\|embedding" /var/log/app.log | tail -50

# Check Celery task logs
grep -i "extract_key_learnings" /var/log/celery.log | tail -20

# Monitor real-time memory operations
tail -f /var/log/app.log | grep -i memory
```

## Getting Help

### Log Collection

When reporting issues, collect these logs:

```bash
# Application logs
docker logs app_container > app.log

# Qdrant logs
docker logs qdrant_container > qdrant.log

# Celery logs
docker logs celery_worker > celery.log

# System information
docker ps > containers.log
docker network ls > networks.log
```

### Configuration Dump

```bash
# Environment variables
env | grep -E "(MEMORY|QDRANT|EMBEDDING|BEDROCK)" > config.txt

# Memory service settings
python -c "
from app.core.config import settings
print(f'MEMORY_MAX_RELATED_MEMORY_NODES: {settings.MEMORY_MAX_RELATED_MEMORY_NODES}')
print(f'MEMORY_RELATEDNESS_THRESHOLD: {settings.MEMORY_RELATEDNESS_THRESHOLD}')
print(f'EMBEDDING_MODEL: {settings.EMBEDDING_MODEL}')
print(f'EMBEDDING_MODEL_DIMS: {settings.EMBEDDING_MODEL_DIMS}')
"
```

### Support Information

When seeking support, provide:

1. **Error Messages**: Complete error logs with stack traces
2. **Configuration**: Environment variables and settings
3. **System Info**: Docker versions, OS, hardware specs
4. **Reproduction Steps**: Exact steps to reproduce the issue
5. **Expected vs Actual**: What should happen vs what actually happens
