# Memory Feature Documentation

## Overview

The Memory feature is an intelligent system that automatically extracts, stores, and retrieves key learnings from agent conversations. It enables agents to build persistent knowledge that improves their performance over time by learning from past interactions, solutions, and patterns.

## Key Features

- **Automatic Extraction**: Intelligently extracts key learnings from completed conversations
- **Vector Storage**: Uses Qdrant vector database for efficient similarity-based retrieval
- **Memory Evolution**: Combines and connects related memories to avoid duplication
- **Agent-Specific**: Maintains separate memory collections for different agent roles
- **Workspace Isolation**: Memories are isolated per workspace for security
- **Search & Retrieval**: Provides semantic search capabilities for relevant memories

## How It Works

1. **Conversation Completion**: When a conversation ends, the system analyzes it for extractable content
2. **Content Filtering**: Determines if the conversation contains substantive technical content worth learning from
3. **Memory Extraction**: Uses LLM to extract structured memory nodes (task, solution, tags, links)
4. **Similarity Analysis**: Finds related existing memories using vector similarity search
5. **Memory Evolution**: Decides whether to combine, connect, or store memories independently
6. **Storage**: Stores memories in Qdrant vector database with embeddings for retrieval
7. **Retrieval**: Agents can search memories during conversations to access relevant past learnings

## Architecture Components

- **MemoryService**: Core service handling extraction, storage, and retrieval
- **Vector Store**: Qdrant database for storing memory embeddings
- **Memory Schemas**: Pydantic models defining memory structure
- **Background Tasks**: Celery tasks for asynchronous memory processing
- **API Endpoints**: REST APIs for memory management
- **Frontend UI**: Web interface for viewing and managing memories

## Quick Start

### For Developers

1. **Backend Setup**: Memory service is automatically initialized with the application
2. **Configuration**: Set required environment variables (see [Configuration](./configuration.md))
3. **Usage**: Memories are automatically extracted from conversations
4. **Access**: Use the `/api/v1/memory/` endpoints or frontend UI

### For Users

1. **Automatic**: Memories are created automatically from your conversations
2. **View**: Access memories through the dashboard at `/memories`
3. **Search**: Filter memories by agent role or search content
4. **Manage**: View details and delete unwanted memories

## Documentation Structure

- [Architecture](./architecture.md) - Technical design and components
- [API Reference](./api-reference.md) - REST API documentation
- [User Guide](./user-guide.md) - How to use the memory feature
- [Configuration](./configuration.md) - Settings and environment variables
- [Implementation Details](./implementation-details.md) - Deep technical dive
- [Troubleshooting](./troubleshooting.md) - Common issues and solutions
- [Examples](./examples.md) - Usage examples and use cases

## Key Benefits

- **Improved Agent Performance**: Agents learn from past successes and failures
- **Reduced Redundancy**: Avoids repeating the same mistakes or inefficient approaches
- **Knowledge Accumulation**: Builds a persistent knowledge base over time
- **Context Awareness**: Provides relevant historical context for current tasks
- **Optimization**: Identifies and reuses optimal tool sequences and parameters

## Memory Types

The system extracts different types of memories:

- **Task Solutions**: Specific problems and their solutions
- **Tool Sequences**: Optimal sequences of tool calls for common tasks
- **Error Patterns**: Common errors and their resolution steps
- **Best Practices**: Proven approaches and optimization techniques
- **Conditional Logic**: Decision trees for different scenarios

## Next Steps

- Read the [Architecture](./architecture.md) to understand the technical design
- Check [Configuration](./configuration.md) to set up your environment
- Follow the [User Guide](./user-guide.md) to start using memories
- Explore [Examples](./examples.md) for practical use cases
