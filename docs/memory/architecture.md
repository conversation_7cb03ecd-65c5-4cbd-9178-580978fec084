# Memory Feature Architecture

## System Overview

The Memory feature is built on a multi-layered architecture that combines LLM-based extraction, vector storage, and intelligent memory evolution to create a persistent learning system for AI agents.

```mermaid
graph TB
    A[Conversation Completion] --> B[Memory Extraction Decision]
    B --> C{Should Extract?}
    C -->|Yes| D[Extract Memory Node]
    C -->|No| E[Skip Extraction]
    D --> F[Generate Embeddings]
    F --> G[Find Related Memories]
    G --> H[Memory Evolution Analysis]
    H --> I{Evolution Type}
    I -->|Combine| J[Merge Memories]
    I -->|Connect| K[Link Memories]
    I -->|Standalone| L[Store Independently]
    J --> M[Update Vector Store]
    K --> M
    L --> M
    M --> N[Background Task Complete]
```

## Core Components

### 1. Memory Service (`MemoryService`)

**Location**: `backend/app/services/memory/memory_service.py`

The central orchestrator that handles all memory operations:

- **Extraction Logic**: Determines what conversations are worth extracting from
- **LLM Integration**: Uses structured output to extract memory nodes
- **Vector Operations**: Manages embeddings and similarity search
- **Evolution Engine**: Implements memory combination and connection logic
- **Search Interface**: Provides semantic search capabilities

**Key Methods**:
- `extract_key_learnings_by_role()`: Main extraction workflow
- `search_memory()`: Semantic search for relevant memories
- `_extract_memory_node()`: LLM-based memory extraction
- `_should_extract_learnings()`: Content filtering logic

### 2. Vector Storage Layer

**Technology**: Qdrant Vector Database
**Configuration**: `backend/app/core/qdrant.py`

**Collection Structure**:
```
memories-{workspace_id}-{agent_role}
├── Vectors: 1024-dimensional embeddings (Cohere multilingual)
├── Metadata: {"role": "agent_role"}
└── Payload: JSON-serialized MemoryNode
```

**Features**:
- Cosine similarity search
- Metadata filtering by agent role
- Automatic collection creation
- Parallel processing support

### 3. Memory Schemas

**Location**: `backend/app/services/memory/schema.py`

**Core Models**:

```python
class MemoryNode(BaseModel):
    tags: list[str] | None          # Categorization tags
    task: str | None                # Problem description
    solution: str | None            # Detailed solution
    links: list[str] | None         # Related memory IDs

class MemoryEvolution(BaseModel):
    should_evolve: bool             # Whether to evolve
    evolution_type: Literal["combine", "connect", "standalone"]
    combined_node: CombinedNode | None
    connections: list[str] | None
    rationale: str | None

class ExtractionDecision(BaseModel):
    should_extract: bool            # Whether to extract memories
```

### 4. Background Processing

**Technology**: Celery
**Location**: `backend/app/tasks/memory_tasks.py`

**Workflow**:
1. Conversation completion triggers `extract_key_learnings_task`
2. Task runs asynchronously to avoid blocking user interactions
3. Handles failures gracefully with logging and error reporting

### 5. API Layer

**Location**: `backend/app/api/routes/memory.py`

**Endpoints**:
- `POST /api/v1/memory/`: Retrieve memories with filtering
- `DELETE /api/v1/memory/`: Delete specific memory
- `PUT /api/v1/memory/`: Update memory (future enhancement)

### 6. Frontend Interface

**Location**: `frontend/app/(dashboard)/memories/`

**Components**:
- **Memory Listing**: Paginated table with filtering
- **Memory Dialog**: Detailed view with markdown rendering
- **Search & Filter**: By agent role and content
- **Actions**: View details and delete memories

## Data Flow

### Memory Extraction Flow

1. **Trigger**: Conversation completion in `BaseService.trigger_memory_extraction()`
2. **Queue**: Celery task queued with conversation ID
3. **Processing**: `MemoryService.extract_key_learnings_by_role()` executes
4. **Filtering**: Content analysis determines extraction worthiness
5. **Extraction**: LLM extracts structured memory node
6. **Embedding**: Generate vector embedding for similarity search
7. **Evolution**: Analyze relationships with existing memories
8. **Storage**: Store in Qdrant with metadata and embeddings

### Memory Retrieval Flow

1. **Search Request**: Agent tool or API call with query
2. **Collection Lookup**: Determine collection name from workspace/role
3. **Vector Search**: Similarity search in Qdrant
4. **Filtering**: Apply similarity threshold and metadata filters
5. **Formatting**: Convert results to readable format
6. **Return**: Structured memory information to requester

## Memory Evolution Logic

### Decision Matrix

| Scenario | Evolution Type | Action |
|----------|---------------|---------|
| Similar task, better solution | Combine | Merge into improved memory |
| Related but different tasks | Connect | Link memories with references |
| Unique task/solution | Standalone | Store independently |
| Duplicate content | Combine | Replace with consolidated version |

### Combination Criteria

Memories are combined when:
- Same or very similar task description
- Combined solution provides more value
- Similarity score above threshold (0.6)
- Same agent role and workspace

### Connection Criteria

Memories are connected when:
- Related but distinct tasks
- Solutions complement each other
- Part of larger workflow or process
- Cross-references would be valuable

## Scalability Considerations

### Performance Optimizations

1. **Async Processing**: All heavy operations run in background
2. **Embedding Caching**: Reuse embeddings where possible
3. **Batch Operations**: Process multiple memories efficiently
4. **Connection Pooling**: Optimize database connections

### Storage Efficiency

1. **Collection Isolation**: Separate collections per workspace/role
2. **Metadata Indexing**: Efficient filtering by role
3. **Similarity Thresholds**: Prevent low-quality matches
4. **Memory Limits**: Configurable limits per workspace

### Monitoring & Observability

1. **Langfuse Integration**: Track LLM calls and performance
2. **Structured Logging**: Detailed operation logs
3. **Error Handling**: Graceful failure recovery
4. **Metrics**: Extraction success rates and timing

## Security & Privacy

### Data Isolation

- **Workspace Separation**: Collections namespaced by workspace ID
- **Role-Based Access**: Memories filtered by agent role
- **User Permissions**: API access controlled by authentication

### Data Protection

- **Embedding Only**: No raw conversation data in vector store
- **Structured Extraction**: Only relevant technical content extracted
- **Deletion Support**: Complete memory removal capability
- **Audit Trail**: Logging of all memory operations

## Configuration Points

### Environment Variables

- `MEMORY_MAX_RELATED_MEMORY_NODES`: Maximum memories to consider for evolution
- `MEMORY_RELATEDNESS_THRESHOLD`: Similarity threshold for connections
- `EMBEDDING_MODEL_DIMS`: Vector dimensions (1024)
- `KEY_LEARNING_DECISION_MODEL`: LLM model for extraction decisions

### Runtime Configuration

- Collection naming templates
- Similarity thresholds
- Batch processing limits
- Retry policies for failed operations

## Future Enhancements

### Planned Features

1. **Memory Analytics**: Usage patterns and effectiveness metrics
2. **Manual Memory Creation**: User-created memories
3. **Memory Templates**: Predefined memory structures
4. **Cross-Agent Learning**: Shared memories between agent types
5. **Memory Versioning**: Track memory evolution over time

### Technical Improvements

1. **Hybrid Search**: Combine dense and sparse vectors
2. **Memory Compression**: Reduce storage requirements
3. **Real-time Updates**: Live memory updates during conversations
4. **Advanced Evolution**: More sophisticated combination logic
