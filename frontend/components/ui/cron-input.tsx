'use client';

import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import {
    Popover,
    PopoverContent,
    PopoverTrigger,
} from '@/components/ui/popover';
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from '@/components/ui/select';
import { cn } from '@/lib/utils';
import cronstrue from 'cronstrue';
import { format, setHours, setMinutes } from 'date-fns';
import { ChevronDown, Clock } from 'lucide-react';
import { useEffect, useState } from 'react';

// Common cron presets
const CRON_PRESETS = [
    { label: 'Every minute', value: '* * * * *' },
    { label: 'Every hour', value: '0 * * * *' },
    { label: 'Every day at midnight', value: '0 0 * * *' },
    { label: 'Every day at noon', value: '0 12 * * *' },
    { label: 'Every Monday at 9 AM', value: '0 9 * * 1' },
    { label: 'Every weekday at 9 AM', value: '0 9 * * 1-5' },
    { label: 'Every month on the 1st at 9 AM', value: '0 9 1 * *' },
    { label: 'Every 15 minutes', value: '*/15 * * * *' },
    { label: 'Every 30 minutes', value: '*/30 * * * *' },
    { label: 'Every 2 hours', value: '0 */2 * * *' },
] as const;

// Generate time options
const HOURS = Array.from({ length: 24 }, (_, i) => ({
    value: i.toString().padStart(2, '0'),
    label: format(setHours(new Date(), i), 'h a')
}));

const MINUTES = Array.from({ length: 12 }, (_, i) => ({
    value: (i * 5).toString().padStart(2, '0'),
    label: format(setMinutes(new Date(), i * 5), 'mm')
}));

export interface CronInputProps {
    value: string;
    onChange: (value: string) => void;
    onValidityChange?: (isValid: boolean) => void;
    className?: string;
    disabled?: boolean;
    placeholder?: string;
}

export function CronInput({ 
    value, 
    onChange, 
    onValidityChange,
    className,
    disabled,
    placeholder = '0 0 * * *'
}: CronInputProps) {
    const [isValid, setIsValid] = useState(true);
    const [explanation, setExplanation] = useState<string>('');
    const [showPresets, setShowPresets] = useState(false);
    const [showTimePicker, setShowTimePicker] = useState(false);
    const [selectedHour, setSelectedHour] = useState<string>('00');
    const [selectedMinute, setSelectedMinute] = useState<string>('00');

    // Validate cron expression and generate explanation
    useEffect(() => {
        try {
            if (value) {
                const explanation = cronstrue.toString(value, { verbose: true });
                setExplanation(explanation);
                setIsValid(true);
            } else {
                setExplanation('');
                setIsValid(true);
            }
        } catch (error) {
            setIsValid(false);
            setExplanation('Invalid cron expression');
        }
    }, [value, onValidityChange]);

    // Handle time selection
    const handleTimeSelect = () => {
        const cronExpression = `${selectedMinute} ${selectedHour} * * *`;
        onChange(cronExpression);
        setShowTimePicker(false);
    };

    // Handle preset selection
    const handlePresetSelect = (preset: string) => {
        onChange(preset);
        setShowPresets(false);
    };

    return (
        <div className={cn('space-y-2', className)}>
            <div className="flex gap-2">
                <div className="relative flex-1">
                    <Input
                        value={value}
                        onChange={(e) => onChange(e.target.value)}
                        placeholder={placeholder}
                        disabled={disabled}
                        className={cn(
                            'pr-8',
                            !isValid && 'border-destructive focus-visible:ring-destructive'
                        )}
                    />
                    <Popover open={showTimePicker} onOpenChange={setShowTimePicker}>
                        <PopoverTrigger asChild>
                            <Button
                                variant="ghost"
                                size="icon"
                                className="absolute right-0 top-0 h-full px-3 hover:bg-transparent"
                                disabled={disabled}
                            >
                                <Clock className="h-4 w-4 text-muted-foreground" />
                            </Button>
                        </PopoverTrigger>
                        <PopoverContent className="w-[200px] p-4" align="end">
                            <div className="space-y-4">
                                <div className="grid grid-cols-2 gap-2">
                                    <div className="space-y-2">
                                        <label className="text-sm font-medium">Hour</label>
                                        <Select
                                            value={selectedHour}
                                            onValueChange={setSelectedHour}
                                            disabled={disabled}
                                        >
                                            <SelectTrigger>
                                                <SelectValue />
                                            </SelectTrigger>
                                            <SelectContent>
                                                {HOURS.map((hour) => (
                                                    <SelectItem key={hour.value} value={hour.value}>
                                                        {hour.label}
                                                    </SelectItem>
                                                ))}
                                            </SelectContent>
                                        </Select>
                                    </div>
                                    <div className="space-y-2">
                                        <label className="text-sm font-medium">Minute</label>
                                        <Select
                                            value={selectedMinute}
                                            onValueChange={setSelectedMinute}
                                            disabled={disabled}
                                        >
                                            <SelectTrigger>
                                                <SelectValue />
                                            </SelectTrigger>
                                            <SelectContent>
                                                {MINUTES.map((minute) => (
                                                    <SelectItem key={minute.value} value={minute.value}>
                                                        {minute.label}
                                                    </SelectItem>
                                                ))}
                                            </SelectContent>
                                        </Select>
                                    </div>
                                </div>
                                <Button 
                                    className="w-full" 
                                    onClick={handleTimeSelect}
                                    disabled={disabled}
                                >
                                    Apply Time
                                </Button>
                            </div>
                        </PopoverContent>
                    </Popover>
                </div>
                <Popover modal open={showPresets} onOpenChange={setShowPresets}>
                    <PopoverTrigger asChild>
                        <Button 
                            variant="outline" 
                            className="flex items-center gap-2"
                            disabled={disabled}
                        >
                            Presets
                            <ChevronDown className="h-4 w-4" />
                        </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-[300px] max-h-[300px] overflow-y-auto p-0" align="end">
                        <div className="max-h-[300px] overflow-y-auto">
                            <div className="p-2 space-y-1">
                                {CRON_PRESETS.map((preset) => (
                                    <Button
                                        key={preset.value}
                                        variant="ghost"
                                        className="w-full justify-start font-normal"
                                        onClick={() => handlePresetSelect(preset.value)}
                                        disabled={disabled}
                                    >
                                        <div className="flex flex-col items-start">
                                            <span>{preset.label}</span>
                                            <span className="text-xs text-muted-foreground">
                                                {preset.value}
                                            </span>
                                        </div>
                                    </Button>
                                ))}
                            </div>
                        </div>
                    </PopoverContent>
                </Popover>
            </div>

            {value && (
                <Card className={cn(
                    'border',
                    !isValid && 'border-destructive'
                )}>
                    <CardContent className="p-3">
                        <div className="flex items-start gap-2">
                            <Badge variant={isValid ? "secondary" : "destructive"}>
                                {isValid ? "Valid" : "Invalid"}
                            </Badge>
                            <p className="text-sm text-muted-foreground">
                                {explanation}
                            </p>
                        </div>
                    </CardContent>
                </Card>
            )}

            <div className="text-xs text-muted-foreground">
                <p>Format: minute hour day month weekday</p>
                <p>Example: 0 9 * * 1-5 (Every weekday at 9 AM)</p>
            </div>
        </div>
    );
} 