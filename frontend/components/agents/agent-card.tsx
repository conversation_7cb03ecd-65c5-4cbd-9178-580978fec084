import { memo, useState } from 'react';
import Image from 'next/image';
import { AgentPublic } from '@/client';
import { ToolBadge } from './tool-badge';
import { MCPServerBadge } from './mcp-server-badge';
import { CellAction } from '@/app/(dashboard)/agents/_components/item-tables/cell-action';
import { useAgentData } from '@/app/(dashboard)/agents/_components/item-listing';
import {
  Card,
  CardContent,
  CardFooter,
  CardHeader,
} from "@/components/ui/card";
import { ResourceDialog } from '@/app/(dashboard)/agents/_components/resource-dialog';

interface AgentCardProps {
  item: AgentPublic;
}

// eslint-disable-next-line react/display-name
export const AgentCard = memo(({ item }: AgentCardProps) => {
  // Get data from context
  const { agentTools, agentMcpServerMap } = useAgentData();
  const tools = agentTools[item.id] || [];
  const mcpServers = agentMcpServerMap[item.id] || [];

  // Parse agent title to get the name
  const agentName = item.title.split(" ")[0].toLowerCase();
  const avatarPath = `/avatars/${agentName}.webp`;

  // State for ResourceDialog
  const [showResourceDialog, setShowResourceDialog] = useState(false);

  // Determine if this is a Group Chat (autonomous_agent)
  const isGroupChat = item.type === 'autonomous_agent';

  // Card click handler for Group Chat
  const handleCardClick = () => {
    if (isGroupChat) {
      setShowResourceDialog(true);
    }
  };

  return (
    <>
      {isGroupChat && (
        <ResourceDialog
          isOpen={showResourceDialog}
          onClose={() => setShowResourceDialog(false)}
          agentId={item.id}
          agentType={item.type}
        />
      )}
      <Card
        className={`flex flex-col transition-all duration-200 ${
          isGroupChat ? 'cursor-pointer' : ''
        }`}
        onClick={isGroupChat ? handleCardClick : undefined}
      >
        <CardHeader className="pb-3">
          <div className="flex items-start gap-3 min-w-0">
            <div className="flex-shrink-0">
              <div className="h-8 w-8 rounded-full overflow-hidden">
                <Image
                  src={avatarPath}
                  alt={`${agentName} Avatar`}
                  width={32}
                  height={32}
                  className="object-cover"
                  onError={(e) => {
                    const target = e.target as HTMLImageElement;
                    target.src = "/avatars/cloud.webp";
                  }}
                />
              </div>
            </div>
            <div className="flex-1 min-w-0">
              <h3 className="font-semibold text-lg tracking-tight truncate">
                {item.title}
              </h3>
            </div>
          </div>
        </CardHeader>

        <CardContent className="flex-1 pb-0">
          <p className="text-sm text-muted-foreground line-clamp-3">
            {item.description}
          </p>
        </CardContent>

        <CardFooter className="flex flex-col gap-4">
          {((tools.length > 0) || (mcpServers.length > 0)) && (
            <div className="w-full space-y-3 pt-5">
              {tools.length > 0 && (
                <div className="flex gap-2 flex-wrap">
                  {tools.map((tool) => (
                    <ToolBadge key={tool.id} tool={tool} />
                  ))}
                </div>
              )}
              {mcpServers.length > 0 && (
                <div className="flex gap-2 flex-wrap">
                  {mcpServers.map((server) => (
                    <MCPServerBadge key={server.name} server={server} />
                  ))}
                </div>
              )}
            </div>
          )}

          <div className="flex items-center justify-between w-full gap-2">
            <div className="flex items-center gap-2">
            </div>
            {/* Only show CellAction for Team Members (conversation_agent) */}
            {!isGroupChat && <CellAction data={item} />}
          </div>
        </CardFooter>
      </Card>
    </>
  );
});
