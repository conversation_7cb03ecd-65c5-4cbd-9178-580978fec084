// components/marketing/BrandingSection.tsx
import { FC } from "react";
import Image from "next/image";
import { cn } from "@/lib/utils";

interface BrandingSectionProps {
    className?: string;
    logo?: React.ReactNode;
    title: string;
    description: string;
    image: {
        src: string;
        alt: string;
    };
    footer?: React.ReactNode;
}

export const BrandingSection: FC<BrandingSectionProps> = ({
    className,
    logo,
    title,
    description,
    image,
    footer,
}) => {
    // Default logo with responsive sizing
    const defaultLogo = (
        <div className="flex items-center gap-2 sm:gap-3 group">
            <div className="relative">
                <Image
                    src="/logo.svg"
                    alt="CloudThinker Logo"
                    width={48}
                    height={32}
                    className="h-6 w-auto sm:h-7 md:h-8 transition-transform duration-300 group-hover:scale-105"
                    priority
                />
            </div>
            <span className="text-lg sm:text-xl font-semibold tracking-tight text-white">
                CloudThinker
            </span>
        </div>
    );

    const defaultFooter = (
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between w-full gap-2 sm:gap-4">
            <p className="text-xs sm:text-sm text-white/70 font-medium">
                CloudThinker © 2025
            </p>
        </div>
    );

    return (
        <div
            className={cn(
                "relative hidden h-screen flex-col text-white lg:flex overflow-hidden",
                // Matching the landing page color theme - dark blue/teal gradient
                "bg-gradient-to-br from-slate-950 via-blue-950 to-slate-900",
                // Enhanced overlay for better text contrast
                "before:absolute before:inset-0",
                "before:bg-gradient-to-br before:from-blue-950/40 before:via-teal-950/20 before:to-slate-950/60",
                className
            )}
        >
            {/* Animated background pattern matching landing page style */}
            <div className="absolute inset-0 opacity-[0.02]">
                <div
                    className="absolute inset-0"
                    style={{
                        backgroundImage: `radial-gradient(circle at 1px 1px, rgba(255,255,255,0.3) 1px, transparent 0)`,
                        backgroundSize: '24px 24px'
                    }}
                />
            </div>

            {/* Floating gradient orbs for depth - matching landing page aesthetic */}
            <div className="absolute inset-0 overflow-hidden pointer-events-none">
                <div className="absolute top-10 sm:top-20 right-10 sm:right-20 w-24 h-24 sm:w-32 sm:h-32 bg-gradient-to-br from-teal-400/20 to-blue-400/10 rounded-full blur-xl animate-pulse" />
                <div
                    className="absolute bottom-20 sm:bottom-40 left-10 sm:left-20 w-16 h-16 sm:w-24 sm:h-24 bg-gradient-to-br from-blue-400/15 to-teal-400/10 rounded-full blur-lg animate-pulse"
                    style={{ animationDelay: '2s' }}
                />
                <div
                    className="absolute top-1/2 right-1/4 w-12 h-12 sm:w-16 sm:h-16 bg-gradient-to-br from-cyan-400/10 to-blue-400/5 rounded-full blur-md animate-pulse"
                    style={{ animationDelay: '4s' }}
                />
            </div>

            {/* Header with responsive logo - fixed height */}
            <header className="relative z-20 flex-shrink-0 p-4 sm:p-6 lg:p-8">
                <div className="flex items-center">
                    {logo || defaultLogo}
                </div>
            </header>

            {/* Main content with constrained height to ensure footer visibility */}
            <main className="relative z-20 flex-1 flex flex-col items-center justify-center px-4 sm:px-6 lg:px-8 py-4 sm:py-6 min-h-0">
                {/* Hero content with improved typography and spacing */}
                <div className="text-center max-w-full mx-auto space-y-6 sm:space-y-8 lg:space-y-10">
                    <div className="space-y-4 sm:space-y-6 lg:space-y-8">
                        <h1 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl xl:text-6xl font-bold tracking-tight leading-[1.1] px-2 max-w-5xl mx-auto">
                            <span className="block bg-gradient-to-r from-white via-blue-100 to-cyan-200 bg-clip-text text-transparent">
                                {title}
                            </span>
                        </h1>

                        <div className="max-w-xs sm:max-w-lg md:max-w-xl lg:max-w-3xl mx-auto">
                            <p className="text-sm sm:text-base md:text-lg lg:text-xl text-white/90 leading-relaxed font-light px-4 sm:px-6">
                                {description}
                            </p>
                        </div>
                    </div>
                </div>

                {/* Product showcase with clean, borderless design */}
                <div className="w-full max-w-sm sm:max-w-3xl md:max-w-4xl lg:max-w-5xl xl:max-w-6xl mx-auto mt-6 sm:mt-8 lg:mt-12 flex-1 min-h-0 flex items-center">
                    <div className="relative group w-full">
                        {/* Clean hero banner image without decorative elements */}
                        <div className="relative overflow-hidden rounded-lg sm:rounded-xl lg:rounded-2xl">
                            <Image
                                src={image.src}
                                alt={image.alt}
                                width={1200}
                                height={800}
                                className={cn(
                                    "w-full h-auto",
                                    "transition-all duration-500 ease-out",
                                    "group-hover:scale-[1.01] sm:group-hover:scale-[1.02]",
                                    "shadow-2xl shadow-black/30"
                                )}
                                style={{
                                    objectFit: "contain",
                                    maxHeight: "calc(100vh - 180px)",
                                }}
                                priority
                                quality={95}
                            />

                            {/* Subtle overlay for depth */}
                            <div className="absolute inset-0 bg-gradient-to-t from-black/5 via-transparent to-transparent pointer-events-none rounded-lg sm:rounded-xl lg:rounded-2xl" />
                        </div>
                    </div>
                </div>
            </main>

            {/* Footer with guaranteed visibility - fixed height */}
            <footer className="relative z-20 flex-shrink-0 p-4 sm:p-6 lg:p-8 mt-auto">
                {footer || defaultFooter}
            </footer>
        </div>
    );
};
