'use client';

import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger
} from '@/components/ui/collapsible';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuSub,
  SidebarMenuSubButton,
  SidebarMenuSubItem,
  SidebarRail
} from '@/components/ui/sidebar';
import {
  workspaceItems,
  configurationItems,
  billingItems
} from '@/constants/data';
import {
  BadgeCheck,
  Bell,
  ChevronRight,
  ChevronsUpDown,
  CreditCard,
  GalleryVerticalEnd,
  LogOut,
  Loader2,
  Lock,
  User
} from 'lucide-react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import * as React from 'react';
import { Icons } from '../icons';
import useAuth from '@/hooks/useAuth';
import { CloudThinkerLogo } from '../ui/logo';
import { useSubscription } from '@/lib/hooks/useSubscription';
import { Skeleton } from '@/components/ui/skeleton';
import { useMemo, useState, useEffect } from 'react';
import { NavItem } from '@/types';
import { useRouter } from 'next/navigation';
import { PagePath } from '@/constants/route';
import useSaasEnabled from '@/hooks/useSaasEnabled';
import clientCookie from 'js-cookie';
import { UserInfo } from '@/types/common.enum';
import { WorkspaceSelector } from './workspace-selector';
import { useSidebar } from '@/components/ui/sidebar';

export const company = {
  name: 'Cloud Thinker'
};

export default function AppSidebar() {
  const { user, logout } = useAuth();
  const pathname = usePathname();
  const { activeSubscription, hasActiveSubscription, isLoading, isFetched } =
    useSubscription();
  const router = useRouter();
  const currentWorkspaceId = clientCookie.get(UserInfo.WorkspacesID);
  const { setOpen, state } = useSidebar();

  // State to track which collapsible items are open
  const [openItems, setOpenItems] = useState<Record<string, boolean>>({});



  // Check if user is an invited member of the current workspace
  const isInvitedUser = user && currentWorkspaceId && user.workspaces?.some(w =>
    w.id === currentWorkspaceId && !user.own_workspaces?.some(ow => ow.id === currentWorkspaceId)
  );

  // SaaS feature flag
  const { isEnabled: saasEnabled } = useSaasEnabled();

  // Memoize the plan name display element to prevent unnecessary re-renders
  const planNameDisplay = useMemo(() => {
    // Only show plan name if SaaS is enabled
    if (!saasEnabled) return 'Community';

    if (isLoading || !isFetched) {
      return <Skeleton className="h-4 w-16" />;
    }
    return activeSubscription?.product_name || 'Starter';
  }, [isLoading, activeSubscription, isFetched, saasEnabled]);

  // Memoize the configuration items to include billing and subscription only when SaaS is enabled
  const mergedConfigItems = useMemo(() => {
    // Hide configuration items for invited users
    if (isInvitedUser) return [];

    if (!saasEnabled) return configurationItems;

    // Create a new array with spread operator instead of deep cloning
    // This preserves the structure while allowing us to modify it
    const mergedItems = configurationItems.map((item) => ({
      ...item,
      items: item.items ? [...item.items] : []
    }));

    // Add billing items to the first configuration item's subitems
    if (mergedItems.length > 0 && mergedItems[0].items) {
      mergedItems[0].items = [...mergedItems[0].items, ...billingItems];
    }

    return mergedItems;
  }, [saasEnabled, isInvitedUser]);

  // Filter out the Manage section for invited users
  const filteredWorkspaceItems = useMemo(() => {
    if (isInvitedUser) {
      return workspaceItems.filter(item => item.title !== 'Manage');
    }
    return workspaceItems;
  }, [isInvitedUser]);

  // Initialize open items based on active state
  useEffect(() => {
    const initialOpenItems: Record<string, boolean> = {};

    // Set workspace items
    filteredWorkspaceItems.forEach((item) => {
      if (item.items && item.items.length > 0) {
        const isSomeChildActive = item.items.some((subItem) =>
          pathname?.startsWith(subItem.url)
        );
        initialOpenItems[item.title] = item.isActive || isSomeChildActive;
      }
    });

    // Set config items
    mergedConfigItems.forEach((item) => {
      if (item.items && item.items.length > 0) {
        const isSomeChildActive = item.items.some((subItem) =>
          pathname.startsWith(subItem.url)
        );
        initialOpenItems[item.title] = item.isActive || isSomeChildActive;
      }
    });

    setOpenItems(initialOpenItems);
  }, [pathname, filteredWorkspaceItems, mergedConfigItems]);

  return (
    <div className="group">
      <Sidebar collapsible="icon">
        <SidebarHeader>
          <div className="flex gap-2 py-2 text-sidebar-accent-foreground ">
            <div className="flex aspect-square size-8 items-center justify-center rounded-lg">
              <CloudThinkerLogo />
            </div>
            <div className="grid flex-1 text-left text-sm leading-tight">
              <span className="truncate font-semibold">{company.name}</span>
              {planNameDisplay && (
                <span className="truncate text-xs">{planNameDisplay}</span>
              )}
            </div>
          </div>

          {/* Workspace Selector */}
          <WorkspaceSelector />
        </SidebarHeader>

        <SidebarContent className="overflow-x-hidden">
          <SidebarGroup>
            <SidebarGroupLabel>Workspace</SidebarGroupLabel>
            <SidebarMenu>
              {filteredWorkspaceItems.map((item) => {
                const Icon = item.icon ? Icons[item.icon] : Icons.logo;
                const isSomeChildActive = item.items?.some((subItem) =>
                  pathname?.startsWith(subItem.url)
                );

                return item?.items && item?.items?.length > 0 ? (
                  <Collapsible
                    key={item.title}
                    asChild
                    open={openItems[item.title] || false}
                    onOpenChange={(open) => {
                      setOpenItems(prev => ({ ...prev, [item.title]: open }));
                    }}
                    className="group/collapsible"
                  >
                    <SidebarMenuItem>
                      <CollapsibleTrigger asChild>
                        <SidebarMenuButton
                          tooltip={item.title}
                          isActive={pathname === item.url}
                          onClick={(e) => {
                            // Open sidebar and expand collapsible when clicking on items with sub-items
                            if (state === 'collapsed') {
                              e.preventDefault(); // Prevent default collapsible toggle
                              setOpen(true);
                              setOpenItems(prev => ({ ...prev, [item.title]: true }));
                            }
                          }}
                        >
                          {item.icon && <Icon />}
                          <span>{item.title}</span>
                          <ChevronRight className="ml-auto transition-transform duration-200 group-data-[state=open]/collapsible:rotate-90" />
                        </SidebarMenuButton>
                      </CollapsibleTrigger>
                      <CollapsibleContent>
                        <SidebarMenuSub>
                          {item.items?.map((subItem) => (
                            <SidebarMenuSubItem key={subItem.title}>
                              <SidebarMenuSubButton
                                asChild
                                isActive={pathname.startsWith(subItem.url)}
                              >
                                <Link href={subItem.url}>
                                  <span>{subItem.title}</span>
                                </Link>
                              </SidebarMenuSubButton>
                            </SidebarMenuSubItem>
                          ))}
                        </SidebarMenuSub>
                      </CollapsibleContent>
                    </SidebarMenuItem>
                  </Collapsible>
                ) : (
                  <SidebarMenuItem key={item.title}>
                    <SidebarMenuButton
                      asChild
                      tooltip={item.title}
                      isActive={pathname === item.url}
                    >
                      <Link href={item.url}>
                        <Icon />
                        <span>{item.title}</span>
                      </Link>
                    </SidebarMenuButton>
                  </SidebarMenuItem>
                );
              })}
            </SidebarMenu>
          </SidebarGroup>
          <SidebarGroup>
            <div className="border-sidebar-separator my-2 border-t" />
            <SidebarMenu>
              {mergedConfigItems.map((item: NavItem) => {
                const Icon = item.icon
                  ? Icons[item.icon as keyof typeof Icons]
                  : Icons.logo;
                const isSomeChildActive = item.items?.some((subItem: NavItem) =>
                  pathname.startsWith(subItem.url)
                );

                return item?.items && item?.items?.length > 0 ? (
                  <Collapsible
                    key={item.title}
                    asChild
                    open={openItems[item.title] || false}
                    onOpenChange={(open) => {
                      setOpenItems(prev => ({ ...prev, [item.title]: open }));
                    }}
                    className="group/collapsible"
                  >
                    <SidebarMenuItem>
                      <CollapsibleTrigger asChild>
                        <SidebarMenuButton
                          tooltip={item.title}
                          isActive={pathname === item.url}
                          onClick={(e) => {
                            // Open sidebar and expand collapsible when clicking on items with sub-items
                            if (state === 'collapsed') {
                              e.preventDefault(); // Prevent default collapsible toggle
                              setOpen(true);
                              setOpenItems(prev => ({ ...prev, [item.title]: true }));
                            }
                          }}
                        >
                          {item.icon && <Icon />}
                          <span>{item.title}</span>
                          <ChevronRight className="ml-auto transition-transform duration-200 group-data-[state=open]/collapsible:rotate-90" />
                        </SidebarMenuButton>
                      </CollapsibleTrigger>
                      <CollapsibleContent>
                        <SidebarMenuSub>
                          {item.items?.map((subItem: NavItem) => (
                            <SidebarMenuSubItem key={subItem.title}>
                              <SidebarMenuSubButton
                                asChild
                                isActive={pathname.startsWith(subItem.url)}
                              >
                                <Link href={subItem.url}>
                                  <span>{subItem.title}</span>
                                </Link>
                              </SidebarMenuSubButton>
                            </SidebarMenuSubItem>
                          ))}
                        </SidebarMenuSub>
                      </CollapsibleContent>
                    </SidebarMenuItem>
                  </Collapsible>
                ) : (
                  <SidebarMenuItem key={item.title}>
                    <SidebarMenuButton
                      asChild
                      tooltip={item.title}
                      isActive={pathname === item.url}
                    >
                      <Link href={item.url}>
                        <Icon />
                        <span>{item.title}</span>
                      </Link>
                    </SidebarMenuButton>
                  </SidebarMenuItem>
                );
              })}
            </SidebarMenu>
          </SidebarGroup>
        </SidebarContent>
        <SidebarFooter>
          <SidebarMenu>
            {/* Only show subscription/upgrade section if SaaS is enabled and user doesn't have an active subscription */}
            {saasEnabled && isFetched && !isLoading && !hasActiveSubscription && (
              <SidebarMenuItem>
                <div className="px-2 group-data-[collapsible=icon]:hidden">
                  <div className="mb-2 space-y-1">
                    <h4 className="text-sm font-semibold">Starter Plan</h4>
                    <p className="text-xs text-muted-foreground">
                      Upgrade to unlock more features
                    </p>
                  </div>
                  <div className="mb-2 h-1.5 w-full rounded-full bg-muted">
                    <div
                      className="h-1.5 rounded-full bg-primary"
                      style={{ width: '15%' }}
                    />
                  </div>
                </div>

                <div className="px-2 group-data-[collapsible=icon]:px-0">
                  <SidebarMenuButton
                    asChild
                    className="bg-primary text-primary-foreground hover:bg-primary/90"
                  >
                    <Link
                      href={PagePath.Purchase}
                      className="flex items-center justify-center gap-2"
                    >
                      <GalleryVerticalEnd />
                      <span className="group-data-[collapsible=icon]:hidden">
                        Upgrade Now
                      </span>
                    </Link>
                  </SidebarMenuButton>
                </div>
              </SidebarMenuItem>
            )}

            {/* Show loading state while fetching subscription data (only if SaaS is enabled) */}
            {saasEnabled && (isLoading || !isFetched) && (
              <SidebarMenuItem>
                <div className="flex items-center space-x-2 px-2 group-data-[collapsible=icon]:hidden">
                  <Loader2 className="h-4 w-4 animate-spin" />
                  <span className="text-xs">Loading subscription...</span>
                </div>
              </SidebarMenuItem>
            )}

            <SidebarMenuItem>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <SidebarMenuButton
                    size="lg"
                    className="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground"
                  >
                    <Avatar className="h-8 w-8 rounded-lg">
                      {user?.avatar_url ? (
                        <AvatarImage src={user.avatar_url} alt={user?.full_name || 'User'} className="rounded-lg" />
                      ) : null}
                      <AvatarFallback className="rounded-lg">
                        {user?.full_name?.slice(0, 2)?.toUpperCase() || 'U'}
                      </AvatarFallback>
                    </Avatar>
                    <div className="grid flex-1 text-left text-sm leading-tight">
                      <span className="truncate font-semibold">
                        {user?.full_name || ''}
                      </span>
                      <span className="truncate text-xs">
                        {user?.email || ''}
                      </span>
                    </div>
                    <ChevronsUpDown className="ml-auto size-4" />
                  </SidebarMenuButton>
                </DropdownMenuTrigger>
                <DropdownMenuContent
                  className="w-[--radix-dropdown-menu-trigger-width] min-w-56 rounded-lg"
                  side="bottom"
                  align="end"
                  sideOffset={4}
                >
                  <DropdownMenuLabel className="p-0 font-normal">
                    <div className="flex items-center gap-2 px-1 py-1.5 text-left text-sm">
                      <Avatar className="h-8 w-8 rounded-lg">
                        {user?.avatar_url ? (
                          <AvatarImage src={user.avatar_url} alt={user?.full_name || 'User'} className="rounded-lg" />
                        ) : null}
                        <AvatarFallback className="rounded-lg">
                          {user?.full_name?.slice(0, 2)?.toUpperCase() || 'U'}
                        </AvatarFallback>
                      </Avatar>
                      <div className="grid flex-1 text-left text-sm leading-tight">
                        <span className="truncate font-semibold">
                          {user?.full_name || ''}
                        </span>
                        <span className="truncate text-xs">
                          {' '}
                          {user?.email || ''}
                        </span>
                      </div>
                    </div>
                  </DropdownMenuLabel>
                  <DropdownMenuSeparator />

                  <DropdownMenuGroup>
                    <DropdownMenuLabel>Account</DropdownMenuLabel>
                    <DropdownMenuItem
                      onClick={() => router.push(PagePath.Profile)}
                      className="flex items-center gap-2"
                    >
                      <User className="h-4 w-4" />
                      Profile
                    </DropdownMenuItem>
                    <DropdownMenuItem
                      onClick={() => router.push(PagePath.Password)}
                      className="flex items-center gap-2"
                    >
                      <Lock className="h-4 w-4" />
                      Password
                    </DropdownMenuItem>
                  </DropdownMenuGroup>
                  <DropdownMenuSeparator />
                  {saasEnabled && !isInvitedUser && (
                    <DropdownMenuItem
                      onClick={() => router.push(PagePath.Billing)}
                      className="flex items-center gap-2"
                    >
                      <CreditCard className="h-4 w-4" />
                      Billing
                    </DropdownMenuItem>
                  )}
                  {saasEnabled && !isInvitedUser && (
                    <DropdownMenuItem
                      onClick={() => router.push(PagePath.Subscription)}
                      className="flex items-center gap-2"
                    >
                      <BadgeCheck className="h-4 w-4" />
                      Subscription
                    </DropdownMenuItem>
                  )}
                  <DropdownMenuItem
                    onClick={logout}
                    className="flex items-center gap-2"
                  >
                    <LogOut className="h-4 w-4" />
                    Log out
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </SidebarMenuItem>
          </SidebarMenu>
        </SidebarFooter>
        <SidebarRail />
      </Sidebar>
    </div>
  );
}
