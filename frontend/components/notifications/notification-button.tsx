'use client';

import React, { useState, useEffect, useCallback, useMemo, useRef } from 'react';
import {
  <PERSON>,
  Check,
  CheckCheck,
  AlertTriangle,
  AlertCircle,
  Info,
  MessageSquare,
  CheckCircle2,
  Loader2,
  Calendar,
  CalendarDays,
  CalendarClock,
  Archive,
  ExternalLink,
  AlertOctagon,
} from 'lucide-react';
import { format, formatDistanceToNow } from 'date-fns';
import { Button } from '@/components/ui/button';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';
import { useNotifications, Notification, NotificationFilter } from '@/hooks/use-notifications';
import type { NotificationType } from '@/client/types.gen';
import type { Alert } from '@/app/(dashboard)/alerts/_components/types';
import { usePathname, useRouter } from 'next/navigation';
import {
  Tabs,
  TabsContent,
  Ta<PERSON>List,
  TabsTrigger,
} from "@/components/ui/tabs";

// Format time for notifications
const formatTime = (dateString: string | undefined) => {
  if (!dateString) return 'Unknown date';

  const date = new Date(dateString);
  const now = new Date();

  // For dates within the last 24 hours, show relative time
  if (Math.abs(now.getTime() - date.getTime()) < 24 * 60 * 60 * 1000) {
    return formatDistanceToNow(date, { addSuffix: true });
  }
  // For dates on the current year, show month and day
  else if (date.getFullYear() === now.getFullYear()) {
    return format(date, 'MMM d');
  }
  // For older dates, show full date
  else {
    return format(date, 'MMM d, yyyy');
  }
};

// Get icon based on notification type
const getNotificationIcon = (type: NotificationType | undefined) => {
  switch (type) {
    case 'error':
      return <AlertCircle className="h-4 w-4 text-destructive" />;
    case 'warning':
      return <AlertTriangle className="h-4 w-4 text-amber-500" />;
    case 'interrupt':
      return <MessageSquare className="h-4 w-4 text-primary" />;
    case 'info':
      return <Info className="h-4 w-4 text-blue-500" />;
    // Add these cases only if they exist in NotificationType
    // If not in the type, handle them in default
    default:
      return <Info className="h-4 w-4 text-blue-500" />;
  }
};

// Get status icon based on notification status and metadata
const getStatusIcon = (notification: Notification) => {
  const isUnread = notification.status === 'unread';

  if (isUnread) {
    return <div className="h-2 w-2 rounded-full bg-primary shrink-0" aria-label="Unread" />;
  } else {
    return <CheckCircle2 className="h-4 w-4 text-muted-foreground/50 shrink-0" aria-label="Read" />;
  }
};

// Get time group icon
const getTimeGroupIcon = (group: string) => {
  switch (group) {
    case 'today':
      return <Calendar className="h-4 w-4 text-primary" />;
    case 'yesterday':
      return <CalendarDays className="h-4 w-4 text-blue-500" />;
    case 'thisWeek':
      return <CalendarClock className="h-4 w-4 text-green-500" />;
    case 'thisMonth':
      return <CalendarClock className="h-4 w-4 text-amber-500" />;
    case 'older':
      return <Archive className="h-4 w-4 text-muted-foreground" />;
    default:
      return <Calendar className="h-4 w-4" />;
  }
};

// Format group title
const formatGroupTitle = (group: string): string => {
  switch (group) {
    case 'today':
      return 'Today';
    case 'yesterday':
      return 'Yesterday';
    case 'thisWeek':
      return 'This Week';
    case 'thisMonth':
      return 'This Month';
    case 'older':
      return 'Older';
    default:
      return group;
  }
};

// Add alert severity icons
const getAlertSeverityIcon = (severity: 'critical' | 'high' | 'medium' | 'low') => {
  switch (severity) {
    case 'critical':
      return <AlertOctagon className="h-4 w-4 text-destructive" />;
    case 'high':
      return <AlertTriangle className="h-4 w-4 text-amber-500" />;
    case 'medium':
      return <AlertCircle className="h-4 w-4 text-blue-500" />;
    case 'low':
      return <Info className="h-4 w-4 text-muted-foreground" />;
    default:
      return <Info className="h-4 w-4" />;
  }
};

// Add alert status badge
const getAlertStatusBadge = (status: 'triggered' | 'acknowledged' | 'resolved') => {
  switch (status) {
    case 'triggered':
      return <Badge variant="destructive" className="text-xs">Triggered</Badge>;
    case 'acknowledged':
      return <Badge variant="secondary" className="text-xs">Acknowledged</Badge>;
    case 'resolved':
      return <Badge variant="outline" className="text-xs">Resolved</Badge>;
    default:
      return null;
  }
};

export function NotificationButton() {
  const [open, setOpen] = useState(false);
  const [activeTab, setActiveTab] = useState('all');
  const [isFilterOpen, setIsFilterOpen] = useState(false);
  const router = useRouter();

  // Simple ref for the scroll area
  const scrollAreaRef = useRef<HTMLDivElement>(null);

  const {
    notifications,
    groupedNotifications,
    totalCount,
    unreadCount,
    allNotificationsCount,
    loading,
    isFetching,
    error,
    markAsRead,
    markAllAsRead,
    handleInterruptAction,
    fetchNotifications,
    loadMore,
    paginationParams,
    activeFilters,
    setActiveFilters,
    refetch,
    saveScrollPosition,
    restoreScrollPosition,
    alerts,
    isAlertsLoading
  } = useNotifications();

  // Reset active tab when popover closes
  useEffect(() => {
    if (!open) {
      setActiveTab('all');
      setIsFilterOpen(false);
    }
  }, [open]);

  // Get current pathname for detecting navigation
  const pathname = usePathname();

  // Refresh notifications when pathname changes (tab switch, breadcrumb navigation)
  useEffect(() => {
    if (refetch) {
      refetch();
    }
  }, [pathname, refetch]);

  // Refresh notifications when the page becomes visible again (tab switch, reload)
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.visibilityState === 'visible' && refetch) {
        refetch();
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [refetch]);

  // Simple load more function
  const handleLoadMore = useCallback(() => {
    if (!loading && !isFetching) {
      loadMore();
    }
  }, [loading, isFetching, loadMore]);

  // Handle tab change
  const handleTabChange = useCallback((value: string) => {
    setActiveTab(value);

    // Create a new filter object based on the tab
    let newFilters: Partial<NotificationFilter> = {
      ...activeFilters
    };

    // Update filters based on tab
    if (value === 'all') {
      newFilters.status = ['unread', 'read'];
      newFilters.requiresAction = undefined;
    } else if (value === 'unread') {
      newFilters.status = ['unread'];
      newFilters.requiresAction = undefined;
    } else if (value === 'action') {
      newFilters.status = ['unread', 'read'];
      newFilters.requiresAction = true;
    }

    // Update active filters state
    setActiveFilters(newFilters);

    // Prepare API parameters
    const apiParams: any = {
      skip: 0,
      status: newFilters.status,
      requiresAction: newFilters.requiresAction
    };

    // Include type filter if present
    if (newFilters.type && newFilters.type.length > 0) {
      apiParams.type = newFilters.type;
    }

    // Fetch notifications with updated filters
    fetchNotifications(apiParams);
  }, [fetchNotifications, activeFilters, setActiveFilters]);

  // Apply filter
  const applyFilter = useCallback((filter: Partial<NotificationFilter>) => {
    // Create a complete filter object based on the current active filters and the new filter
    const updatedFilter = {
      ...activeFilters,
      ...filter,
      skip: 0
    };

    // Update the active filters state
    setActiveFilters(updatedFilter);

    // Prepare parameters for the API call
    const apiParams: any = {
      skip: 0,
      status: updatedFilter.status || ['unread', 'read'],
      requiresAction: updatedFilter.requiresAction
    };

    // Handle type filter separately
    if (updatedFilter.type && updatedFilter.type.length > 0) {
      apiParams.type = updatedFilter.type;
    }

    // Apply timeframe filter if present
    if (updatedFilter.timeframe && updatedFilter.timeframe !== 'all') {
      // We don't need to pass timeframe to the API as it's handled by the hook
      // But we keep it in the activeFilters state for UI purposes
    }

    // Fetch notifications with the updated filters
    fetchNotifications(apiParams);
    setIsFilterOpen(false);
  }, [fetchNotifications, activeFilters, setActiveFilters]);

  // Reset filters
  const resetFilters = useCallback(() => {
    // Reset active filters state
    setActiveFilters({
      status: ['unread', 'read'],
      type: undefined,
      requiresAction: undefined,
      timeframe: 'all'
    });

    // Fetch notifications with default parameters
    fetchNotifications({
      skip: 0,
      status: ['unread', 'read'],
      type: undefined,
      requiresAction: undefined
    });

    setActiveTab('all');
    setIsFilterOpen(false);
  }, [fetchNotifications, setActiveFilters]);

  // Optimize the renderNotification function with useCallback
  const renderNotification = useCallback((notification: Notification) => {
    const isUnread = notification.status === 'unread';
    const requiresAction = notification.requires_action;

    const getBorderColor = () => {
      if (notification.type === 'error') return 'border-l-destructive';
      if (notification.type === 'warning') return 'border-l-amber-500';
      if (notification.type === 'interrupt') return 'border-l-primary';
      return 'border-l-blue-500';
    };

    const handleNavigation = (e?: React.MouseEvent) => {
      if (e) e.stopPropagation(); // Prevent other click handlers if present
      if (notification.action_url) {
        setOpen(false); // Close notification popover
        router.push(notification.action_url);
      }
    };

    const handleMarkAsReadToggle = (e: React.MouseEvent) => {
      e.stopPropagation(); // Prevent navigation
      saveScrollPosition();
      if (isUnread) {
        markAsRead.mutate(notification.id);
      }
    };

    // Simplified action button
    const renderActionButton = () => {
      if (!notification.action_url) return null;

      return (
        <Button
          variant="ghost"
          size="sm"
          className="h-7 px-2 text-xs text-primary hover:bg-primary/10"
          onClick={handleNavigation} // Changed from handleActionClick
        >
          View <ExternalLink className="ml-1 h-3 w-3" />
        </Button>
      );
    };

    // Extract operation from metadata if available
    const getOperationDetails = () => {
      const metadata = notification.notification_metadata || {};
      const operation = metadata.operation as string | undefined;

      if (operation) {
        return operation;
      }
      return null;
    };

    // Check if this is a permission notification
    const isPermissionNotification = notification.message?.includes('🔒') ||
                                    notification.title?.includes('Permission') ||
                                    notification.title?.includes('Action Required');

    const operation = getOperationDetails();

    return (
      <div
        key={notification.id}
        className={cn(
          'relative group flex flex-col p-3 transition-colors border-l-4 rounded-xl my-1 mx-1 shadow-sm hover:bg-muted/40',
          getBorderColor(),
          isUnread ? 'bg-muted/20' : 'bg-background'
        )}
        // Main click navigates if action_url is present
        onClick={notification.action_url ? handleNavigation : undefined}
        role="button"
        tabIndex={notification.action_url ? 0 : -1}
        onKeyDown={notification.action_url ? (e) => e.key === 'Enter' && handleNavigation() : undefined}
        aria-labelledby={`notification-title-${notification.id}`}
        aria-describedby={`notification-message-${notification.id}`}
      >
        {/* Mark as Read Button - positioned top-right */}
        {isUnread && (
          <Button
            variant="ghost"
            size="icon"
            className={cn(
              "absolute top-1 right-1 h-6 w-6 text-muted-foreground hover:text-foreground",
              "opacity-0 group-hover:opacity-100 focus:opacity-100 transition-opacity",
              "hover:bg-blue-500/10"
            )}
            onClick={handleMarkAsReadToggle}
            aria-label="Mark as read"
          >
            <Check className="h-3.5 w-3.5" />
          </Button>
        )}

        {isPermissionNotification ? (
          /* Compact Permission Notification Layout */
          <>
            <div className="flex items-center justify-between mb-1.5">
              <div className="flex items-center gap-2">
                {getStatusIcon(notification)}
                <span className="text-xs text-muted-foreground whitespace-nowrap">
                  {formatTime(notification.created_at)}
                </span>
              </div>
              <div className="flex items-center gap-1">
                <AlertTriangle className="h-3.5 w-3.5 text-amber-500" />
                <span className="text-xs font-semibold text-amber-500">Action Required</span>
              </div>
            </div>

            <div className="flex items-start gap-2 mb-1">
              <div className="flex-shrink-0 mt-0.5">
                {getNotificationIcon(notification.type)}
              </div>
              <div className="flex-1 pr-6"> {/* Added padding right to avoid overlap with action button */}
                <p id={`notification-message-${notification.id}`} className="text-sm text-muted-foreground">
                  {notification.message.replace('🔒', '')}
                  {operation && <span className="font-medium"> • {operation}</span>}
                </p>
              </div>
            </div>

            {notification.action_url && (
              <div className="flex justify-end mt-1">
                {renderActionButton()}
              </div>
            )}
          </>
        ) : (
          /* Standard Notification Layout */
          <>
            <div className="flex items-center justify-between mb-1.5">
              <div className="flex items-center gap-2">
                {getStatusIcon(notification)}
                <span className="text-xs text-muted-foreground whitespace-nowrap">
                  {formatTime(notification.created_at)}
                </span>
              </div>
              <div className="flex items-center justify-center w-5 h-5 rounded-full bg-muted/80 shrink-0">
                {getNotificationIcon(notification.type)}
              </div>
            </div>

            <div className="mb-1 pr-6"> {/* Added padding right to avoid overlap with action button */}
              <h4 id={`notification-title-${notification.id}`} className="text-sm font-semibold mb-0.5">{notification.title}</h4>
              <p id={`notification-message-${notification.id}`} className="text-sm text-muted-foreground">{notification.message}</p>
            </div>

            {notification.action_url && (
              <div className="flex justify-end mt-1">
                {renderActionButton()}
              </div>
            )}
          </>
        )}
      </div>
    );
  }, [markAsRead, saveScrollPosition, router, setOpen]);

  // Render notifications grouped by time
  const renderGroupedNotifications = useCallback(() => {
    // Get groups that have notifications
    const nonEmptyGroups = Object.entries(groupedNotifications)
      .filter(([_, notifications]) => notifications.length > 0);

    if (nonEmptyGroups.length === 0) {
      return (
        <div className="flex items-center justify-center py-8 animate-in fade-in slide-in-from-bottom-2 duration-300">
          <span className="text-sm text-muted-foreground">
            No notifications
          </span>
        </div>
      );
    }

    return nonEmptyGroups.map(([group, groupNotifications]) => (
      <div key={group} className="mb-3"> {/* Increased mb */}
        <div className="flex items-center gap-1.5 px-2 py-1.5 mb-1.5 sticky top-0 bg-popover z-10 border-b border-border"> {/* Added sticky, bg, z-index, border */}
          {getTimeGroupIcon(group)}
          <h5 className="text-xs font-semibold text-muted-foreground tracking-wide uppercase">
            {formatGroupTitle(group)}
          </h5>
          <span className="ml-auto text-[10px] font-medium text-muted-foreground bg-muted px-1.5 py-0.5 rounded-full">
            {groupNotifications.length}
          </span>
        </div>
        {groupNotifications.map(renderNotification)}
      </div>
    ));
  }, [groupedNotifications, renderNotification]);

  // Add alert rendering function
  const renderAlert = useCallback((alert: Alert) => {
    console.log(alert);
    const handleAlertClick = () => {
      setOpen(false);
      router.push(`/alerts/${alert.id}`);
    };

    const getAlertBorderColor = () => {
      if (alert.severity === 'critical') return 'border-l-destructive';
      if (alert.severity === 'high') return 'border-l-amber-500';
      if (alert.severity === 'medium') return 'border-l-blue-500';
      return 'border-l-muted-foreground';
    };

    return (
      <div
        key={alert.id}
        className={cn(
          'flex flex-col gap-1 p-3 transition-colors border-l-4 rounded-xl my-1 mx-1 shadow-sm hover:bg-muted/40 cursor-pointer',
          getAlertBorderColor(),
          alert.status === 'triggered' ? 'bg-muted/20' : 'bg-background'
        )}
        onClick={handleAlertClick}
        role="button"
        tabIndex={0}
        onKeyDown={(e) => e.key === 'Enter' && handleAlertClick()}
        aria-labelledby={`alert-description-${alert.id}`}
        aria-describedby={alert.issue ? `alert-issue-${alert.id}` : undefined}
      >
        <div className="flex items-center justify-between mb-1">
          <div className="flex items-center gap-2">
            {getAlertSeverityIcon(alert.severity)}
            <span className="text-xs text-muted-foreground whitespace-nowrap">
              {formatTime(alert.startTime)}
            </span>
          </div>
          <div className="flex items-center gap-2">
            {getAlertStatusBadge(alert.status)}
            {alert.events > 1 && (
              <Badge variant="outline" className="text-xs px-1.5 py-0.5">
                {alert.events} events
              </Badge>
            )}
          </div>
        </div>

        <div className="flex flex-col gap-1">
          <h4 id={`alert-title-${alert.id}`} className="text-sm font-semibold break-words">{alert.title}</h4>
          <p id={`alert-description-${alert.id}`} className="text-sm text-muted-foreground">
            {alert.description.length > 120
              ? `${alert.description.substring(0, 120)}...`
              : alert.description
            }
          </p>
          {alert.issue && (
            <p id={`alert-issue-${alert.id}`} className="text-sm text-muted-foreground break-words">{alert.issue}</p>
          )}
        </div>

        {alert.assignee && (
          <div className="flex justify-end mt-2">
            <Badge variant="outline" className="text-xs px-1.5 py-0.5">
              Assigned to: {alert.assignee}
            </Badge>
          </div>
        )}
      </div>
    );
  }, [router, setOpen]);

  // Create combined items for the All tab
  const combinedItems = useMemo(() => {
    const notificationItems = notifications.map(notification => ({
      type: 'notification' as const,
      item: notification,
      timestamp: new Date(notification.created_at).getTime()
    }));

    const alertItems = (alerts || []).map(alert => ({
      type: 'alert' as const,
      item: alert,
      timestamp: new Date(alert.startTime).getTime()
    }));

    return [...notificationItems, ...alertItems]
      .sort((a, b) => b.timestamp - a.timestamp);
  }, [notifications, alerts]);

  // Group combined items by time
  const groupedCombinedItems = useMemo(() => {
    const groups: Record<string, typeof combinedItems> = {
      today: [],
      yesterday: [],
      thisWeek: [],
      thisMonth: [],
      older: []
    };

    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate()).getTime();
    const yesterday = today - 86400000;
    const thisWeek = today - 6 * 86400000;
    const thisMonth = today - 30 * 86400000;

    combinedItems.forEach(item => {
      const timestamp = item.timestamp;

      if (timestamp >= today) {
        groups.today.push(item);
      } else if (timestamp >= yesterday) {
        groups.yesterday.push(item);
      } else if (timestamp >= thisWeek) {
        groups.thisWeek.push(item);
      } else if (timestamp >= thisMonth) {
        groups.thisMonth.push(item);
      } else {
        groups.older.push(item);
      }
    });

    return groups;
  }, [combinedItems]);

  // Create a shared content component that handles both notifications and alerts
  const CombinedContent = useCallback(() => (
    <div className="transition-all duration-300 ease-in-out">
      <ScrollArea
        ref={scrollAreaRef}
        className="max-h-[60vh] overflow-y-auto p-1 custom-scrollbar"
        scrollHideDelay={0}
      >
        {loading && paginationParams.skip === 0 ? (
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-5 w-5 animate-spin text-muted-foreground" />
            <span className="ml-2 text-sm text-muted-foreground">Loading...</span>
          </div>
        ) : (
          <div className="relative">
            {Object.entries(groupedCombinedItems)
              .filter(([_, items]) => items.length > 0)
              .map(([group, items]) => (
                <div key={group} className="mb-3"> {/* Increased mb for more space between groups */}
                  <div className="flex items-center gap-1.5 px-2 py-1.5 mb-1.5 sticky top-0 bg-popover z-10 border-b border-border"> {/* Added sticky, bg, z-index, border */}
                    {getTimeGroupIcon(group)}
                    <h5 className="text-xs font-semibold text-muted-foreground tracking-wide uppercase">
                      {formatGroupTitle(group)}
                    </h5>
                    <span className="ml-auto text-[10px] font-medium text-muted-foreground bg-muted px-1.5 py-0.5 rounded-full">
                      {items.length}
                    </span>
                  </div>
                  {items.map(item => (
                    item.type === 'notification'
                      ? renderNotification(item.item)
                      : renderAlert(item.item)
                  ))}
                </div>
              ))}

            {/* Load More button */}
            {notifications.length < totalCount && (
              <div className="flex justify-center my-4">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleLoadMore}
                  disabled={loading || isFetching}
                  className="w-full max-w-[200px]"
                >
                  {loading || isFetching ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      Loading...
                    </>
                  ) : (
                    <>Load More</>
                  )}
                </Button>
              </div>
            )}

            {combinedItems.length === 0 && (
              <div className="flex items-center justify-center py-8">
                <span className="text-sm text-muted-foreground">
                  No items to display
                </span>
              </div>
            )}
          </div>
        )}
      </ScrollArea>
    </div>
  ), [loading, isFetching, paginationParams.skip, groupedCombinedItems, notifications.length, totalCount, handleLoadMore, renderNotification, renderAlert]);

  // Calculate total badge count (unread notifications + triggered alerts)
  const totalBadgeCount = useMemo(() => {
    const triggeredAlertsCount = alerts?.filter(alert => alert.status === 'triggered').length || 0;
    return unreadCount + triggeredAlertsCount;
  }, [unreadCount, alerts]);

  // Keep the original NotificationContent for unread and action tabs
  const NotificationContent = useCallback(() => (
    <div className="transition-all duration-300 ease-in-out">
      <ScrollArea
        ref={scrollAreaRef}
        className="max-h-[60vh] overflow-y-auto p-1 custom-scrollbar"
        scrollHideDelay={0}
      >
        {loading && paginationParams.skip === 0 ? (
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-5 w-5 animate-spin text-muted-foreground" />
            <span className="ml-2 text-sm text-muted-foreground">Loading...</span>
          </div>
        ) : (
          <div className="relative">
            {renderGroupedNotifications()}

            {/* Load More button */}
            {notifications.length < totalCount && (
              <div className="flex justify-center my-4">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleLoadMore}
                  disabled={loading || isFetching}
                  className="w-full max-w-[200px]"
                >
                  {loading || isFetching ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      Loading...
                    </>
                  ) : (
                    <>Load More Notifications</>
                  )}
                </Button>
              </div>
            )}

            {notifications.length === 0 && (
              <div className="flex items-center justify-center py-8">
                <span className="text-sm text-muted-foreground">
                  No notifications
                </span>
              </div>
            )}
          </div>
        )}
      </ScrollArea>
    </div>
  ), [loading, isFetching, paginationParams.skip, renderGroupedNotifications, notifications.length, totalCount, handleLoadMore]);

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="ghost"
          size="icon"
          className="relative h-8 w-8"
        >
          <Bell className="h-4 w-4" />
          {totalBadgeCount > 0 && (
            <Badge
              variant="destructive"
              className="absolute -right-1 -top-1 h-5 min-w-5 rounded-full p-0.5 text-[10px] flex items-center justify-center font-medium animate-pulse"
            >
              {totalBadgeCount > 99 ? '99+' : totalBadgeCount}
            </Badge>
          )}
        </Button>
      </PopoverTrigger>
      <PopoverContent
        align="end"
        className="w-96 p-0 rounded-xl"
        sideOffset={5}
      >
        <div className="flex items-center justify-between p-2">
          <h4 className="text-sm font-medium flex items-center">
            Notifications
            <span className="ml-1 text-xs text-muted-foreground">({totalCount})</span>
            {unreadCount > 0 && (
              <span className="ml-2 text-xs text-primary">
                {unreadCount} unread
              </span>
            )}
          </h4>
          <Button
            variant="ghost"
            size="sm"
            className="h-6 px-2 text-xs"
            onClick={() => {
              saveScrollPosition();
              markAllAsRead.mutate();
            }}
            disabled={unreadCount === 0}
          >
            <CheckCheck className="mr-1 h-3 w-3" />
            Mark all read
          </Button>
        </div>

        <Separator className="mb-1" />

        <Tabs defaultValue="all" value={activeTab} onValueChange={handleTabChange}>
          <div className="px-2 pt-0">
            <TabsList className="w-full h-8">
              <TabsTrigger value="all" className="flex-1 text-xs h-7">
                All
                {totalBadgeCount > 0 && (
                  <span className="ml-1 text-[10px] text-muted-foreground">
                    ({totalBadgeCount})
                  </span>
                )}
              </TabsTrigger>
              <TabsTrigger value="unread" className="flex-1 text-xs h-7">
                Unread
                {unreadCount > 0 && (
                  <span className="ml-1 text-[10px] text-muted-foreground">
                    ({unreadCount})
                  </span>
                )}
              </TabsTrigger>
              <TabsTrigger value="action" className="flex-1 text-xs h-7">
                Requires Action
              </TabsTrigger>
            </TabsList>
          </div>

          <TabsContent value="all" className="m-0">
            <CombinedContent />
          </TabsContent>

          <TabsContent value="unread" className="m-0">
            <NotificationContent />
          </TabsContent>

          <TabsContent value="action" className="m-0">
            <NotificationContent />
          </TabsContent>
        </Tabs>
      </PopoverContent>
    </Popover>
  );
}
