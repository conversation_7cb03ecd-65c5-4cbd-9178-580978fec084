// components/ui/bot-avatar.tsx
import {
    Bot,
    User,
    Code,
    Terminal,
    Database,
    Cloud,
    UserCog,
    UserRoundSearch,
    UserPen,
    Shield
} from 'lucide-react'
import { cn } from '@/lib/utils'
import Image from 'next/image'
import { useEffect, useState } from 'react';
import { getCachedAvatar } from '@/lib/avatarCache';

interface BotAvatarProps {
    role?: string;
    className?: string;
    sticky?: boolean;
    variant?: 'default' | 'compact';
    hideRoleText?: boolean;
}

export const BotAvatar = ({
    role = 'assistant',
    className,
    sticky = false,
    variant = 'default',
    hideRoleText = false
}: BotAvatarProps) => {
    // Get the agent configuration based on role
    const agentConfig = getAgentConfig(role);
    const roleLabel = agentConfig.name;

    // Avatar state (base64 or fallback)
    const [avatarUrl, setAvatarUrl] = useState('/avatars/cloud.webp');

    useEffect(() => {
        let isMounted = true;
        getCachedAvatar(role, '/avatars/cloud.webp').then(url => {
            if (isMounted) setAvatarUrl(url);
        });
        return () => { isMounted = false; };
    }, [role]);

    return (
        <div className={cn(
            "flex flex-row items-center gap-2",
            variant === 'compact' ? "mb-1" : "mb-2",
            sticky && "sticky top-0",
            className
        )}>
            <div className={cn(
                "flex items-center justify-center flex-shrink-0",
                variant === 'compact' ? "w-8 h-8" : "w-10 h-10",
                role !== 'user' && "rounded-full bg-white dark:bg-gray-900 shadow-sm overflow-hidden border border-gray-700"
            )}>
                {role === 'user' ? (
                    <div className={cn(
                        "flex items-center justify-center rounded-full",
                        "bg-primary/10 dark:bg-primary/20"
                    )}>
                        {/* User icon removed */}
                    </div>
                ) : (
                    <Image
                        src={avatarUrl}
                        alt={`${roleLabel} Avatar`}
                        width={variant === 'compact' ? 32 : 40}
                        height={variant === 'compact' ? 32 : 40}
                        className="object-cover"
                        onError={(e) => {
                            const target = e.target as HTMLImageElement;
                            target.src = "/avatars/cloud.webp";
                        }}
                    />
                )}
            </div>

            {!hideRoleText && (
                <div className={cn(
                    "text-primary/90 dark:text-primary/80 font-bold tracking-wide",
                    variant === 'compact' ? "text-xs" : "text-sm",
                    "transition-all duration-300 ease-in-out",
                    "hover:text-primary dark:hover:text-primary",
                    "ml-0.5 pb-0.5" // Subtle alignment adjustment
                )}>
                    {roleLabel}
                </div>
            )}
        </div>
    )
}

// Utility function to get agent configuration
const getAgentConfig = (role: string) => {
    // Convert role to lowercase for case-insensitive matching
    const roleLower = role.toLowerCase();

    switch (roleLower) {
        case 'assistant':
            return {
                role,
                icon: <Bot size={16} />,
                shortName: 'AI',
                name: 'Supervisor',
                bgColor: '',
                color: 'text-green-600 dark:text-green-400',
            };
        case 'user':
            return {
                role,
                icon: <User size={16} />,
                shortName: 'U',
                name: 'User',
                bgColor: '',
                color: 'text-blue-500 dark:text-blue-300',
            };
        case 'system':
            return {
                role,
                icon: <UserCog size={16} />,
                shortName: 'S',
                name: 'System',
                bgColor: '',
                color: 'text-gray-500 dark:text-gray-300',
            };
        case 'supervisor':
            return {
                role,
                icon: <UserRoundSearch size={16} />,
                shortName: 'SV',
                name: 'Supervisor',
                bgColor: '',
                color: 'text-purple-600 dark:text-purple-400',
            };
        case 'cloud_engineer':
            return {
                role,
                icon: <UserPen size={16} />,
                shortName: 'CE',
                name: 'Cloud Engineer',
                bgColor: '',
                color: 'text-blue-600 dark:text-blue-400',
            };
        case 'security_specialist':
            return {
                role,
                icon: <Shield size={16} />,
                shortName: 'SS',
                name: 'Security Specialist',
                bgColor: '',
                color: 'text-red-600 dark:text-red-400',
            };
        case 'anna':
            return {
                role,
                icon: <Cloud size={16} />,
                shortName: 'A',
                name: 'Anna',
                bgColor: '',
                color: 'text-blue-600 dark:text-blue-400',
            };
        case 'bob':
            return {
                role,
                icon: <Database size={16} />,
                shortName: 'B',
                name: 'Bob',
                bgColor: '',
                color: 'text-purple-600 dark:text-purple-400',
            };
        case 'function':
            return {
                role,
                icon: <Code size={16} />,
                shortName: 'F',
                name: 'Function',
                bgColor: '',
                color: 'text-green-500 dark:text-green-300',
            };
        case 'tool':
            return {
                role,
                icon: <Terminal size={16} />,
                shortName: 'T',
                name: 'Tool',
                bgColor: '',
                color: 'text-amber-500 dark:text-amber-300',
            };
        case 'data':
            return {
                role,
                icon: <Database size={16} />,
                shortName: 'D',
                name: 'Data',
                bgColor: '',
                color: 'text-cyan-500 dark:text-cyan-300',
            };
        default:
            // For custom agent names, preserve the original name but capitalize first letter
            return {
                role,
                icon: <Code size={16} />,
                shortName: role.charAt(0).toUpperCase(),
                name: role.charAt(0).toUpperCase() + role.slice(1),
                bgColor: '',
                color: 'text-gray-500 dark:text-gray-300',
            };
    }
};
