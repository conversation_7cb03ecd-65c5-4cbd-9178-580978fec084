'use client'

import { useState, useEffect, useRef } from "react";
import { User, Lock, Info } from "lucide-react";
import { cn } from "@/lib/utils";
import { AgentMentionProps } from "@/components/chat/types";
import { useAgents } from "@/hooks/use-agents";
import { createPortal } from "react-dom";

export function AgentMentionDropdown({
  isVisible,
  position,
  filter,
  onSelect,
  onClose
}: AgentMentionProps) {
  const [highlightedIndex, setHighlightedIndex] = useState(0);
  const [hoveredIndex, setHoveredIndex] = useState<number | null>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const agentItemsRef = useRef<Array<HTMLDivElement | null>>([]);

  // Use our custom hook with caching
  const { data: agentsResponse } = useAgents();
  const agentsData = agentsResponse?.data;

  // Filter agents based on input - search both name and description
  const filteredAgents = Array.isArray(agentsData) ? agentsData
    .filter(agent => agent.type === 'conversation_agent' && agent.is_active) // Only show active conversation agents
    .filter(agent => {
      const lowercaseFilter = filter.toLowerCase();
      return agent.title?.toLowerCase().includes(lowercaseFilter) ||
        (agent.description && agent.description.toLowerCase().includes(lowercaseFilter));
    })
    .map(agent => ({
      id: agent.id,
      name: agent.title || '',
      description: agent.description,
      avatar: `/avatars/${agent.title?.toLowerCase().split(" ")[0]}.webp`,
      locked: false
    })) : [];

  // Reset highlighted index when filtered data changes
  useEffect(() => {
    setHighlightedIndex(0);
  }, [filteredAgents.length]);

  // Initialize when dropdown becomes visible
  useEffect(() => {
    if (isVisible) {
      setHighlightedIndex(0);
      setHoveredIndex(null);
    }
  }, [isVisible]);

  // Handle keyboard events
  useEffect(() => {
    if (!isVisible) return;

    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'ArrowUp') {
        e.preventDefault();
        setHighlightedIndex(prev =>
          prev > 0 ? prev - 1 : filteredAgents.length - 1
        );
      } else if (e.key === 'ArrowDown') {
        e.preventDefault();
        setHighlightedIndex(prev =>
          prev < filteredAgents.length - 1 ? prev + 1 : 0
        );
      } else if (e.key === 'Enter') {
        e.preventDefault();
        const agent = filteredAgents[highlightedIndex];
        if (agent && !agent.locked) {
          onSelect(agent.id, agent.name);
        }
      } else if (e.key === 'Escape') {
        e.preventDefault();
        onClose();
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [isVisible, filteredAgents, highlightedIndex, onSelect, onClose]);

  // Ensure the highlighted item is visible
  useEffect(() => {
    if (!isVisible || filteredAgents.length === 0) return;

    const highlightedElement = agentItemsRef.current[highlightedIndex];
    const containerElement = containerRef.current;

    if (highlightedElement && containerElement) {
      const containerRect = containerElement.getBoundingClientRect();
      const elementRect = highlightedElement.getBoundingClientRect();

      if (elementRect.bottom > containerRect.bottom) {
        containerElement.scrollTop += elementRect.bottom - containerRect.bottom;
      } else if (elementRect.top < containerRect.top) {
        containerElement.scrollTop -= containerRect.top - elementRect.top;
      }
    }
  }, [highlightedIndex, isVisible, filteredAgents.length]);

  if (!isVisible) return null;

  const dropdown = (
    <div
      className={cn(
        "fixed z-50 bg-background/95 backdrop-blur-sm",
        "border border-muted-foreground/10",
        "rounded-xl shadow-lg",
        "w-[500px] max-h-[320px] overflow-hidden",
        "transition-all duration-200 ease-in-out",
        "animate-in fade-in-50 zoom-in-95"
      )}
      style={{
        top: `${position.top}px`,
        left: `${position.left}px`
      }}
    >
      <div className="p-3 border-b border-muted-foreground/10 bg-muted/30 flex items-center">
        <span className="text-sm font-medium">Mention an active agent</span>
        <div className="ml-2 px-2 py-0.5 bg-primary/10 rounded-full text-xs text-primary">
          {filteredAgents.length} {filteredAgents.length === 1 ? 'agent' : 'agents'}
        </div>
      </div>

      <div
        ref={containerRef}
        className="py-1.5 max-h-[240px] overflow-y-auto custom-scrollbar"
      >
        {filteredAgents.length > 0 ? (
          filteredAgents.map((agent, index) => (
            <div
              key={agent.id}
              ref={(el) => {
                agentItemsRef.current[index] = el;
              }}
              className={cn(
                "flex items-center justify-between px-4 py-3",
                "hover:bg-muted/50 transition-colors duration-150",
                "border-l-2 relative",
                index === highlightedIndex
                  ? "bg-muted/50 border-primary"
                  : "border-transparent",
                agent.locked && "opacity-70 cursor-not-allowed"
              )}
              onClick={() => {
                if (!agent.locked) {
                  onSelect(agent.id, agent.name);
                }
              }}
              onMouseEnter={() => setHoveredIndex(index)}
              onMouseLeave={() => setHoveredIndex(null)}
            >
              <div className="flex items-center gap-3">
                <div className={cn(
                  "flex-shrink-0 h-10 w-10 rounded-full bg-background/60 flex items-center justify-center",
                  agent.locked ? "bg-muted/30" : ""
                )}>
                  {agent.avatar ? (
                    <img
                      src={agent.avatar}
                      alt={agent.name}
                      className="h-8 w-8 rounded-full object-cover"
                      onError={(e) => {
                        const target = e.target as HTMLImageElement;
                        target.src = "/avatars/cloud.webp";
                      }}
                    />
                  ) : (
                    <User className={cn(
                      "h-5 w-5",
                      agent.locked ? "text-muted-foreground/50" : "text-blue-500"
                    )} />
                  )}
                </div>
                <div className="flex flex-col">
                  <span className="font-medium text-foreground">{agent.name}</span>
                  <span className="text-xs text-muted-foreground">
                    {agent.description && agent.description.length > 50
                      ? `${agent.description.substring(0, 50)}...`
                      : agent.description || 'AI assistant'}
                  </span>
                </div>
              </div>

              {agent.locked ? (
                <div className="flex items-center text-muted-foreground">
                  <Lock className="h-4 w-4 mr-1" />
                  <span className="text-xs">Locked</span>
                </div>
              ) : (
                <div className="relative group">
                  <Info className="h-4 w-4 text-muted-foreground/60 hover:text-primary transition-colors" />
                </div>
              )}

              {/* Tooltip on hover */}
              {index === hoveredIndex && (
                <div className={cn(
                  "absolute right-2",
                  index === 0 ? "top-14" : "-top-12",
                  "z-50",
                  "bg-black/80 text-white",
                  "p-2 rounded shadow-lg",
                  "text-xs max-w-[280px]",
                  "animate-in fade-in-50 zoom-in-95"
                )}>
                  {agent.locked ? (
                    "This agent is locked. Upgrade to access their expertise."
                  ) : (
                    <>
                      <p className="font-semibold mb-1">What this agent can do:</p>
                      <p>{agent.description || "Help with various tasks related to their expertise."}</p>
                    </>
                  )}
                </div>
              )}
            </div>
          ))
        ) : (
          <div className="flex flex-col items-center justify-center py-6 text-center">
            <div className="h-10 w-10 rounded-full bg-muted/50 flex items-center justify-center mb-2">
              <User className="h-5 w-5 text-muted-foreground/70" />
            </div>
            <p className="text-sm text-muted-foreground">No active agents found</p>
            <p className="text-xs text-muted-foreground/70 mt-1 max-w-[80%]">
              Try a different search term or activate some agents
            </p>
          </div>
        )}
      </div>
    </div>
  );

  return createPortal(dropdown, document.body);
}
