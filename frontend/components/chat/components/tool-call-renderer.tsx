import { useState, useMemo, useRef, useCallback, useLayoutEffect, useEffect } from "react";
import { ToolCall } from "../types";
import { cn } from "@/lib/utils";
import {
  CheckCircle2, XCircle, LoaderCircle, ChevronDown, <PERSON>evronUp,
  <PERSON><PERSON>,
  Check,
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import Image from "next/image";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { SyntaxHighlighter } from '@/components/ui/code-block';
import { motion, AnimatePresence } from "framer-motion";
import { ColoredToolIcon } from "./colored-tool-icon";
import { getToolType } from "./tool-config";
import { getCachedAvatar } from "@/lib/avatarCache";

// Status configuration
const STATUS_CONFIG = {
  running: {
    icon: <LoaderCircle className="h-3.5 w-3.5 animate-spin" />,
    color: "text-blue-600 dark:text-blue-400 border-none",
    bgColor: "bg-blue-500/10 dark:bg-blue-500/20",
    border: "border-blue-200/60 dark:border-blue-800/60",
    ariaLabel: "Operation in progress",
    label: "Running"
  },
  completed: {
    icon: <CheckCircle2 className="h-3.5 w-3.5" />,
    color: "text-green-600 dark:text-green-400 border-none",
    bgColor: "bg-green-500/10 dark:bg-green-500/20",
    border: "border-green-200/60 dark:border-green-800/60",
    ariaLabel: "Operation completed successfully",
    label: "Completed"
  },
  error: {
    icon: <XCircle className="h-3.5 w-3.5" />,
    color: "text-red-600 dark:text-red-400 border-none",
    bgColor: "bg-red-500/10 dark:bg-red-500/20",
    border: "border-red-200/60 dark:border-red-800/60",
    ariaLabel: "Operation failed with error",
    label: "Error"
  }
} as const;

// Utility functions
function isJsonContent(content: any): boolean {
  if (!content) return false;
  if (typeof content === 'object') return true;
  if (typeof content === 'string') {
    try {
      JSON.parse(content);
      return true;
    } catch {
      return false;
    }
  }
  return false;
}

/**
 * Checks if the content appears to be markdown
 * Looks for common markdown patterns
 */
function isMarkdownContent(content: any): boolean {
  if (!content || typeof content !== 'string') return false;

  // Check for common markdown patterns
  const markdownPatterns = [
    /^#+ .*$/m,                           // Headers
    /\[.*\]\(.*\)/,                       // Links
    /\*\*.*\*\*/,                         // Bold
    /\*.*\*/,                             // Italic
    /```[\s\S]*?```/,                     // Code blocks
    /^\s*[-*+] /m,                        // Lists
    /^\s*\d+\. /m,                        // Numbered lists
    /^>\s.*$/m,                           // Blockquotes
    /\|.*\|.*\|/,                         // Tables
    /^---$/m,                             // Horizontal rules
  ];

  // Return true if any markdown pattern is found
  return markdownPatterns.some(pattern => pattern.test(content));
}

function extractReasoning(args: string | undefined): string | null {
  if (!args) return null;
  try {
    const parsed = typeof args === 'string' ? JSON.parse(args) : args;
    return parsed.reasoning || null;
  } catch {
    return null;
  }
}

interface ToolCallRendererProps {
  toolCall: ToolCall;
  isExpanded?: boolean;
  onExpandChange?: (expanded: boolean) => void;
  className?: string;
  agentInfo?: {
    name: string;
    role: string;
  };
  useAnimations?: boolean;
  showAvatar?: boolean;
}

function CopyButton({ getContent, label }: { getContent: () => string, label: string }) {
  const [copied, setCopied] = useState(false);
  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(getContent());
      setCopied(true);
      setTimeout(() => setCopied(false), 1200);
    } catch {}
  };
  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <Button
            type="button"
            size="icon"
            variant="ghost"
            className="h-6 w-6 p-0 ml-1"
            aria-label={`Copy ${label}`}
            onClick={e => { e.stopPropagation(); handleCopy(); }}
            tabIndex={0}
          >
            {copied ? <Check className="h-4 w-4 text-green-600" /> : <Copy className="h-4 w-4" />}
          </Button>
        </TooltipTrigger>
        <TooltipContent>{copied ? "Copied!" : `Copy ${label}`}</TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}

export function ToolCallRenderer({
  toolCall,
  isExpanded: initialExpanded = false,
  onExpandChange,
  className = "",
  agentInfo,
  useAnimations = true,
  showAvatar = false
}: ToolCallRendererProps) {

  console.log('toolCall', toolCall);
  const [isExpandedState, setIsExpandedState] = useState(initialExpanded);
  // const { resolvedTheme } = useTheme();
  // const isDarkMode = resolvedTheme === 'dark';

  // Add state for expanding args and result sections
  const [isArgsExpanded, setIsArgsExpanded] = useState(false);
  const [isResultExpanded, setIsResultExpanded] = useState(false);
  const [showArgsExpand, setShowArgsExpand] = useState(false);
  const [showResultExpand, setShowResultExpand] = useState(false);

  // Add state for avatar caching
  const [avatarSrc, setAvatarSrc] = useState<string>("/avatars/cloud.webp");
  const [avatarLoading, setAvatarLoading] = useState(false);

  const argsRef = useRef<HTMLDivElement>(null);
  const [resultNode, setResultNode] = useState<HTMLDivElement | null>(null);
  const resultCallbackRef = useCallback((node: HTMLDivElement | null) => {
    setResultNode(node);
  }, []);

  // Load cached avatar when agentInfo changes
  useEffect(() => {
    if (showAvatar && agentInfo?.name) {
      setAvatarLoading(true);
      const agentRole = agentInfo.name.toLowerCase().split(' ')[0];
      getCachedAvatar(agentRole, "/avatars/cloud.webp")
        .then(src => {
          setAvatarSrc(src);
          setAvatarLoading(false);
        })
        .catch(() => {
          setAvatarSrc("/avatars/cloud.webp");
          setAvatarLoading(false);
        });
    }
  }, [showAvatar, agentInfo?.name]);

  useLayoutEffect(() => {
    const checkOverflow = () => {
      if (argsRef.current) {
        setShowArgsExpand(argsRef.current.scrollHeight > argsRef.current.clientHeight);
      }
      if (resultNode) {
        setShowResultExpand(resultNode.scrollHeight > resultNode.clientHeight);
      }
    };

    const timerId = setTimeout(checkOverflow, 100);
    window.addEventListener('resize', checkOverflow);

    return () => {
      clearTimeout(timerId);
      window.removeEventListener('resize', checkOverflow);
    };
  }, [resultNode, isArgsExpanded, isResultExpanded, toolCall.arguments, toolCall.output, toolCall.error]);

  // Use either controlled or uncontrolled expansion state
  const isExpanded = onExpandChange ? initialExpanded : isExpandedState;
  const setIsExpanded = (expanded: boolean) => {
    setIsExpandedState(expanded);
    if (onExpandChange) {
      onExpandChange(expanded);
    }
  };

  // Basic tool data
  const status = STATUS_CONFIG[toolCall.status] || STATUS_CONFIG.running;
  const toolType = getToolType(toolCall.name);
  // const permission = getToolPermission(toolCall.name);
  const reasoning = extractReasoning(toolCall.arguments);
  const isAwsTool = toolCall.name.startsWith('aws_');

  const argsIsJson = isJsonContent(toolCall.arguments);
  // const outputIsJson = isJsonContent(toolCall.output || toolCall.error);

  const formattedArgs = argsIsJson && toolCall.arguments
    ? (typeof toolCall.arguments === 'string'
        ? JSON.parse(toolCall.arguments)
        : toolCall.arguments)
    : toolCall.arguments;

  const awsScript = isAwsTool && formattedArgs && typeof formattedArgs === 'object' && 'script' in formattedArgs
    ? String(formattedArgs.script)
    : null;

  const formatAwsOutput = useMemo(() => {
    if (!toolCall.output || typeof toolCall.output !== 'string') return null;

    try {
      const parsed = JSON.parse(toolCall.output);
      return JSON.stringify(parsed, null, 2);
    } catch {
      return toolCall.output;
    }
  }, [toolCall.output]);

  // Determine if reasoning exists and has content
  const hasReasoning = !!(reasoning && typeof reasoning === 'string' && reasoning.trim());

  const toggleExpand = () => {
    setIsExpanded(!isExpanded);
  };

  const content = (
    <div className={cn(
      "will-change-transform border border-black/10 dark:border-white/10 rounded-xl md:rounded-2xl overflow-hidden my-2 md:my-4 mx-1 md:mx-0",
      isExpanded ? "shadow-md" : "hover:shadow-sm",
      className
    )}
    data-tool-call="true"
    >
      {/* Tool Header Button */}
      <TooltipProvider>
        <Button
          variant="ghost"
          className={cn(
            "flex w-full items-center px-2 py-2 md:px-3 md:py-2 h-auto rounded-none",
            "hover:bg-muted/30 hover:text-foreground transition-colors",
            "group focus-visible:ring-1 focus-visible:ring-primary/40",
            isExpanded && "bg-muted/20"
          )}
          onClick={toggleExpand}
          aria-expanded={isExpanded}
        >
          <div className="flex items-center justify-between w-full min-w-0">
            <div className="flex items-center gap-2 overflow-hidden min-w-0 flex-1">
              {/* Agent Avatar (if provided and requested) */}
              {showAvatar && agentInfo && (
                <div className="h-6 w-6 md:h-7 md:w-7 shrink-0 rounded-full overflow-hidden bg-primary/10 flex items-center justify-center">
                  {avatarLoading ? (
                    <div className="w-full h-full bg-muted animate-pulse rounded-full" />
                  ) : (
                    <Image
                      src={avatarSrc}
                      alt={agentInfo.name || 'Agent'}
                      width={28}
                      height={28}
                      className="object-cover w-full h-full"
                      onError={() => {
                        setAvatarSrc("/avatars/cloud.webp");
                      }}
                    />
                  )}
                </div>
              )}

              {/* Tool Icon */}
              <ColoredToolIcon
                icon={toolType.icon}
                iconColor={toolType.iconColor}
                bgColor={toolType.bgColor}
                size={28}
                className="p-1 md:p-1.5 rounded-lg md:rounded-xl shrink-0"
              />

              {/* Tool Info */}
              <div className="flex flex-col min-w-0 items-start flex-1 overflow-hidden">
                <div className="flex items-center gap-1 md:gap-2 w-full">
                  <span className="font-medium text-xs md:text-sm text-left truncate flex-1">
                    {toolType.display}
                  </span>
                  <Badge
                    variant="outline"
                    className={cn(
                      "px-1 h-4 md:h-5 text-xs border-none shrink-0",
                      status.color,
                      "transition-colors"
                    )}
                  >
                    <span className="flex items-center gap-0.5 md:gap-1">
                      {status.icon}
                      <span className="text-[9px] md:text-[10px] font-medium">{status.label}</span>
                    </span>
                  </Badge>
                </div>

                                {/* Reasoning or Agent Info - always show */}
                {(hasReasoning || (agentInfo && showAvatar)) && (
                  <div className="text-xs text-muted-foreground text-left w-full">
                    {hasReasoning ? (
                      <span className="italic line-clamp-1 md:line-clamp-1">
                        {reasoning!}
                      </span>
                    ) : agentInfo && showAvatar ? (
                      <span className="italic truncate">Executed by {agentInfo.name}</span>
                    ) : null}
                  </div>
                )}
              </div>
            </div>

            {/* Expand/Collapse */}
            <ChevronDown className={cn(
              "h-4 w-4 text-muted-foreground/60 transition-transform shrink-0 ml-2",
              "group-hover:text-muted-foreground",
              isExpanded && "rotate-180"
            )} />
          </div>
        </Button>
      </TooltipProvider>

      {/* Content (when expanded) */}
      {useAnimations ? (
        <AnimatePresence>
          {isExpanded && (
            <motion.div
              initial={{ height: 0, opacity: 0 }}
              animate={{ height: "auto", opacity: 1 }}
              exit={{ height: 0, opacity: 0 }}
              transition={{ duration: 0.1 }}
              className="overflow-hidden"
            >
              {renderExpandedContent()}
            </motion.div>
          )}
        </AnimatePresence>
      ) : (
        isExpanded && (
          <div className="border-t border-border/40">
            {renderExpandedContent()}
          </div>
        )
      )}
    </div>
  );

  function renderExpandedContent() {
    return (
      <div className="border-t border-border/40 p-2 md:p-3 space-y-2 md:space-y-3 bg-muted/5">
        {/* Reasoning Section */}
        {hasReasoning && (
          <div className="space-y-1 md:space-y-1.5">
            <div className="text-xs font-medium text-muted-foreground flex items-center gap-1">
              Reasoning
            </div>
            <div className="rounded-lg md:rounded-xl border border-border/60 overflow-hidden bg-card/30">
              <div className="p-3 text-xs font-mono overflow-x-auto overflow-y-auto max-h-[150px] md:max-h-[200px] custom-scrollbar" style={{wordBreak: 'break-word', whiteSpace: 'pre-wrap'}}>
                {reasoning}
              </div>
            </div>
          </div>
        )}

        {/* Arguments Section */}
        {toolCall.name !== 'group_chat' && (
          <div className="space-y-1 md:space-y-1.5">
            <div className="text-xs font-medium text-muted-foreground flex items-center gap-1">
              {isAwsTool ? 'Script' : 'Arguments'}
              {(showArgsExpand || isArgsExpanded) && (
                <Button
                  type="button"
                  size="sm"
                  variant="outline"
                  className="ml-1 md:ml-2 flex items-center gap-1 px-1.5 py-0.5 md:px-2 md:py-1 text-xs"
                  aria-label={isArgsExpanded ? `Collapse ${isAwsTool ? 'Script' : 'Arguments'}` : `Expand ${isAwsTool ? 'Script' : 'Arguments'}`}
                  onClick={e => { e.stopPropagation(); setIsArgsExpanded(v => !v); }}
                  tabIndex={0}
                >
                  {isArgsExpanded ? <ChevronUp className="h-3.5 w-3.5 md:h-4 md:w-4" /> : <ChevronDown className="h-3.5 w-3.5 md:h-4 md:w-4" />}
                  <span className="hidden sm:inline md:inline">{isArgsExpanded ? 'Collapse' : 'Expand'}</span>
                </Button>
              )}
            </div>
            <div className="rounded-lg md:rounded-xl border border-border/60 overflow-hidden">
              <div ref={argsRef} className={cn("bg-muted/30 overflow-x-auto overflow-y-auto", isArgsExpanded ? "max-h-none" : "max-h-[200px] md:max-h-[300px]", "custom-scrollbar")}>
                {isAwsTool && awsScript ? (
                  <SyntaxHighlighter
                    language="bash"
                    className="text-xs min-w-0"
                    wrapLongLines={false}
                    customStyle={{
                      margin: 0,
                      padding: '0.75rem',
                      background: 'transparent',
                      overflowX: 'auto',
                      whiteSpace: 'pre'
                    }}
                  >
                    {awsScript}
                  </SyntaxHighlighter>
                ) : (
                  <SyntaxHighlighter
                    language="json"
                    className="text-xs min-w-0"
                    wrapLongLines={false}
                    customStyle={{
                      margin: 0,
                      padding: '0.75rem',
                      background: 'transparent',
                      overflowX: 'auto',
                      whiteSpace: 'pre'
                    }}
                  >
                    {typeof formattedArgs === 'string' ? formattedArgs : JSON.stringify(formattedArgs, null, 2)}
                  </SyntaxHighlighter>
                )}
              </div>
            </div>
          </div>
        )}

        {/* Output Section */}
        {(toolCall.output || toolCall.error) && (
          <div className="space-y-1 md:space-y-1.5">
            <div className="flex items-center justify-between">
              <div className="text-xs font-medium text-muted-foreground flex items-center gap-1">
                {toolCall.error ? 'Error' : 'Result'}
                {(showResultExpand || isResultExpanded) && (
                  <Button
                    type="button"
                    size="sm"
                    variant="outline"
                    className="ml-1 md:ml-2 flex items-center gap-1 px-1.5 py-0.5 md:px-2 md:py-1 text-xs"
                    aria-label={isResultExpanded ? `Collapse ${toolCall.error ? 'Error' : 'Result'}` : `Expand ${toolCall.error ? 'Error' : 'Result'}`}
                    onClick={e => { e.stopPropagation(); setIsResultExpanded(v => !v); }}
                    tabIndex={0}
                  >
                    {isResultExpanded ? <ChevronUp className="h-3.5 w-3.5 md:h-4 md:w-4" /> : <ChevronDown className="h-3.5 w-3.5 md:h-4 md:w-4" />}
                    <span className="hidden sm:inline md:inline">{isResultExpanded ? 'Collapse' : 'Expand'}</span>
                  </Button>
                )}
              </div>
            </div>

            <div className="rounded-lg md:rounded-xl border border-border/60 overflow-hidden">
              {toolCall.error ? (
                <div ref={resultCallbackRef} className={cn("bg-red-500/10 dark:bg-red-500/20 p-3 text-xs font-mono text-red-600 dark:text-red-400 overflow-x-auto overflow-y-auto", isResultExpanded ? "max-h-none" : "max-h-[200px] md:max-h-[300px]", "custom-scrollbar")} style={{wordBreak: 'break-word', whiteSpace: 'pre-wrap'}}>
                  {toolCall.error}
                </div>
              ) : isAwsTool ? (
                <div ref={resultCallbackRef} className={cn("bg-muted/30 overflow-x-auto overflow-y-auto", isResultExpanded ? "max-h-none" : "max-h-[200px] md:max-h-[300px]", "custom-scrollbar")}>
                  <SyntaxHighlighter
                    language="json"
                    className="text-xs min-w-0"
                    wrapLongLines={false}
                    customStyle={{
                      margin: 0,
                      padding: '0.75rem',
                      background: 'transparent',
                      overflowX: 'auto',
                      whiteSpace: 'pre'
                    }}
                  >
                    {formatAwsOutput || String(toolCall.output)}
                  </SyntaxHighlighter>
                </div>
              ) : (
                <div ref={resultCallbackRef} className={cn("bg-muted/30 overflow-x-auto overflow-y-auto", isResultExpanded ? "max-h-none" : "max-h-[200px] md:max-h-[300px]", "custom-scrollbar")}>
                  <SyntaxHighlighter
                    language={isJsonContent(toolCall.output) ? "json" : "bash"}
                    className="text-xs min-w-0"
                    wrapLongLines={false}
                    customStyle={{
                      margin: 0,
                      padding: '0.75rem',
                      background: 'transparent',
                      overflowX: 'auto',
                      whiteSpace: 'pre'
                    }}
                  >
                    {typeof toolCall.output === 'string' ? toolCall.output : JSON.stringify(toolCall.output, null, 2)}
                  </SyntaxHighlighter>
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    );
  }

  return content;
}
