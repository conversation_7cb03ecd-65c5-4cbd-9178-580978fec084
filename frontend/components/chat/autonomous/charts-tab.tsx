'use client';

import { motion } from 'framer-motion';
import { <PERSON><PERSON><PERSON> as BarChartIcon } from 'lucide-react';
import { ScrollArea } from '@/components/ui/scroll-area';
import { CanvasItem } from './canvas';
import { DisplayComponent } from '../components/message/display-component';
import { Avatar } from '@/components/ui/avatar';
import { AvatarImage } from '@/components/ui/avatar';
import { AvatarFallback } from '@/components/ui/avatar';
import { MessageDisplayComponentPublic } from '@/client/types.gen';

interface ChartsTabProps {
  canvasItems: CanvasItem[];
}

// Animation variants
const itemVariants = {
  hidden: {
    y: 20,
    opacity: 0
  },
  visible: {
    y: 0,
    opacity: 1,
    transition: {
      ease: 'easeOut',
      duration: 0.3
    }
  }
};

export function ChartsTab({ canvasItems }: ChartsTabProps) {
  // No items placeholder
  if (canvasItems.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center h-full text-center text-muted-foreground p-4">
        <BarChartIcon className="h-12 w-12 mb-4 opacity-20" />
        <p className="text-sm">No charts or tables available yet</p>
        <p className="text-xs mt-1">
          Visualizations will appear here when generated
        </p>
      </div>
    );
  }

  return (
    <ScrollArea className="h-full w-full p-3">
      {/* Chronological Items (oldest first) */}
      {canvasItems.map((item) => (
        <motion.div
          key={item.id}
        >
          {/* Agent information */}
          {item.agentName && (
            <div className="flex items-center mb-2">
              <Avatar className="h-6 w-6 mr-2">
                <AvatarImage src={`/avatars/${item.agentName.toLowerCase()}.webp`} />
                <AvatarFallback className="bg-primary/10 text-primary text-xs">
                  {item.agentName.charAt(0).toUpperCase()}
                </AvatarFallback>
              </Avatar>
              <span className="text-sm font-medium">{item.agentName}</span>
            </div>
          )}

          {/* Only render display components */}
          <div className="mt-4">
            <DisplayComponent component={item.content as MessageDisplayComponentPublic} />
          </div>
        </motion.div>
      ))}
    </ScrollArea>
  );
}

export default ChartsTab;
