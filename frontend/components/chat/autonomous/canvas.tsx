'use client'

import { useEffect, useState, useRef, useMemo, useCallback } from 'react';
import { cn } from '@/lib/utils';
import React from 'react';
import { Bar<PERSON>hart as Database, ListChecks, Wrench, Table2 } from 'lucide-react';
import { ToolCall } from '../types';
import { MessageDisplayComponentPublic, ResourceRead, RecommendationPublic } from '@/client/types.gen';
import dynamic from 'next/dynamic';
import Image from 'next/image';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { useAgents } from '@/hooks/use-agents';
import { AgentsService } from '@/client';
import { Switch } from '@/components/ui/switch';
import { toast } from 'sonner';
import { Loader2 } from 'lucide-react';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger
} from '@/components/ui/tooltip';
import { Message } from '@/types/chat';
import { TabsContainer } from './tabs-container';

// Dynamically import tab components
const ResourceTab = dynamic(() => import('./resource-tab'));
const ChartsTab = dynamic(() => import('./charts-tab'));
const PlanningTab = dynamic(() => import('./planning-tab'));
const ToolsTab = dynamic(() => import('./tools-tab'));

// Canvas item with agent information
interface CanvasItemWithAgent {
  id: string;
  type: 'toolCall' | 'displayComponent';
  content: ToolCall | MessageDisplayComponentPublic;
  agentName?: string;
  agentRole?: string;
  messageIndex: number; // Changed from optional to required to match chat-panel.tsx
}

interface ChartsCanvasProps {
  isVisible: boolean;
  toolCalls: ToolCall[];
  displayComponents?: MessageDisplayComponentPublic[];
  canvasItemsWithAgent?: CanvasItemWithAgent[];
  className?: string;
  resourceData?: ResourceRead;
  streamingRecommendations?: RecommendationPublic[];
  setStreamingRecommendations?: React.Dispatch<React.SetStateAction<RecommendationPublic[]>>;
  isStreaming?: boolean;
  defaultTab?: 'resource' | 'charts' | 'planning' | 'tools';
  onTabChange?: (tab: 'resource' | 'charts' | 'planning' | 'tools') => void;
  lastMessage?: Message;
}

// Combined interface for all items that can be shown in the canvas
export interface CanvasItem {
  id: string;
  type: 'toolCall' | 'displayComponent';
  timestamp: Date;
  content: ToolCall | MessageDisplayComponentPublic;
  status?: 'running' | 'completed' | 'error';
  agentName?: string;
  agentRole?: string;
  messageIndex: number; // Changed from optional to required
}

export function ChartsCanvas({
  isVisible,
  toolCalls,
  displayComponents = [],
  canvasItemsWithAgent = [],
  className,
  resourceData,
  streamingRecommendations = [],
  setStreamingRecommendations = () => {},
  isStreaming = false,
  defaultTab = 'charts',
  onTabChange,
  lastMessage,
}: ChartsCanvasProps) {
  const [canvasItems, setCanvasItems] = useState<CanvasItem[]>([]);
  const [activeTab, setActiveTab] = useState<'resource' | 'charts' | 'planning' | 'tools' | null>(null);
  const [planningTools, setPlanningTools] = useState<ToolCall[]>([]);

  // Team agents state management
  const queryClient = useQueryClient();
  const { data: agentsResponse, isLoading: isAgentsLoading } = useAgents();
  const agents = agentsResponse?.data;
  // Track optimistic state for each agent
  const [optimisticStates, setOptimisticStates] = useState<Record<string, boolean>>({});

  // Create a map for agent information by tool ID
  const [toolAgentMap, setToolAgentMap] = useState<Record<string, {name: string; role: string}>>({});

  // Handle tab change
  const handleTabChange = useCallback((tabId: string) => {
    setActiveTab(tabId as 'resource' | 'charts' | 'planning' | 'tools');
    if (onTabChange) {
      onTabChange(tabId as 'resource' | 'charts' | 'planning' | 'tools');
    }
  }, [onTabChange]);

  // Process planning tool calls - keep this to populate planningTools but remove tab switching
  useEffect(() => {
    if (toolCalls && toolCalls.length > 0) {
      // Extract planning tool calls by name
      const planningToolCalls = toolCalls.filter(tool =>
        tool.name === 'planning' ||
        tool.name.includes('planning') ||
        tool.name.includes('plan')
      );

      // Update planning tools state if there are planning tools
      if (planningToolCalls.length > 0) {
        setPlanningTools(planningToolCalls);
      }
    }
  }, [toolCalls]);

  // Reset planningTools when conversation changes (lastMessage changes)
  useEffect(() => {
    setPlanningTools([]);
    if (toolCalls && toolCalls.length > 0) {
      const planningToolCalls = toolCalls.filter(tool =>
        tool.name === 'planning' ||
        tool.name.includes('planning') ||
        tool.name.includes('plan')
      );
      if (planningToolCalls.length > 0) {
        setPlanningTools(planningToolCalls);
      }
    }
  }, [lastMessage]);

  // Build the agent map for tools
  useEffect(() => {
    // Reset the map first to avoid stale data
    const newAgentMap: Record<string, {name: string; role: string}> = {};

    // Look through canvasItemsWithAgent to find tool calls with agent info
    canvasItemsWithAgent.forEach(item => {
      // Add more defensive checks
      if (!item || !item.content) {
        return;
      }

      if (item.type === 'toolCall' && item.agentName) {
        const tool = item.content as ToolCall;

        // Check if tool has valid ID
        if (!tool || !tool.id) {
          return;
        }

        newAgentMap[tool.id] = {
          name: item.agentName,
          role: item.agentRole || item.agentName || ''
        };
      }
    });

    // Also look through messages to extract agent information for
    // (Only set this if not already found in canvasItemsWithAgent)
    toolCalls.forEach(tool => {
      // Add defensive check for tool ID
      if (!tool || !tool.id) {
        return;
      }

      if (!newAgentMap[tool.id]) {
        // Check if tool call has agent information directly attached
        if (tool.agentRole || tool.agentName) {
          const agentInfo = tool.agentRole || tool.agentName || 'assistant';
          newAgentMap[tool.id] = {
            name: agentInfo,
            role: agentInfo
          };
        } else {
          // Use a default agent name if we can't determine it
          newAgentMap[tool.id] = {
            name: 'assistant',
            role: 'assistant'
          };
        }
      } else {
      }
    });

    setToolAgentMap(newAgentMap);
  }, [canvasItemsWithAgent, toolCalls]);

  // Standardize on using canvasItemsWithAgent as the primary data source
  useEffect(() => {
    // Process data from canvasItemsWithAgent (preferred) or fall back to displayComponents
    if (canvasItemsWithAgent.length > 0) {
      // Filter to only include display components - tool calls are handled separately
      const items: CanvasItem[] = canvasItemsWithAgent
        .filter(item => item.type === 'displayComponent')
        .map(item => {
          const component = item.content as MessageDisplayComponentPublic;
          return {
            id: item.id,
            type: 'displayComponent',
            timestamp: component.created_at ? new Date(component.created_at) : new Date(),
            content: component,
            agentName: item.agentName,
            agentRole: item.agentRole,
            messageIndex: item.messageIndex
          };
        });

      // Simplified sorting logic - always prioritize message index if available
      const sortedItems = items.sort((a, b) => {
        // First try to sort by messageIndex
        if (a.messageIndex !== undefined && b.messageIndex !== undefined) {
          return a.messageIndex - b.messageIndex;
        }
        // Fall back to timestamp if message index is not available
        return a.timestamp.getTime() - b.timestamp.getTime();
      });

      setCanvasItems(sortedItems);
    }
    // Only use displayComponents if canvasItemsWithAgent is empty
    else if (displayComponents.length > 0) {
      const displayComponentItems: CanvasItem[] = displayComponents.map(component => ({
        id: component.id,
        type: 'displayComponent',
        timestamp: component.created_at ? new Date(component.created_at) : new Date(),
        content: component,
        messageIndex: 0 // Provide a default message index when not available
      }));

      // Sort chronologically (oldest first)
      const sortedItems = displayComponentItems.sort((a, b) =>
        a.timestamp.getTime() - b.timestamp.getTime()
      );

      setCanvasItems(sortedItems);
    }
    else {
      setCanvasItems([]);
    }
  }, [canvasItemsWithAgent, displayComponents]);

  // Helper function to check if an agent is speaking
  const isAgentSpeaking = (agentTitle: string | undefined) => {
    if (!lastMessage || !isStreaming) return false;
    return lastMessage.role?.toLowerCase() === agentTitle?.toLowerCase().split(" ")[0];
  };

  const toggleActiveMutation = useMutation({
    mutationFn: async ({ id, isActive }: { id: string; isActive: boolean }) => {
      return AgentsService.updateAgent({
        id,
        requestBody: {
          is_active: isActive
        }
      });
    },
    onSuccess: (_data, variables) => {
      toast.success(
        variables.isActive ? 'Agent activated' : 'Agent deactivated'
      );

      // Invalidate and refetch the agents query
      queryClient.invalidateQueries({ queryKey: ['agents'] });
    },
    onError: (_, variables) => {
      // Revert optimistic update on error
      setOptimisticStates((prev: Record<string, boolean>) => ({
        ...prev,
        [variables.id]: !variables.isActive
      }));
      toast.error('Failed to update agent status');
    }
  });

  // Handle toggle with optimistic updates
  const handleToggleActive = (id: string, currentActive: boolean) => {
    // Apply optimistic update immediately
    setOptimisticStates((prev: Record<string, boolean>) => ({
      ...prev,
      [id]: !currentActive
    }));

    // Call the actual mutation
    toggleActiveMutation.mutate({
      id,
      isActive: !currentActive
    });
  };

  // Sort agents by title to maintain consistent order
  const sortedAgents = agents && Array.isArray(agents)
    ? [...agents].sort((a, b) => a.title.localeCompare(b.title))
    : [];

  // Combine supervisor with other conversation agents
  const conversationAgents = [
    ...(sortedAgents.filter(agent => agent.type === 'conversation_agent') || [])
  ];

  // Conditionally render based on isVisible only
  if (!isVisible) return null;

  // Pre-render calculation of dimensions
  const totalCount = canvasItems.length;
  const planningCount = new Set(planningTools.map(tool => {
    try {
      const output = JSON.parse(tool?.output || '{}');
      return output.data?.plan_id || tool.name;
    } catch {
      return tool.name;
    }
  })).size;

  // Count general tools (not planning, charts, or tables)
  const generalToolsCount = useMemo(() => {
    return toolCalls.filter(tool => {
      // Skip planning tools
      if (tool.name === 'planning' ||
          tool.name.includes('planning') ||
          tool.name.includes('plan')) {
        return false;
      }

      // Skip group chat tools
      if (tool.name === 'group_chat') {
        return false;
      }

      // Skip chart or display component tools
      if (tool.name.includes('chart') ||
          tool.name.includes('display_component') ||
          tool.name.includes('create_chart') ||
          tool.name.includes('table')) {
        return false;
      }

      // Skip thought tools (aligning with ToolsTab filtering)
      if (tool.name === 'thought' ||
          tool.name.includes('thinking') ||
          tool.name.includes('thought')) {
        return false;
      }

      return true;
    }).length;
  }, [toolCalls]);

  // Check if we should show the planning tab
  const shouldShowPlanningTab = planningTools.length > 0;

  // Check if we should show the tools tab
  const shouldShowToolsTab = generalToolsCount > 0;

  // Check if we should show the charts tab
  const shouldShowChartsTab = canvasItems.length > 0;

  // Auto-select the first available tab (priority: resource → charts → planning → tools)
  const availableTabs = useMemo(() => {
    const tabs: ('resource' | 'charts' | 'planning' | 'tools')[] = [];
    if (resourceData) tabs.push('resource');
    if (shouldShowChartsTab) tabs.push('charts');
    if (shouldShowPlanningTab) tabs.push('planning');
    if (shouldShowToolsTab) tabs.push('tools');
    return tabs;
  }, [resourceData, shouldShowChartsTab, shouldShowPlanningTab, shouldShowToolsTab]);

  // Auto-select tab logic
  useEffect(() => {
    if (availableTabs.length > 0) {
      // If defaultTab is provided and available, use it
      if (defaultTab && availableTabs.includes(defaultTab)) {
        setActiveTab(defaultTab);
      }
      // If no active tab is set, select first available
      else if (!activeTab) {
        setActiveTab(availableTabs[0]);
      }
    } else {
      // No tabs available, reset to null
      setActiveTab(null);
    }
  }, [availableTabs, defaultTab]); // Removed activeTab from dependencies to prevent override of manual changes

  // Handle case where current active tab becomes unavailable
  useEffect(() => {
    if (activeTab && availableTabs.length > 0 && !availableTabs.includes(activeTab)) {
      // Current tab is no longer available, switch to first available
      setActiveTab(availableTabs[0]);
    }
  }, [activeTab, availableTabs]);

  // Memoize the content of each tab to prevent unnecessary re-renders
  const resourceTabContent = useMemo(() => (
    resourceData && (
      <ResourceTab
        resource={resourceData}
        streamingRecommendations={streamingRecommendations}
        setStreamingRecommendations={setStreamingRecommendations}
        isStreaming={isStreaming}
      />
    )
  ), [resourceData, streamingRecommendations, setStreamingRecommendations, isStreaming]);

  const chartsTabContent = useMemo(() => (
    shouldShowChartsTab && (
      <ChartsTab canvasItems={canvasItems} />
    )
  ), [shouldShowChartsTab, canvasItems]);

  const planningTabContent = useMemo(() => (
    <PlanningTab
      planningTools={planningTools}
    />
  ), [planningTools, toolAgentMap, activeTab]);

  const toolsTabContent = useMemo(() => (
    <ToolsTab toolCalls={toolCalls} agentMap={toolAgentMap} />
  ), [toolCalls, toolAgentMap]);

  return (
    <>
      {isVisible && (
        <div
          className={cn(
            "h-full border-l border-border bg-background z-10 flex flex-col shadow-lg",
            className
          )}
        >
          {/* Canvas Header - Only Team Agents */}
          <div className="relative z-20 border-b border-border bg-background/95 backdrop-blur-sm">
            {/* Team Agents Section */}
            <div className="border-b border-border">
              <div className="flex p-2 gap-2 overflow-x-auto">
                {isAgentsLoading ? (
                  // Loading state
                  <>
                    {[...Array(5)].map((_, i) => (
                      <div key={i} className="p-1.5 flex-shrink-0">
                        <div className="w-10 h-10 rounded-full bg-muted/50 animate-pulse" />
                      </div>
                    ))}
                  </>
                ) : (
                  // Render agents
                  <>
                    {conversationAgents.map((agent) => {
                      const isSpeaking = isAgentSpeaking(agent.title);
                      const agentName = agent.title.split(" ")[0].toLowerCase();
                      const avatarPath = `/avatars/${agentName}.webp`;

                      // Determine active state: prioritize optimistic state, fallback to query data
                      const isActive = optimisticStates[agent.id] !== undefined
                        ? optimisticStates[agent.id]
                        : agent.is_active === true;

                      return (
                        <TooltipProvider key={agent.id}>
                          <Tooltip delayDuration={0}>
                            <TooltipTrigger asChild>
                              <div
                                className={cn(
                                  "relative p-1.5 group rounded-lg flex-shrink-0",
                                  isSpeaking ? [
                                    "bg-primary/10",
                                    "scale-105",
                                    "shadow-lg shadow-primary/10"
                                  ].join(" ") : [
                                    "hover:bg-muted/50",
                                    "hover:scale-102",
                                    "hover:shadow-md"
                                  ].join(" "),
                                  !agent.is_active && "opacity-50"
                                )}
                              >
                                <div className="relative flex items-center justify-between group">
                                  <div className={cn(
                                    "w-10 h-10 rounded-full overflow-hidden",
                                    isSpeaking ? [
                                      "ring-2 ring-primary",
                                      "shadow-lg shadow-primary/20",
                                      "scale-105"
                                    ].join(" ") : [
                                      "ring-1",
                                      agent.is_active ? "ring-border" : "ring-muted",
                                      "group-hover:ring-primary/30",
                                      "group-hover:shadow-sm"
                                    ].join(" ")
                                  )}>
                                    <Image
                                      src={avatarPath}
                                      alt={agent.title || 'Team member'}
                                      unoptimized
                                      width={40}
                                      height={40}
                                      className={cn(
                                        "object-cover",
                                        isSpeaking && "scale-105",
                                        "group-hover:scale-102",
                                        !isActive && "grayscale"
                                      )}
                                      onError={(e) => {
                                        const target = e.target as HTMLImageElement;
                                        target.src = "/avatars/cloud.webp";
                                      }}
                                    />
                                  </div>

                                  <div className="absolute -right-0.5 -bottom-0.5 w-2 h-2">
                                    <div className={cn(
                                      "w-2 h-2 rounded-full",
                                      "ring-1 ring-background",
                                      isSpeaking
                                        ? "bg-primary animate-[glow_4s_ease-in-out_infinite]"
                                        : isActive ? "bg-emerald-500"
                                          : "bg-gray-400"
                                    )} />
                                  </div>
                                </div>
                              </div>
                            </TooltipTrigger>
                            <TooltipContent
                              side="bottom"
                              align="start"
                              className={cn(
                                "p-3 bg-popover/95 backdrop-blur-sm border border-border",
                                "z-50", // Higher z-index
                                "data-[state=delayed-open]:animate-in",
                                "data-[state=closed]:animate-out",
                                "data-[state=closed]:fade-out-0",
                                "data-[state=open]:fade-in-0",
                                "data-[state=closed]:zoom-out-95",
                                "data-[state=open]:zoom-in-95",
                                "shadow-md"
                              )}
                              sideOffset={10}
                            >
                              <div className="flex flex-col gap-3 w-[240px]">
                                <div className="flex items-center justify-between">
                                  <div className="flex items-center gap-2">
                                    <div className={cn(
                                      "w-8 h-8 rounded-full overflow-hidden",
                                      isSpeaking ? "ring-2 ring-primary" : "ring-1 ring-border",
                                      (optimisticStates[agent.id] === false || (optimisticStates[agent.id] === undefined && !agent.is_active)) && "grayscale"
                                    )}>
                                      <Image
                                        src={avatarPath}
                                        alt={agent.title || 'Team member'}
                                        width={32}
                                        height={32}
                                        className="object-cover"
                                        onError={(e) => {
                                          const target = e.target as HTMLImageElement;
                                          target.src = "/avatars/cloud.webp";
                                        }}
                                      />
                                    </div>
                                    <div className="flex flex-col">
                                      <div className="flex items-center gap-1.5">
                                        <p className="text-sm font-medium text-foreground truncate">
                                          {agent.title?.split(" (")[0]}
                                          <span className="text-muted-foreground">
                                            {" "}({agent.title?.split(" (")[1]?.replace(")", "") || "Engineer"})
                                          </span>
                                        </p>
                                      </div>
                                      <div className="flex items-center gap-1.5">
                                        <div className={cn(
                                          "w-1.5 h-1.5 rounded-full",
                                          isSpeaking
                                            ? "bg-primary"
                                            : (optimisticStates[agent.id] === true || (optimisticStates[agent.id] === undefined && agent.is_active))
                                              ? "bg-emerald-500"
                                              : "bg-gray-400"
                                        )} />
                                        <span className={cn(
                                          "text-xs",
                                          isSpeaking
                                            ? "text-primary"
                                            : (optimisticStates[agent.id] === true || (optimisticStates[agent.id] === undefined && agent.is_active))
                                              ? "text-emerald-500"
                                              : "text-gray-400"
                                        )}>
                                          {isSpeaking ? "Speaking" : agent.is_active ? "Available" : "Inactive"}
                                        </span>
                                      </div>
                                    </div>
                                  </div>
                                  <div className="relative">
                                    {toggleActiveMutation.isPending && toggleActiveMutation.variables?.id === agent.id && (
                                      <div className="absolute inset-0 flex items-center justify-center bg-popover/30 backdrop-blur-[1px] rounded-xl z-10">
                                        <Loader2 className="h-3 w-3 animate-spin text-primary" />
                                      </div>
                                    )}
                                    <div className="flex items-center gap-1.5">
                                      <Switch
                                        checked={optimisticStates[agent.id] === true || (optimisticStates[agent.id] === undefined && agent.is_active === true)}
                                        onCheckedChange={() => handleToggleActive(
                                          agent.id,
                                          optimisticStates[agent.id] === true || (optimisticStates[agent.id] === undefined && agent.is_active === true)
                                        )}
                                        disabled={toggleActiveMutation.isPending && toggleActiveMutation.variables?.id === agent.id}
                                        className={cn(
                                          "scale-75 origin-right",
                                          (optimisticStates[agent.id] === true || (optimisticStates[agent.id] === undefined && agent.is_active === true)) ?
                                            "data-[state=checked]:bg-green-500 dark:data-[state=checked]:bg-green-600" :
                                            "data-[state=unchecked]:bg-gray-200 dark:data-[state=unchecked]:bg-gray-700"
                                        )}
                                      />
                                    </div>
                                  </div>
                                </div>
                                <div className="text-xs leading-relaxed text-muted-foreground">
                                  {agent.description}
                                </div>
                              </div>
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                      );
                    })}
                  </>
                )}
              </div>
            </div>
          </div>

          {/* Use TabsContainer component AFTER the header */}
          {availableTabs.length > 0 && activeTab && (
            <TabsContainer
              tabs={[
                ...(resourceData ? [{
                  id: 'resource',
                  label: 'Resource',
                  icon: <Database className="h-4 w-4" />
                }] : []),
                ...(shouldShowChartsTab ? [{
                  id: 'charts',
                  label: 'Chart & Table',
                  icon: <Table2 className="h-4 w-4" />,
                  badge: totalCount
                }] : []),
                ...(shouldShowPlanningTab ? [{
                  id: 'planning',
                  label: 'Planning',
                  icon: <ListChecks className="h-4 w-4" />,
                  badge: planningCount
                }] : []),
                ...(shouldShowToolsTab ? [{
                  id: 'tools',
                  label: 'Tools',
                  icon: <Wrench className="h-4 w-4" />,
                  badge: generalToolsCount
                }] : [])
              ]}
              activeTab={activeTab}
              onTabChange={(tabId) => handleTabChange(tabId as 'resource' | 'charts' | 'planning' | 'tools')}
            >
              {/* Content Area - Passed as children to TabsContainer */}
              <div className="flex-1 overflow-hidden relative h-full">
                {activeTab === 'resource' && resourceTabContent}
                {activeTab === 'charts' && chartsTabContent}
                {activeTab === 'planning' && planningTabContent}
                {activeTab === 'tools' && toolsTabContent}
                {/* Individual tabs handle scrolling via ScrollArea */}
              </div>
            </TabsContainer>
          )}
        </div>
      )}
    </>
  );
}
