// components/chat/types.ts
import { MessageActionType, MessageDisplayComponentType, ChartType, MessageDisplayComponentPublic } from '@/client/types.gen';
import { InterruptConfirmation } from '@/hooks/message-stream';

export interface Attachment {
  name: string;
  size: number;
  type: string;
  url?: string;
}

export interface ToolCall {
  id: string;
  name: string;
  arguments: string;
  output?: string;
  error?: string;
  status: 'running' | 'completed' | 'error';
  startTime?: Date;
  endTime?: Date;
  reasoning?: string;
  position?: number;
  thought?: string;
  agentRole?: string;
  agentName?: string;
}

export interface DisplayComponent {
  id: string;
  type: MessageDisplayComponentType;
  chart_type: ChartType | null;
  title: string | null;
  description: string | null;
  data: Record<string, unknown>;
  config: Record<string, unknown>;
  position: number;
  created_at: string;
}

// Update Message interface
export interface Message {
  id: string;
  role: string;
  content: string;
  toolCalls?: ToolCall[];
  attachments?: { name: string; size: number; }[];
  onRestore?: boolean;
  timestamp: Date;
  actionType?: MessageActionType;
  recommendationResponse?: RecommendationResponse | null;
  displayComponents?: MessageDisplayComponentPublic[];
  confirmation?: InterruptConfirmation | null;
  isInterrupt?: boolean;
  interruptMessage?: string;
}

export interface ContentPart {
  type: 'thinking' | 'content';
  content: string;
}

export interface Session {
  id: string;
  title: string;
  timestamp: Date;
  category?: string;
  lastMessage?: Message;
}

export interface SessionUpdate {
  id: string;
  name: string;
}

export interface SessionCreate {
  name: string;
}

export interface SessionResponse {
  sessions: Session[];
}

export interface RecommendationData {
  type: string;
  title: string;
  description: string;
  potential_savings: number;
  effort: string;
  risk: string;
  status: string;
}

export interface RecommendationResponse {
  recommendations: RecommendationData[];
}

export interface RecommendationStreamEvent {
  type: 'on_start_recommendation_generation'
    | 'on_end_recommendation_generation'
    | 'on_recommendation_generation_response';
  content: string | RecommendationResponse;
}

// Agent types for @ mention feature
export interface Agent {
  id: string;
  name: string;
  avatar?: string;
  description?: string;
  locked?: boolean;
}

// Resource types for # mention feature
export interface ResourceItem {
  id: string;
  title: string;
  description: string;
  subtitle?: string;
  metadata?: ResourceItemMetadata;
}

export interface ResourceCategory {
  id: string;
  name: string;
  icon: React.ReactNode;
  items: ResourceItem[];
  isDynamic?: boolean;
  source?: string;
}

// Message input component props
export interface MessageInputProps {
  onSendMessage: (content: string) => void;
  disabled?: boolean;
  value?: string;
  onChange?: (value: string) => void;
  isStreaming?: boolean;
  onStop?: () => void;
  showExampleQuestions?: boolean;
  isEmptyConversation?: boolean;
  resourceType?: string;
  quotaInfo?: {
    quota_used: number;
    quota_limit: number;
    quota_remaining: number;
    usage_percentage: number;
  };
}

// Props for sub-components
export interface ModelSelectorProps {
  selectedModelId: string;
  onSelectModel: (modelId: string) => void;
  isDropdownOpen: boolean;
  setIsDropdownOpen: (isOpen: boolean) => void;
  models: Array<{
    id: string;
    name: string;
    version?: string;
    locked?: boolean;
  }>;
}

export interface MentionDropdownProps {
  isVisible: boolean;
  position: { top: number; left: number };
  filter: string;
  onSelect: (itemId: string, fullPath: string) => void;
  onClose: () => void;
}

export interface ResourceMentionProps extends MentionDropdownProps {
  categories: ResourceCategory[];
  dropdownRef?: React.RefObject<HTMLDivElement>;
}

export interface AgentMentionProps extends MentionDropdownProps {
  // No additional props needed, agents are fetched directly in the component
}

export interface QuotaIndicatorProps {
  quotaInfo?: {
    quota_used: number;
    quota_limit: number;
    quota_remaining: number;
    usage_percentage: number;
  };
}

export interface ActionButtonProps {
  isStreaming: boolean;
  hasContent: boolean;
  disabled?: boolean;
  onSubmit: () => void;
  onStop?: () => void;
}

// Update the ResourceItemMetadata interface to include usage_mode and other fields
export interface ResourceItemMetadata {
  access_level: 'private' | 'shared';
  usage_mode: 'manual' | 'agent_requested' | 'always';
  tags: string[];
  createdAt: string;
  updatedAt: string;
  formattedCreatedAt: string;
  formattedUpdatedAt: string;
  fullDescription: string;
  allowed_users: string[];
}
