import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { NotificationsService, AlertsService } from '@/client';
import { toast } from 'sonner';
import type { NotificationResponse, NotificationType, NotificationStatus } from '@/client/types.gen';
import { useState, useCallback, useEffect, useMemo, useRef } from 'react';
import type { Alert, AlertSeverity, AlertStatus } from '@/app/(dashboard)/alerts/_components/types';

export type Notification = NotificationResponse;

// Define filter types for better type safety
export type NotificationFilter = {
  status?: ('unread' | 'read' | 'archived')[];
  type?: NotificationType[];
  requiresAction?: boolean;
  timeframe?: 'today' | 'week' | 'month' | 'all';
};

// Debounce function to limit how often a function can be called
const debounce = (func: Function, delay: number) => {
  let timeoutId: NodeJS.Timeout;
  return (...args: any[]) => {
    if (timeoutId) {
      clearTimeout(timeoutId);
    }
    timeoutId = setTimeout(() => func(...args), delay);
  };
};

// Map API severity to local severity
const mapSeverity = (severity: string): AlertSeverity => {
  const severityMap: { [key: string]: AlertSeverity } = {
    'CRITICAL': 'critical',
    'HIGH': 'high',
    'MEDIUM': 'medium',
    'LOW': 'low',
    'INFO': 'low'
  };
  return severityMap[severity] || 'low';
};

// Map API status to local status
const mapStatus = (status: string): AlertStatus => {
  const statusMap: { [key: string]: AlertStatus } = {
    'OPEN': 'triggered',
    'ACKNOWLEDGED': 'acknowledged',
    'RESOLVED': 'resolved',
    'CLOSED': 'resolved'
  };
  return statusMap[status] || 'triggered';
};

export const useNotifications = () => {
  const queryClient = useQueryClient();
  const [paginationParams, setPaginationParams] = useState({
    skip: 0,
    limit: 10,
    requiresAction: undefined as boolean | undefined,
    type: undefined as NotificationType[] | undefined
  });

  // Active filters state
  const [activeFilters, setActiveFilters] = useState<NotificationFilter>({
    status: ['unread', 'read'],
    type: undefined,
    requiresAction: undefined,
    timeframe: 'all'
  });

  // Store all loaded notifications to support infinite scrolling
  const [allLoadedNotifications, setAllLoadedNotifications] = useState<Notification[]>([]);

  // Track unread count separately
  const [unreadCount, setUnreadCount] = useState(0);

  // Track if we're currently loading more notifications
  const isLoadingMore = useRef(false);

  // Reference to store the previous scroll position
  const scrollPositionRef = useRef<number>(0);

  // Add alerts state
  const [alerts, setAlerts] = useState<Alert[]>([]);

  // Update alerts query to use AlertsService
  const { data: alertsData, isLoading: isAlertsLoading } = useQuery({
    queryKey: ['alerts', 'list'],
    queryFn: async () => {
      try {
        const response = await AlertsService.listAlerts({
          status: 'OPEN', // Get open alerts (mapped to triggered)
          sortBy: 'created_at',
          sortDesc: true,
          limit: 10
        });

        // Map API response to Alert type
        const mappedAlerts = (response.data || []).map(alert => ({
          title: alert.title,
          id: alert.id,
          severity: mapSeverity(alert.severity),
          startTime: alert.created_at,
          description: alert.description,
          events: 1, // Default to 1 event since API doesn't have this field
          issue: null, // API doesn't have this field
          assignee: null, // API doesn't have this field
          status: mapStatus(alert.status)
        }));

        return mappedAlerts;
      } catch (error) {
        console.error('Error fetching alerts:', error);
        return [];
      }
    },
    staleTime: 60000, // 1 minute stale time (increased from 10s)
    gcTime: 300000, // Keep in cache for 5 minutes
    refetchInterval: 120000, // Reduce frequency to 2 minutes from 30 seconds
    enabled: true, // Explicitly enable the query
    retry: 2, // Retry failed requests twice
  });

  // Query for notifications, combining the previous separate queries
  const {
    data,
    isLoading: loading,
    error,
    refetch,
    isFetching
  } = useQuery({
    queryKey: ['notifications', paginationParams],
    queryFn: async () => {
      const response = await NotificationsService.listNotifications({
        skip: paginationParams.skip,
        limit: paginationParams.limit,
        requiresAction: paginationParams.requiresAction,
        requestBody: paginationParams.type
      });

      // Calculate unread count from the results
      if (paginationParams.skip === 0) {
        const unreadNotifications = response.data.filter(
          notification => notification.status === 'unread'
        );
        setUnreadCount(unreadNotifications.length);
      }

      return response;
    },
    staleTime: 30000, // 30 seconds stale time
    refetchOnWindowFocus: true,
    refetchInterval: 30000, // Poll every 30 seconds
  });

  // Simplified scroll position management
  const saveScrollPosition = useCallback(() => {
    const scrollContainer = document.querySelector('.custom-scrollbar');
    if (scrollContainer) {
      scrollPositionRef.current = scrollContainer.scrollTop;
    }
  }, []);

  const restoreScrollPosition = useCallback(() => {
    // Use requestAnimationFrame to ensure the DOM has updated
    requestAnimationFrame(() => {
      const scrollContainer = document.querySelector('.custom-scrollbar');
      if (scrollContainer && scrollPositionRef.current > 0) {
        scrollContainer.scrollTop = scrollPositionRef.current;
      }
    });
  }, []);

  // Update allLoadedNotifications when data changes
  useEffect(() => {
    if (data?.data) {
      if (paginationParams.skip === 0) {
        // Reset the list if we're loading the first page
        setAllLoadedNotifications(data.data);
        // Reset scroll position for new searches
        scrollPositionRef.current = 0;

        // Update unread count when loading first page
        const unreadNotifications = data.data.filter(
          notification => notification.status === 'unread'
        );
        setUnreadCount(unreadNotifications.length);
      } else {
        // Append new notifications to the existing list
        // Filter out duplicates by ID
        const newNotifications = data.data.filter(
          newNotif => !allLoadedNotifications.some(existingNotif => existingNotif.id === newNotif.id)
        );

        if (newNotifications.length > 0) {
          setAllLoadedNotifications(prev => [...prev, ...newNotifications]);
          // Restore scroll position after adding more items
          restoreScrollPosition();
        }
      }

      // Reset loading more flag
      isLoadingMore.current = false;
    }
  }, [data, paginationParams.skip, allLoadedNotifications, restoreScrollPosition]);

  // Update alerts when data changes
  useEffect(() => {
    if (alertsData) {
      setAlerts(alertsData);
    }
  }, [alertsData]);

  // Ensure we have valid data
  const notifications = allLoadedNotifications;
  const totalCount = data?.count || 0;
  const allNotificationsCount = totalCount;

  // Group notifications by date for better UI organization
  const groupedNotifications = useMemo(() => {
    const groups: Record<string, Notification[]> = {
      today: [],
      yesterday: [],
      thisWeek: [],
      thisMonth: [],
      older: []
    };

    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate()).getTime();
    const yesterday = today - 86400000;
    const thisWeek = today - 6 * 86400000;
    const thisMonth = today - 30 * 86400000;

    notifications.forEach(notification => {
      const createdAt = new Date(notification.created_at || '').getTime();

      if (createdAt >= today) {
        groups.today.push(notification);
      } else if (createdAt >= yesterday) {
        groups.yesterday.push(notification);
      } else if (createdAt >= thisWeek) {
        groups.thisWeek.push(notification);
      } else if (createdAt >= thisMonth) {
        groups.thisMonth.push(notification);
      } else {
        groups.older.push(notification);
      }
    });

    return groups;
  }, [notifications]);

  // Function to fetch notifications with different parameters
  const fetchNotifications = useCallback((params: Partial<typeof paginationParams> & Partial<NotificationFilter>) => {
    // Update filters if provided
    if (params.status || params.type || params.requiresAction || params.timeframe) {
      setActiveFilters(prev => ({
        ...prev,
        ...params
      }));
    }

    // Reset allLoadedNotifications when changing filters
    if (params.status || params.type || params.requiresAction || params.timeframe) {
      setAllLoadedNotifications([]);
    }

    // Update pagination params
    setPaginationParams(prev => ({
      ...prev,
      ...params,
      // Reset skip when changing filters
      skip: params.status || params.type || params.requiresAction ? 0 : params.skip || prev.skip
    }));
  }, []);

  // Load more notifications (for infinite scrolling) with debouncing
  const loadMoreRaw = useCallback(() => {
    if (notifications.length < totalCount && !loading && !isFetching && !isLoadingMore.current) {
      // Save scroll position before loading more
      saveScrollPosition();

      // Set loading more flag to prevent multiple calls
      isLoadingMore.current = true;

      setPaginationParams(prev => ({
        ...prev,
        skip: prev.skip + prev.limit
      }));
    }
  }, [notifications.length, totalCount, loading, isFetching, saveScrollPosition]);

  // Debounced version of loadMore to prevent rapid firing
  const loadMore = useMemo(() => debounce(loadMoreRaw, 300), [loadMoreRaw]);

  // Mark notification as read with optimistic updates
  const markAsRead = useMutation({
    mutationFn: (id: string) => NotificationsService.markNotificationRead({ notificationId: id }),
    onMutate: async (id) => {
      // Save scroll position before updating
      saveScrollPosition();

      // Cancel any outgoing refetches
      await queryClient.cancelQueries({ queryKey: ['notifications'] });

      // Snapshot the previous value
      const previousNotifications = queryClient.getQueryData(['notifications', paginationParams]);

      // Optimistically update the notification status in the loaded notifications
      setAllLoadedNotifications(prev =>
        prev.map(n => {
          if (n.id === id) {
            return {
              ...n,
              status: 'read' as NotificationStatus,
              read_at: new Date().toISOString()
            };
          }
          return n;
        })
      );

      // Check if this notification was unread before and update the count
      const wasUnread = notifications.find(n => n.id === id)?.status === 'unread';
      if (wasUnread) {
        setUnreadCount(prev => Math.max(0, prev - 1));
      }

      // Return a context object with the snapshotted value
      return { previousNotifications, wasUnread };
    },
    onSuccess: (_, id, context) => {
      // Update the notification status in the cache for the current view
      queryClient.setQueryData<{ data: Notification[], total: number }>(['notifications', paginationParams], (prev) => {
        if (!prev) return { data: [], total: 0 };

        // Update the notification status in the cache
        const updatedData = prev.data.map(n => {
          if (n.id === id) {
            return {
              ...n,
              status: 'read' as NotificationStatus,
              read_at: new Date().toISOString()
            };
          }
          return n;
        });

        return {
          ...prev,
          data: updatedData
        };
      });

      // Restore scroll position after update
      restoreScrollPosition();
    },
    onError: (_, __, context) => {
      // If the mutation fails, use the context returned from onMutate to roll back
      if (context?.previousNotifications) {
        queryClient.setQueryData(['notifications', paginationParams], context.previousNotifications);
      }

      // Restore unread count if needed
      if (context?.wasUnread) {
        setUnreadCount(prev => prev + 1);
      }

      toast.error('Failed to mark notification as read');

      // Restore scroll position after error
      restoreScrollPosition();
    },
    onSettled: () => {
      // Refresh data to ensure local state matches server state
      setTimeout(() => {
        refetch();
      }, 500);
    }
  });

  // Mark all notifications as read
  const markAllAsRead = useMutation({
    mutationFn: () => NotificationsService.markAllNotificationsRead(),
    onMutate: async () => {
      // Save scroll position before updating
      saveScrollPosition();

      // Cancel any outgoing refetches
      await queryClient.cancelQueries({ queryKey: ['notifications'] });

      // Snapshot the previous value
      const previousNotifications = queryClient.getQueryData(['notifications', paginationParams]);
      const previousUnreadCount = unreadCount;

      // Optimistically update all notifications to read status in the loaded notifications
      setAllLoadedNotifications(prev =>
        prev.map(n => ({
          ...n,
          status: 'read' as NotificationStatus,
          read_at: new Date().toISOString()
        }))
      );

      // Reset unread count
      setUnreadCount(0);

      // Return a context object with the snapshotted value
      return { previousNotifications, previousUnreadCount };
    },
    onSuccess: () => {
      // Update all notifications to read status in the current view
      queryClient.setQueryData<{ data: Notification[], total: number }>(['notifications', paginationParams], (prev) => {
        if (!prev) return { data: [], total: 0 };

        // Update all notifications to read status
        const updatedData = prev.data.map(n => ({
          ...n,
          status: 'read' as NotificationStatus,
          read_at: new Date().toISOString()
        }));

        return {
          ...prev,
          data: updatedData
        };
      });

      // Restore scroll position after update
      restoreScrollPosition();
    },
    onError: (_, __, context) => {
      // If the mutation fails, use the context returned from onMutate to roll back
      if (context?.previousNotifications) {
        queryClient.setQueryData(['notifications', paginationParams], context.previousNotifications);
      }

      // Restore unread count
      if (context?.previousUnreadCount !== undefined) {
        setUnreadCount(context.previousUnreadCount);
      }

      toast.error('Failed to mark all notifications as read');

      // Restore scroll position after error
      restoreScrollPosition();
    },
    onSettled: () => {
      // Refresh data to ensure local state matches server state
      setTimeout(() => {
        refetch();
      }, 500);
    }
  });

  // Handle action for notifications that require action
  const handleInterruptAction = useMutation({
    mutationFn: async ({ id, action }: { id: string; action: 'confirm' | 'cancel' }) => {
      // Save scroll position before action
      saveScrollPosition();

      const notification = notifications.find(n => n.id === id);
      if (!notification?.requires_action) return;

      // Mark as read using the existing endpoint
      await markAsRead.mutateAsync(id);

      return { success: true };
    },
    onSuccess: (_, { action }) => {
      toast.success(`Action ${action}ed successfully`);

      // Restore scroll position after success
      restoreScrollPosition();
    },
    onError: (_, { action }) => {
      toast.error(`Failed to ${action} action`);

      // Restore scroll position after error
      restoreScrollPosition();
    }
  });

  return {
    notifications,
    groupedNotifications,
    totalCount,
    unreadCount,
    allNotificationsCount,
    loading,
    isFetching,
    error,
    markAsRead,
    markAllAsRead,
    handleInterruptAction,
    fetchNotifications,
    loadMore,
    paginationParams,
    activeFilters,
    setActiveFilters,
    refetch,
    saveScrollPosition,
    restoreScrollPosition,
    alerts,
    isAlertsLoading
  };
};
