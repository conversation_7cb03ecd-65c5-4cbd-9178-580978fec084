export async function getCachedAvatar(role: string, fallback: string): Promise<string> {
    const key = `avatar_${role.toLowerCase()}`;
    if (typeof window === 'undefined') return fallback;
    const cached = localStorage.getItem(key);
    if (cached) return cached;
    try {
        const response = await fetch(`/avatars/${role.toLowerCase()}.webp`);
        if (!response.ok) throw new Error('Not found');
        const blob = await response.blob();
        const reader = new window.FileReader();
        return await new Promise<string>((resolve, reject) => {
            reader.onloadend = () => {
                const base64 = reader.result as string;
                localStorage.setItem(key, base64);
                resolve(base64);
            };
            reader.onerror = reject;
            reader.readAsDataURL(blob);
        });
    } catch {
        return fallback;
    }
}
