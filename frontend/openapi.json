{"openapi": "3.1.0", "info": {"title": "Cloud Thinker", "version": "0.1.0"}, "paths": {"/api/v1/login/access-token": {"post": {"tags": ["login"], "summary": "Login Access Token", "description": "OAuth2 compatible token login, get an access token for future requests", "operationId": "login_access_token", "requestBody": {"content": {"application/x-www-form-urlencoded": {"schema": {"$ref": "#/components/schemas/Body_login-login_access_token"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Token"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/login/test-token": {"post": {"tags": ["login"], "summary": "Test Token", "description": "Test access token", "operationId": "test_token", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserPublic"}}}}}, "security": [{"OAuth2PasswordBearer": []}]}}, "/api/v1/password-recovery/{email}": {"post": {"tags": ["login"], "summary": "Recover Password", "description": "Password Recovery", "operationId": "recover_password", "parameters": [{"name": "email", "in": "path", "required": true, "schema": {"type": "string", "title": "Email"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Message"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/reset-password/": {"post": {"tags": ["login"], "summary": "Reset Password", "description": "Reset password", "operationId": "reset_password", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/NewPassword"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Message"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/password-recovery-html-content/{email}": {"post": {"tags": ["login"], "summary": "Recover Password Html Content", "description": "HTML Content for Password Recovery", "operationId": "recover_password_html_content", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "email", "in": "path", "required": true, "schema": {"type": "string", "title": "Email"}}], "responses": {"200": {"description": "Successful Response", "content": {"text/html": {"schema": {"type": "string"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/users/": {"get": {"tags": ["users"], "summary": "Read Users", "description": "Retrieve users based on workspace relationship.\nOnly returns users that belong to the current user's active workspace.", "operationId": "read_users", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "skip", "in": "query", "required": false, "schema": {"type": "integer", "default": 0, "title": "<PERSON><PERSON>"}}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "default": 100, "title": "Limit"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UsersPublic"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "post": {"tags": ["users"], "summary": "Create User", "description": "Create new user.", "operationId": "create_user", "security": [{"OAuth2PasswordBearer": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserCreate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserPublic"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/users/me": {"get": {"tags": ["users"], "summary": "Read User Me", "description": "Get current user.", "operationId": "read_user_me", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserDetail"}}}}}, "security": [{"OAuth2PasswordBearer": []}]}, "delete": {"tags": ["users"], "summary": "Delete User Me", "description": "Delete own user.", "operationId": "delete_user_me", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Message"}}}}}, "security": [{"OAuth2PasswordBearer": []}]}, "patch": {"tags": ["users"], "summary": "Update User Me", "description": "Update own user.", "operationId": "update_user_me", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserUpdateMe"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserPublic"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"OAuth2PasswordBearer": []}]}}, "/api/v1/users/me/password": {"patch": {"tags": ["users"], "summary": "Update Password Me", "description": "Update own password.", "operationId": "update_password_me", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdatePassword"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Message"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"OAuth2PasswordBearer": []}]}}, "/api/v1/users/{user_id}": {"get": {"tags": ["users"], "summary": "Read User By Id", "description": "Get a specific user by id.", "operationId": "read_user_by_id", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "user_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "User Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserPublic"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "patch": {"tags": ["users"], "summary": "Update User", "description": "Update a user.", "operationId": "update_user", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "user_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "User Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserUpdate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserPublic"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["users"], "summary": "Delete User", "description": "Delete a user.", "operationId": "delete_user", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "user_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "User Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Message"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/users/switch-workspace/{workspace_id}": {"get": {"tags": ["users"], "summary": "Switch Workspace", "description": "Allow user to get new token for a different workspace.", "operationId": "switch_workspace", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "workspace_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Workspace Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Token"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/utils/test-email/": {"post": {"tags": ["utils"], "summary": "Test Email", "description": "Test emails.", "operationId": "test_email", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "email_to", "in": "query", "required": true, "schema": {"type": "string", "format": "email", "title": "Email To"}}], "responses": {"201": {"description": "Successful Response", "content": {"application/json": {"schema": {"title": "Response Utils-Test Email"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/utils/health-check/": {"get": {"tags": ["utils"], "summary": "Health Check", "operationId": "health_check", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "boolean", "title": "Response Utils-Health Check"}}}}}}}, "/api/v1/utils/publish/": {"post": {"tags": ["utils"], "summary": "Publish Message", "operationId": "publish_message", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Message"}}}, "required": true}, "responses": {"201": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"OAuth2PasswordBearer": []}]}}, "/api/v1/utils/enqueue/": {"post": {"tags": ["utils"], "summary": "Enqueue Message", "operationId": "enqueue_message", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "task_name", "in": "query", "required": true, "schema": {"type": "string", "title": "Task Name"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Message"}}}}, "responses": {"201": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/items/": {"get": {"tags": ["items"], "summary": "Read Items", "description": "Retrieve items.", "operationId": "read_items", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "skip", "in": "query", "required": false, "schema": {"type": "integer", "default": 0, "title": "<PERSON><PERSON>"}}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "default": 100, "title": "Limit"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ItemsPublic"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "post": {"tags": ["items"], "summary": "Create <PERSON><PERSON>", "description": "Create new item.", "operationId": "create_item", "security": [{"OAuth2PasswordBearer": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ItemCreate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ItemPublic"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/items/{id}": {"get": {"tags": ["items"], "summary": "Read Item", "description": "Get item by ID.", "operationId": "read_item", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ItemPublic"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "put": {"tags": ["items"], "summary": "Update Item", "description": "Update an item.", "operationId": "update_item", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ItemUpdate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ItemPublic"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["items"], "summary": "Delete Item", "description": "Delete an item.", "operationId": "delete_item", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Message"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/aws-accounts/": {"get": {"tags": ["aws-accounts"], "summary": "Read Aws Accounts", "description": "Retrieve AWS accounts.", "operationId": "read_aws_accounts", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "skip", "in": "query", "required": false, "schema": {"type": "integer", "default": 0, "title": "<PERSON><PERSON>"}}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "default": 100, "title": "Limit"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AWSAccountsPublic"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "post": {"tags": ["aws-accounts"], "summary": "Create Aws Account", "description": "Create new AWS account.", "operationId": "create_aws_account", "security": [{"OAuth2PasswordBearer": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AWSAccountCreate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AWSAccountPublic"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/aws-accounts/{id}": {"get": {"tags": ["aws-accounts"], "summary": "<PERSON> Account", "description": "Get AWS account by ID.", "operationId": "read_aws_account", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AWSAccountPublic"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "put": {"tags": ["aws-accounts"], "summary": "Update Aws Account", "description": "Update an AWS account.", "operationId": "update_aws_account", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AWSAccountUpdate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AWSAccountPublic"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["aws-accounts"], "summary": "Delete Aws Account", "description": "Delete an AWS account.", "operationId": "delete_aws_account", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Message"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/resources/": {"get": {"tags": ["resources"], "summary": "Read Resources", "description": "Retrieve resources.", "operationId": "read_resources", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "skip", "in": "query", "required": false, "schema": {"type": "integer", "default": 0, "title": "<PERSON><PERSON>"}}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "default": 100, "title": "Limit"}}, {"name": "name", "in": "query", "required": false, "schema": {"type": "string", "title": "Name"}}, {"name": "resource_type", "in": "query", "required": false, "schema": {"type": "array", "items": {"type": "string"}, "default": [], "title": "Resource Type"}}, {"name": "status", "in": "query", "required": false, "schema": {"type": "array", "items": {"type": "string"}, "default": [], "title": "Status"}}, {"name": "region", "in": "query", "required": false, "schema": {"type": "array", "items": {"type": "string"}, "default": [], "title": "Region"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResourcesPublic"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "post": {"tags": ["resources"], "summary": "Create Resource", "description": "Create new resource.", "operationId": "create_resource", "security": [{"OAuth2PasswordBearer": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResourceCreate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResourcePublic"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/resources/{id}": {"get": {"tags": ["resources"], "summary": "Read Resource", "description": "Get resource by ID.", "operationId": "read_resource", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResourceRead"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "put": {"tags": ["resources"], "summary": "Update Resource", "description": "Update a resource.", "operationId": "update_resource", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResourceUpdate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResourcePublic"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["resources"], "summary": "Delete Resource", "description": "Delete a resource.", "operationId": "delete_resource", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Message"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/metrics/": {"get": {"tags": ["metrics"], "summary": "Read Metrics", "description": "Retrieve metrics.", "operationId": "read_metrics", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "skip", "in": "query", "required": false, "schema": {"type": "integer", "default": 0, "title": "<PERSON><PERSON>"}}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "default": 100, "title": "Limit"}}, {"name": "resource_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Resource Id"}}, {"name": "start_date", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Start Date"}}, {"name": "end_date", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Date"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MetricsPublic"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "post": {"tags": ["metrics"], "summary": "Create Metric", "description": "Create new metric.", "operationId": "create_metric", "security": [{"OAuth2PasswordBearer": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MetricCreate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MetricPublic"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/metrics/{id}": {"get": {"tags": ["metrics"], "summary": "Read Metric", "description": "Get metric by ID.", "operationId": "read_metric", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MetricPublic"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "put": {"tags": ["metrics"], "summary": "Update Metric", "description": "Update a metric.", "operationId": "update_metric", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MetricUpdate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MetricPublic"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["metrics"], "summary": "Delete Metric", "description": "Delete a metric.", "operationId": "delete_metric", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Message"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/recommendations/overal": {"get": {"tags": ["recommendations"], "summary": "Get Recomendation Overal", "description": "Get overal recommendation statistics.", "operationId": "get_recomendation_overal", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RecommendationOveralPublic"}}}}}, "security": [{"OAuth2PasswordBearer": []}]}}, "/api/v1/recommendations/": {"get": {"tags": ["recommendations"], "summary": "Read Recommendations", "description": "Retrieve recommendations.", "operationId": "read_recommendations", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "skip", "in": "query", "required": false, "schema": {"type": "integer", "default": 0, "title": "<PERSON><PERSON>"}}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "default": 100, "title": "Limit"}}, {"name": "search", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Search"}}, {"name": "resource_id", "in": "query", "required": false, "schema": {"type": "array", "items": {"type": "string"}, "default": [], "title": "Resource Id"}}, {"name": "resource_type", "in": "query", "required": false, "schema": {"type": "array", "items": {"type": "string"}, "default": [], "title": "Resource Type"}}, {"name": "recommendation_type", "in": "query", "required": false, "schema": {"type": "array", "items": {"type": "string"}, "default": [], "title": "Recommendation Type"}}, {"name": "status", "in": "query", "required": false, "schema": {"type": "array", "items": {"type": "string"}, "default": [], "title": "Status"}}, {"name": "start_date", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Start Date"}}, {"name": "end_date", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Date"}}, {"name": "order_by", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Order By"}}, {"name": "order_direction", "in": "query", "required": false, "schema": {"type": "string", "default": "desc", "title": "Order Direction"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RecommendationsPublic"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "post": {"tags": ["recommendations"], "summary": "Create Recommendation", "description": "Create new recommendation.", "operationId": "create_recommendation", "security": [{"OAuth2PasswordBearer": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RecommendationCreate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RecommendationPublic"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/recommendations/{id}": {"get": {"tags": ["recommendations"], "summary": "Read Recommendation", "description": "Get recommendation by ID.", "operationId": "read_recommendation", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RecommendationPublic"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "put": {"tags": ["recommendations"], "summary": "Update Recommendation", "description": "Update a recommendation.", "operationId": "update_recommendation", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RecommendationUpdate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RecommendationPublic"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["recommendations"], "summary": "Delete Recommendation", "description": "Delete a recommendation.", "operationId": "delete_recommendation", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Message"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/recommendations/{id}/status": {"put": {"tags": ["recommendations"], "summary": "Update Recommendation Status", "description": "Update the status of a recommendation.", "operationId": "update_recommendation_status", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Id"}}, {"name": "status", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/RecommendationStatus"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RecommendationPublic"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/workflows/": {"get": {"tags": ["workflows"], "summary": "Read Workflows", "description": "Retrieve workflows.", "operationId": "read_workflows", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "skip", "in": "query", "required": false, "schema": {"type": "integer", "default": 0, "title": "<PERSON><PERSON>"}}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "default": 100, "title": "Limit"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WorkflowsPublic"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "post": {"tags": ["workflows"], "summary": "Create Workflow", "description": "Create new workflow.", "operationId": "create_workflow", "security": [{"OAuth2PasswordBearer": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WorkflowCreate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WorkflowPublic"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/workflows/{id}": {"get": {"tags": ["workflows"], "summary": "Read Workflow", "description": "Get workflow by ID.", "operationId": "read_workflow", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WorkflowPublic"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "put": {"tags": ["workflows"], "summary": "Update Workflow", "description": "Update a workflow.", "operationId": "update_workflow", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WorkflowUpdate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WorkflowPublic"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["workflows"], "summary": "Delete Workflow", "description": "Delete a workflow.", "operationId": "delete_workflow", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Message"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/workflows/template": {"post": {"tags": ["workflows"], "summary": "Create Workflow From Template", "description": "Create new workflow from YAML template.", "operationId": "create_workflow_from_template", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "workspace_id", "in": "query", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Workspace Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WorkflowPublic"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/workflows/{workflow_id}/nodes/": {"post": {"tags": ["workflows"], "summary": "Create Workflow Node", "description": "Create new workflow node.", "operationId": "create_workflow_node", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "workflow_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Workflow Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WorkflowNodeCreate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WorkflowNodePublic"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "get": {"tags": ["workflows"], "summary": "Read Workflow Nodes", "description": "Retrieve workflow nodes.", "operationId": "read_workflow_nodes", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "workflow_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Workflow Id"}}, {"name": "skip", "in": "query", "required": false, "schema": {"type": "integer", "default": 0, "title": "<PERSON><PERSON>"}}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "default": 100, "title": "Limit"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/WorkflowNodePublic"}, "title": "Response Workflows-Read Workflow Nodes"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/workflows/{workflow_id}/nodes/{node_id}": {"get": {"tags": ["workflows"], "summary": "Read Workflow Node", "description": "Get workflow node by ID.", "operationId": "read_workflow_node", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "workflow_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Workflow Id"}}, {"name": "node_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Node Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WorkflowNodePublic"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "put": {"tags": ["workflows"], "summary": "Update Workflow Node", "description": "Update a workflow node.", "operationId": "update_workflow_node", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "workflow_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Workflow Id"}}, {"name": "node_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Node Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WorkflowNodeUpdate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WorkflowNodePublic"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["workflows"], "summary": "Delete Workflow Node", "description": "Delete a workflow node.", "operationId": "delete_workflow_node", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "workflow_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Workflow Id"}}, {"name": "node_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Node Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Message"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/workflows/{workflow_id}/nodes/{node_id}/run": {"post": {"tags": ["workflows"], "summary": "Run Workflow Node", "description": "Run a specific workflow node.", "operationId": "run_workflow_node", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "workflow_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Workflow Id"}}, {"name": "node_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Node Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Message"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/workflows/{workflow_id}/run": {"post": {"tags": ["workflows"], "summary": "Run Workflow", "description": "Run an entire workflow.", "operationId": "run_workflow", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "workflow_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Workflow Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Message"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/workspaces/": {"get": {"tags": ["workspaces"], "summary": "Read Workspaces", "description": "Retrieve workspaces - both owned and invited (non-deleted only).", "operationId": "read_workspaces", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "skip", "in": "query", "required": false, "schema": {"type": "integer", "default": 0, "title": "<PERSON><PERSON>"}}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "default": 100, "title": "Limit"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WorkspacesPublic"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "post": {"tags": ["workspaces"], "summary": "Create Workspace", "description": "Create new workspace. Only users who already own workspaces or superusers can create new ones.", "operationId": "create_workspace", "security": [{"OAuth2PasswordBearer": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WorkspaceCreate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WorkspacePublic"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/workspaces/{id}": {"get": {"tags": ["workspaces"], "summary": "Read Workspace", "description": "Get Workspace by ID. Accessible by workspace owner and invited users.", "operationId": "read_workspace", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WorkspaceDetail"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "put": {"tags": ["workspaces"], "summary": "Update Workspace", "description": "Update a workspace. Only workspace owners can perform this action.", "operationId": "update_workspace", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WorkspaceUpdate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WorkspacePublic"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["workspaces"], "summary": "Delete Workspace", "description": "Delete a workspace. Only workspace owners can perform this action.", "operationId": "delete_workspace", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Message"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/tools/run": {"post": {"tags": ["tools"], "summary": "Script Execution", "description": "Script execution to the bash environment.\nThis method is mainly purpose to execute aws-cli tools to interacting with aws resources.\nExample script: aws s3 ls --output table --region ap-southeast-1", "operationId": "script_execution", "security": [{"APIKeyHeader": []}], "parameters": [{"name": "script", "in": "query", "required": true, "schema": {"type": "string", "title": "<PERSON><PERSON><PERSON>"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ScriptExecutionResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/agents/": {"get": {"tags": ["agents"], "summary": "Read Agents", "description": "Retrieve Agents for the current workspace.", "operationId": "read_agents", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "skip", "in": "query", "required": false, "schema": {"type": "integer", "default": 0, "title": "<PERSON><PERSON>"}}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "default": 100, "title": "Limit"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AgentsPublic"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "post": {"tags": ["agents"], "summary": "Create Agent", "description": "Create new Agent.", "operationId": "create_agent", "security": [{"OAuth2PasswordBearer": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AgentCreate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AgentPublic"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/agents/{id}": {"get": {"tags": ["agents"], "summary": "Read Agent", "description": "Get Agent by ID.", "operationId": "read_agent", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AgentPublic"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "put": {"tags": ["agents"], "summary": "Update Agent", "operationId": "update_agent", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AgentUpdate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AgentPublic"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["agents"], "summary": "Delete Agent", "operationId": "delete_agent", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Message"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/agents/init-default/{workspace_id}": {"post": {"tags": ["agents"], "summary": "Init Default Agents", "description": "Initialize default agents for a workspace.", "operationId": "init_default_agents", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "workspace_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Workspace Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Message"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/tasks/": {"post": {"tags": ["tasks"], "summary": "Create Task", "description": "Create new task.", "operationId": "create_task", "security": [{"OAuth2PasswordBearer": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TaskCreate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TaskResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "get": {"tags": ["tasks"], "summary": "List Tasks", "description": "List tasks with filters.", "operationId": "list_tasks", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "execution_status", "in": "query", "required": false, "schema": {"anyOf": [{"$ref": "#/components/schemas/TaskExecutionStatus"}, {"type": "null"}], "title": "Execution Status"}}, {"name": "skip", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 0, "default": 0, "title": "<PERSON><PERSON>"}}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 500, "minimum": 1, "default": 100, "title": "Limit"}}, {"name": "include_history", "in": "query", "required": false, "schema": {"type": "boolean", "default": false, "title": "Include History"}}, {"name": "history_limit", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 100, "minimum": 1, "default": 10, "title": "History Limit"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TaskList"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/tasks/{task_id}": {"get": {"tags": ["tasks"], "summary": "Get Task", "description": "Get task by ID.", "operationId": "get_task", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "task_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Task Id"}}, {"name": "include_history", "in": "query", "required": false, "schema": {"type": "boolean", "default": false, "title": "Include History"}}, {"name": "history_limit", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 100, "minimum": 1, "default": 10, "title": "History Limit"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TaskResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "put": {"tags": ["tasks"], "summary": "Update Task", "description": "Update task.", "operationId": "update_task", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "task_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Task Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TaskUpdate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TaskResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["tasks"], "summary": "Delete Task", "description": "Delete task.", "operationId": "delete_task", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "task_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Task Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TaskDeleteResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/tasks/{task_id}/enable": {"patch": {"tags": ["tasks"], "summary": "Update Task Enable", "description": "Update task enable.", "operationId": "update_task_enable", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "task_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Task Id"}}, {"name": "enable", "in": "query", "required": false, "schema": {"type": "boolean", "default": true, "title": "Enable"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TaskResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/tasks/{task_id}/stop": {"post": {"tags": ["tasks"], "summary": "Stop Task Execution", "description": "Stop an executing task.", "operationId": "stop_task_execution", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "task_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Task Id"}}, {"name": "conversation_id", "in": "query", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Conversation Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TaskStopResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/tasks/{task_id}/progress": {"get": {"tags": ["tasks"], "summary": "Get Task Progress", "description": "Get task execution progress by conversation id.", "operationId": "get_task_progress", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "task_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Task Id"}}, {"name": "conversation_id", "in": "query", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Conversation Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TaskExecutionStatus"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/autonomous-agents/conversations": {"post": {"tags": ["autonomous-agents"], "summary": "Create Conversation", "description": "Create a new conversation.", "operationId": "create_conversation", "security": [{"OAuth2PasswordBearer": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ConversationCreateRequest"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ConversationPublic"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "get": {"tags": ["autonomous-agents"], "summary": "Get Conversations", "description": "Get list of conversations with filtering and pagination.\n\nArgs:\n    current_user: The authenticated user\n    session: Database session\n    agent_id: Optional agent ID to filter by\n    resource_id: Optional resource ID to filter by\n    model_provider: Model provider to use (defaults to 'bedrock')\n    skip: Number of records to skip for pagination\n    limit: Maximum number of records to return (1-100)\n\nReturns:\n    ConversationsPublic: Paginated list of conversations\n\nRaises:\n    HTTPException: If conversation not found or other error occurs", "operationId": "get_conversations", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "agent_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string", "format": "uuid4"}, {"type": "null"}], "title": "Agent Id"}}, {"name": "resource_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string", "format": "uuid4"}, {"type": "null"}], "title": "Resource Id"}}, {"name": "model_provider", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": "bedrock", "title": "Model Provider"}}, {"name": "skip", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 0, "description": "Number of records to skip for pagination", "default": 0, "title": "<PERSON><PERSON>"}, "description": "Number of records to skip for pagination"}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 100, "minimum": 1, "description": "Maximum number of records to return", "default": 20, "title": "Limit"}, "description": "Maximum number of records to return"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ConversationsPublic"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/autonomous-agents/messages/{conversation_id}": {"get": {"tags": ["autonomous-agents"], "summary": "Get Messages History", "description": "Get message history for a conversation.", "operationId": "get_messages_history", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "conversation_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Conversation Id"}}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "default": 200, "title": "Limit"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MessageHistoryPublic"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/autonomous-agents/chat/{conversation_id}/stream": {"post": {"tags": ["autonomous-agents"], "summary": "Chat Stream", "description": "Stream chat responses from the agent.\n\nArgs:\n    conversation_id: ID of the conversation\n    message: User message with content and resume flag\n    session: Database session\n\nReturns:\n    StreamingResponse: Server-sent events stream of agent responses\n\nRaises:\n    HTTPException: If conversation not found (404) or no previous message found (404)", "operationId": "chat_stream", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "conversation_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Conversation Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MessagePublic"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/StreamResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/autonomous-agents/conversations/{conversation_id}/name": {"put": {"tags": ["autonomous-agents"], "summary": "Rename Conversation", "operationId": "rename_conversation", "parameters": [{"name": "conversation_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Conversation Id"}}, {"name": "name", "in": "query", "required": true, "schema": {"type": "string", "title": "Name"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"title": "Response Autonomous-Agents-Rename Conversation"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/autonomous-agents/conversations/{conversation_id}": {"delete": {"tags": ["autonomous-agents"], "summary": "Delete Conversation", "description": "Delete a conversation and its associated LangGraph thread data.", "operationId": "delete_conversation", "parameters": [{"name": "conversation_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Conversation Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"title": "Response Autonomous-Agents-Delete Conversation"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/memory/": {"post": {"tags": ["memory"], "summary": "Get Memory", "description": "Get all memories for given agent roles. If agent roles are not provided, get all memories for all agent roles.", "operationId": "get_memory", "security": [{"OAuth2PasswordBearer": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MemoryFilter"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MemorysRead"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["memory"], "summary": "Delete Memory", "operationId": "delete_memory", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "id", "in": "query", "required": true, "schema": {"type": "string", "title": "Id"}}, {"name": "agent_role", "in": "query", "required": true, "schema": {"type": "string", "title": "Agent Role"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "put": {"tags": ["memory"], "summary": "Update Memory", "operationId": "update_memory", "security": [{"OAuth2PasswordBearer": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MemoryUpdate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/sample-data/resources/{resource_type}": {"post": {"tags": ["sample-data"], "summary": "Create Sample Resources", "description": "Generate sample resources and metrics for testing.\n\nArgs:\n    resource_type: Type of resource (EC2, RDS, etc.)\n    resource_count: Number of resources to generate\n    metrics_per_resource: Number of metric points per resource\n    days_back: Number of days to generate metrics for", "operationId": "create_sample_resources", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "resource_type", "in": "path", "required": true, "schema": {"$ref": "#/components/schemas/ResourceType"}}, {"name": "resource_count", "in": "query", "required": false, "schema": {"type": "integer", "default": 5, "title": "Resource Count"}}, {"name": "metrics_per_resource", "in": "query", "required": false, "schema": {"type": "integer", "default": 100, "title": "Metrics Per Resource"}}, {"name": "days_back", "in": "query", "required": false, "schema": {"type": "integer", "default": 30, "title": "Days Back"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"title": "Response Sample-Data-Create Sample Resources"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/sample-data/metrics/{resource_type}": {"post": {"tags": ["sample-data"], "summary": "Create Sample Metrics", "description": "Create sample metrics for specified resource type.\n\nParameters:\n- resource_type: Type of resource (EC2, RDS, etc.)\n- num_points: Number of data points to generate per metric\n- days_back: Number of days to generate data for", "operationId": "create_sample_metrics", "parameters": [{"name": "resource_type", "in": "path", "required": true, "schema": {"$ref": "#/components/schemas/ResourceType"}}, {"name": "num_points", "in": "query", "required": false, "schema": {"type": "integer", "default": 100, "title": "Num Points"}}, {"name": "days_back", "in": "query", "required": false, "schema": {"type": "integer", "default": 30, "title": "Days Back"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"title": "Response Sample-Data-Create Sample Metrics"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/sample-data/recommendations": {"post": {"tags": ["sample-data"], "summary": "Create <PERSON>ple Recommendations", "operationId": "create_sample_recommendations", "parameters": [{"name": "total_record", "in": "query", "required": false, "schema": {"type": "integer", "exclusiveMaximum": 1000, "exclusiveMinimum": 0, "default": 10, "title": "Total Record"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RecommendationsPublic"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/google/login": {"get": {"tags": ["google"], "summary": "Google Login", "description": "Initiate Google OAuth login flow", "operationId": "google_login", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}, "/api/v1/google/callback": {"get": {"tags": ["google"], "summary": "Google Callback", "description": "Handle Google OAuth callback and login/create user", "operationId": "google_callback", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Token"}}}}}}}, "/api/v1/quotas/usage": {"post": {"tags": ["quotas"], "summary": "Create Usage", "operationId": "create_usage", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TokenUsageCreate"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TokenUsageResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"OAuth2PasswordBearer": []}]}}, "/api/v1/quotas/{user_id}": {"post": {"tags": ["quotas"], "summary": "Create <PERSON><PERSON>", "operationId": "create_usage_quota", "parameters": [{"name": "user_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "User Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UsageQuotaResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "get": {"tags": ["quotas"], "summary": "Get Usage Quota", "description": "Get usage quota for a specific workspace.\n\nArgs:\n    user_id: ID of the user\n\nReturns:\n    Usage quota details", "operationId": "get_usage_quota", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "user_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "User Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UsageQuotaResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/quotas/{user_id}/reset": {"post": {"tags": ["quotas"], "summary": "Reset User Quota", "description": "Reset usage quota for a user.\n\nArgs:\n    user_id: ID of the user\n\nReturns:\n    Reset usage quota details", "operationId": "reset_user_quota", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "user_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "User Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UsageQuotaResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/quotas/{user_id}/statistics": {"get": {"tags": ["quotas"], "summary": "Get Usage Statistics", "description": "Get token usage statistics for a workspace.\n\nArgs:\n    user_id: ID of the user\n    start_date: Optional start date for filtering\n    end_date: Optional end date for filtering\n\nReturns:\n    Usage statistics", "operationId": "get_usage_statistics", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "user_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "User Id"}}, {"name": "start_date", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Start Date"}}, {"name": "end_date", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Date"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UsageStatistics"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/quotas/{workspace_id}/message-statistics": {"get": {"tags": ["quotas"], "summary": "Get Messages Statistics", "description": "Get message usage statistics for a workspace.\n\nArgs:\n    workspace_id: ID of the workspace\n    start_date: Optional start date for filtering\n    end_date: Optional end date for filtering\n\nReturns:\n    Message statistics including:\n    - Total messages and month-over-month change\n    - Average response time and month-over-month change\n    - Success rate and month-over-month change\n    - Average tokens per message (input/output)\n    - Daily message volume (30-day trend)\n    - Token distribution by message length", "operationId": "get_messages_statistics", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "workspace_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Workspace Id"}}, {"name": "start_date", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Start Date"}}, {"name": "end_date", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Date"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MessageStatistics"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/quotas/{user_id}/quota-info": {"get": {"tags": ["quotas"], "summary": "Get Quota Info", "description": "Get quota information for a user.\n\nArgs:\n    user_id: ID of the user", "operationId": "get_quota_info", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "user_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "User Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/QuotaInfo"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/reports/saving-summary": {"get": {"tags": ["reports"], "summary": "Get Savings Summary", "description": "Get total potential savings for the workspace with comparison between two periods.", "operationId": "get_savings_summary", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "start_date", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Start Date"}}, {"name": "end_date", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Date"}}, {"name": "previous_start_date", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Previous Start Date"}}, {"name": "previous_end_date", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Previous End Date"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SavingSummaryReport"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/reports/resource-saving": {"get": {"tags": ["reports"], "summary": "Get Savings By Resource", "description": "Get savings data grouped by date for RDS and EC2 resources.", "operationId": "get_savings_by_resource", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "start_date", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Start Date"}}, {"name": "end_date", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Date"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResourceSavingsReport"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/reports/top-potential-savings": {"get": {"tags": ["reports"], "summary": "Get Top Potential Savings", "description": "Get top N recommendations with highest potential savings that are in PENDING status\nfor the current workspace within the specified date range.", "operationId": "get_top_potential_savings", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 100, "minimum": 1, "default": 5, "title": "Limit"}}, {"name": "start_date", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "description": "Start date in ISO format", "title": "Start Date"}, "description": "Start date in ISO format"}, {"name": "end_date", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "description": "End date in ISO format", "title": "End Date"}, "description": "End date in ISO format"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TopSavingsReport"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/reports/service-savings": {"get": {"tags": ["reports"], "summary": "Get Savings By Service", "description": "Get savings data grouped by service type for pie chart visualization.", "operationId": "get_savings_by_service", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "start_date", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Start Date"}}, {"name": "end_date", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Date"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ServiceSavingsReport"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/task_templates/generate": {"post": {"tags": ["task_templates"], "summary": "Generate", "description": "Generate the task template based on user's input", "operationId": "generate", "parameters": [{"name": "input", "in": "query", "required": true, "schema": {"type": "string", "title": "Input"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TaskTemplateResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/task_templates/": {"post": {"tags": ["task_templates"], "summary": "Create Template", "description": "Create new task template.", "operationId": "create_template", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "is_default", "in": "query", "required": false, "schema": {"type": "boolean", "default": false, "title": "<PERSON>"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TaskTemplateCreate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TaskTemplateResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "get": {"tags": ["task_templates"], "summary": "List Templates", "description": "List task templates with optional category and service filters.", "operationId": "list_templates", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "category", "in": "query", "required": false, "schema": {"anyOf": [{"$ref": "#/components/schemas/TaskCategoryEnum"}, {"type": "null"}], "title": "Category"}}, {"name": "services", "in": "query", "required": false, "schema": {"anyOf": [{"type": "array", "items": {"$ref": "#/components/schemas/TaskServiceEnum"}}, {"type": "null"}], "title": "Services"}}, {"name": "include_defaults", "in": "query", "required": false, "schema": {"type": "boolean", "default": true, "title": "Include De<PERSON>ults"}}, {"name": "skip", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 0, "default": 0, "title": "<PERSON><PERSON>"}}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 500, "minimum": 1, "default": 100, "title": "Limit"}}, {"name": "search_query", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Search Query"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TaskTemplateList"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/task_templates/{template_id}": {"get": {"tags": ["task_templates"], "summary": "Get Template", "description": "Get template by ID.", "operationId": "get_template", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "template_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Template Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TaskTemplateResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "put": {"tags": ["task_templates"], "summary": "Update Template", "description": "Update template.", "operationId": "update_template", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "template_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Template Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TaskTemplateUpdate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TaskTemplateResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["task_templates"], "summary": "Delete Template", "description": "Delete template.", "operationId": "delete_template", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "template_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Template Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"title": "Response Task Templates-Delete Template"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/knowledge_base_runtime/search": {"post": {"tags": ["knowledge_base_runtime"], "summary": "Search knowledge base", "description": "Search the knowledge base with various modes and filters", "operationId": "search", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "query", "in": "query", "required": true, "schema": {"type": "string", "title": "Query"}}, {"name": "kb_id", "in": "query", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Kb Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RetrieverConfig"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SearchResponse"}}}}, "400": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}, "description": "Bad Request"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}, "500": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}, "description": "Internal Server Error"}}}}, "/api/v1/knowledge_base_runtime/summarize": {"post": {"tags": ["knowledge_base_runtime"], "summary": "Generate summary from knowledge base", "description": "Search and summarize content from knowledge base", "operationId": "summarize", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "query", "in": "query", "required": true, "schema": {"type": "string", "title": "Query"}}, {"name": "kb_id", "in": "query", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Kb Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RetrieverConfig"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SummaryResponse"}}}}, "400": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}, "description": "Bad Request"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}, "500": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}, "description": "Internal Server Error"}}}}, "/api/v1/files/create-url": {"post": {"tags": ["files"], "summary": "Create Upload Url", "operationId": "create_upload_url", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UploadCreate"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UploadResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"OAuth2PasswordBearer": []}]}}, "/api/v1/files/{id}": {"post": {"tags": ["files"], "summary": "Check Upload Status", "operationId": "check_upload_status", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UploadPublic"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/knowledge_base/kbs": {"post": {"tags": ["knowledge_base"], "summary": "Create Kb", "operationId": "create_kb", "security": [{"OAuth2PasswordBearer": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/KBCreate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/KBRead"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "get": {"tags": ["knowledge_base"], "summary": "Get Kbs", "description": "Get all knowledge bases for the current user", "operationId": "get_kbs", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "skip", "in": "query", "required": false, "schema": {"type": "integer", "default": 0, "title": "<PERSON><PERSON>"}}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "default": 50, "title": "Limit"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/KBsRead"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/knowledge_base/kbs/available-users": {"get": {"tags": ["knowledge_base"], "summary": "Get Available Users", "description": "Get a list of users available for sharing knowledge bases within the current workspace.\nReturns users that are in the same workspace as the current user.", "operationId": "get_available_users", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AvailableUsersCurrentWorkspace"}}}}}, "security": [{"OAuth2PasswordBearer": []}]}}, "/api/v1/knowledge_base/kbs/point-usage": {"get": {"tags": ["knowledge_base"], "summary": "Get Point Usage", "description": "Get user's point usage across all knowledge bases", "operationId": "get_point_usage", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "title": "Response Knowledge Base-Get Point Usage"}}}}}, "security": [{"OAuth2PasswordBearer": []}]}}, "/api/v1/knowledge_base/kbs/{kb_id}": {"get": {"tags": ["knowledge_base"], "summary": "Get Kb By Id", "description": "Get a specific knowledge base by ID", "operationId": "get_kb_by_id", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "kb_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Kb Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/KBRead"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "put": {"tags": ["knowledge_base"], "summary": "Update Kb", "operationId": "update_kb", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "kb_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Kb Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/KBUpdate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/KBRead"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["knowledge_base"], "summary": "Delete Kb", "operationId": "delete_kb", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "kb_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Kb Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "title": "Response Knowledge Base-Delete Kb"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/knowledge_base/kbs/{kb_id}/presigned-urls": {"post": {"tags": ["knowledge_base"], "summary": "Generate Presigned Urls", "description": "Generate presigned URLs for file uploads.\n\nThis endpoint generates presigned URLs that clients can use to upload files\ndirectly to S3, bypassing the backend for better performance and scalability.", "operationId": "generate_presigned_urls", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "kb_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Kb Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PresignedUrlRequest"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PresignedUrlResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/knowledge_base/kbs/{kb_id}/confirm-uploads": {"post": {"tags": ["knowledge_base"], "summary": "Confirm File Uploads", "description": "Confirm file uploads and start ingestion process.\n\nThis endpoint should be called after files have been successfully uploaded\nusing the presigned URLs to start the document ingestion process.", "operationId": "confirm_file_uploads", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "kb_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Kb Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ConfirmUploadsRequest"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TaskStatusResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/knowledge_base/kbs/{kb_id}/documents": {"post": {"tags": ["knowledge_base"], "summary": "Upload Urls", "operationId": "upload_urls", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "kb_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Kb Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/URLsUploadRequest"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TaskStatusResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "get": {"tags": ["knowledge_base"], "summary": "List Documents", "description": "List documents in a knowledge base.\n\nUser must have access to the knowledge base (owner for personal knowledge bases,\nworkspace member for workspace knowledge bases).", "operationId": "list_documents", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "kb_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Kb Id"}}, {"name": "skip", "in": "query", "required": false, "schema": {"type": "integer", "default": 0, "title": "<PERSON><PERSON>"}}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "default": 50, "title": "Limit"}}, {"name": "search", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Search"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DocumentsKBRead"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/knowledge_base/kbs/{kb_id}/documents/content": {"get": {"tags": ["knowledge_base"], "summary": "Get Document Content", "operationId": "get_document_content", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "kb_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Kb Id"}}, {"name": "object_name", "in": "query", "required": true, "schema": {"type": "string", "title": "Object Name"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "string", "title": "Response Knowledge Base-Get Document Content"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/knowledge_base/kbs/{kb_id}/documents/{document_id}": {"delete": {"tags": ["knowledge_base"], "summary": "Delete Document", "operationId": "delete_document", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "kb_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Kb Id"}}, {"name": "document_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Document Id"}}, {"name": "object_name", "in": "query", "required": true, "schema": {"type": "string", "title": "Object Name"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "title": "Response Knowledge Base-Delete Document"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/knowledge_base/tasks/{task_id}": {"get": {"tags": ["knowledge_base"], "summary": "Get Task Status", "description": "Get the status of an asynchronous task.\n\nThis endpoint returns the current status and progress of a Celery task,\nsuch as document ingestion.", "operationId": "get_task_status", "parameters": [{"name": "task_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Task Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TaskStatusResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/connectors/": {"post": {"tags": ["connectors"], "summary": "Create Connector", "description": "Create new knowledge base.", "operationId": "create_connector", "security": [{"OAuth2PasswordBearer": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ConnectorCreate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ConnectorResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "get": {"tags": ["connectors"], "summary": "List Connectors", "description": "List knowledge bases.", "operationId": "list_connectors", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "skip", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 0, "default": 0, "title": "<PERSON><PERSON>"}}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 500, "minimum": 1, "default": 100, "title": "Limit"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ConnectorList"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/connectors/{id}": {"get": {"tags": ["connectors"], "summary": "Get Connector", "description": "Get knowledge base by ID.", "operationId": "get_connector", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ConnectorResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "put": {"tags": ["connectors"], "summary": "Update Connector", "description": "Update knowledge base.", "operationId": "update_connector", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ConnectorUpdate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ConnectorResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["connectors"], "summary": "Delete Connector", "description": "Delete an item.", "operationId": "delete_connector", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Message"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/subscriptions/plans": {"get": {"tags": ["subscriptions"], "summary": "Get Available Plans", "description": "Get available plans", "operationId": "get_available_plans", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/ProductResponse"}, "type": "array", "title": "Response Subscriptions-Get Available Plans"}}}}}, "security": [{"OAuth2PasswordBearer": []}]}}, "/api/v1/subscriptions/status": {"get": {"tags": ["subscriptions"], "summary": "Get User Subscription Status", "operationId": "get_user_subscription_status", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/SubscriptionStatus"}, "type": "array", "title": "Response Subscriptions-Get User Subscription Status"}}}}}, "security": [{"OAuth2PasswordBearer": []}]}}, "/api/v1/subscriptions/workspace/{workspace_id}/status": {"get": {"tags": ["subscriptions"], "summary": "Get Workspace Subscription Status", "description": "Get subscription status for a workspace", "operationId": "get_workspace_subscription_status", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "workspace_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Workspace Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/SubscriptionStatus"}, "title": "Response Subscriptions-Get Workspace Subscription Status"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/subscriptions/checkout": {"post": {"tags": ["subscriptions"], "summary": "Create Checkout Session", "description": "Create a checkout session for subscription", "operationId": "create_checkout_session", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "price_id", "in": "query", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Price Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CheckoutSessionResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/subscriptions/payment-methods": {"get": {"tags": ["subscriptions"], "summary": "Get User Payment Methods", "description": "Get current user's payment methods", "operationId": "get_user_payment_methods", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "payment_type", "in": "query", "required": false, "schema": {"type": "string", "default": "card", "title": "Payment Type"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/PaymentMethodResponse"}, "title": "Response Subscriptions-Get User Payment Methods"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/subscriptions/invoices": {"get": {"tags": ["subscriptions"], "summary": "Get User Invoices", "description": "Get current user's invoices", "operationId": "get_user_invoices", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "default": 10, "title": "Limit"}}, {"name": "status", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Status"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/InvoiceResponse"}, "title": "Response Subscriptions-Get User Invoices"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/subscriptions/enterprise-enquiry": {"post": {"tags": ["subscriptions"], "summary": "Submit Enterprise Enquiry", "description": "Submit an enterprise plan enquiry", "operationId": "submit_enterprise_enquiry", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/EnterpriseEnquiryRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/EnterpriseEnquiryMessageResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"OAuth2PasswordBearer": []}]}}, "/api/v1/subscriptions/plan-change": {"post": {"tags": ["subscriptions"], "summary": "Submit Plan Change Request", "description": "Submit a plan change request", "operationId": "submit_plan_change_request", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PlanChangeRequestCreate"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PlanChangeRequestResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"OAuth2PasswordBearer": []}]}}, "/api/v1/subscriptions/webhook": {"post": {"tags": ["subscriptions"], "summary": "Webhook", "description": "Handle webhook events from payment provider", "operationId": "webhook", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}, "/api/v1/subscriptions/cancel": {"post": {"tags": ["subscriptions"], "summary": "Cancel Subscription", "description": "Cancel subscription for the current user", "operationId": "cancel_subscription", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "title": "Response Subscriptions-Cancel Subscription"}}}}}, "security": [{"OAuth2PasswordBearer": []}]}}, "/api/v1/module_setting/": {"get": {"tags": ["module_setting"], "summary": "Get Module Settings", "description": "Retrieve all module settings.", "operationId": "get_module_settings", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/ModuleSetting"}, "type": "array", "title": "Response Module Setting-Get Module Settings"}}}}}, "security": [{"OAuth2PasswordBearer": []}]}}, "/api/v1/mcp-config/{workspace_id}": {"get": {"tags": ["mcp-config"], "summary": "Get Mcp Config", "description": "Get MCP configuration for a specific workspace.", "operationId": "get_mcp_config", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "workspace_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Workspace Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MCPConfigResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "post": {"tags": ["mcp-config"], "summary": "Create Mcp Config", "description": "Create a new MCP configuration for a workspace.", "operationId": "create_mcp_config", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "workspace_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Workspace Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MCPConfigCreate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MCPConfigResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "put": {"tags": ["mcp-config"], "summary": "Update Mcp Config", "description": "Update an existing MCP configuration.", "operationId": "update_mcp_config", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "workspace_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Workspace Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MCPConfigCreate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MCPConfigResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["mcp-config"], "summary": "Delete Mcp Config", "description": "Delete an MCP configuration.", "operationId": "delete_mcp_config", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "workspace_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Workspace Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Message"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/mcp-config/{workspace_id}/servers": {"get": {"tags": ["mcp-config"], "summary": "Get Mcp Servers", "description": "Get MCP server configurations with connection status.\n\nReturns information about all configured MCP servers for the workspace,\nincluding their connection status, available tools, and configuration details.", "operationId": "get_mcp_servers", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "workspace_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Workspace Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MCPServerListResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/mcp-config/{workspace_id}/servers/{server_name}/toggle": {"post": {"tags": ["mcp-config"], "summary": "Toggle Mcp Server Active State", "description": "Toggle the active state of an MCP server (activate/deactivate).\n\nThis endpoint allows activating or deactivating a specific MCP server without\nremoving it from the configuration. Inactive servers won't attempt connections.", "operationId": "toggle_mcp_server_active_state", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "workspace_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Workspace Id"}}, {"name": "server_name", "in": "path", "required": true, "schema": {"type": "string", "title": "Server Name"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MCPConfigResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/mcp-config/{workspace_id}/servers/{server_name}/refresh": {"post": {"tags": ["mcp-config"], "summary": "Refresh Mcp Server Status", "description": "Refresh the connection status of a specific MCP server.\n\nThis endpoint checks the current connection status of a specific server\nwithout changing its configuration. It's useful for getting real-time status\nof the server without refreshing all servers at once.", "operationId": "refresh_mcp_server_status", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "workspace_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Workspace Id"}}, {"name": "server_name", "in": "path", "required": true, "schema": {"type": "string", "title": "Server Name"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MCPServerInfo"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/mcp-config/{workspace_id}/servers/{server_name}/permissions/add": {"post": {"tags": ["mcp-config"], "summary": "Add Server Tool Permission", "description": "Add a tool permission to an MCP server.\n\nThis endpoint allows adding a new permission to the server's tools_permissions list.\nPermissions control what tools the server is allowed to use.", "operationId": "add_server_tool_permission", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "workspace_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Workspace Id"}}, {"name": "server_name", "in": "path", "required": true, "schema": {"type": "string", "title": "Server Name"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ToolPermissionRequest"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MCPConfigResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/mcp-config/{workspace_id}/servers/{server_name}/permissions/remove": {"post": {"tags": ["mcp-config"], "summary": "Remove Server Tool Permission", "description": "Remove a tool permission from an MCP server.\n\nThis endpoint allows removing a permission from the server's tools_permissions list.\nRemoving a permission may restrict what tools the server can use.", "operationId": "remove_server_tool_permission", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "workspace_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Workspace Id"}}, {"name": "server_name", "in": "path", "required": true, "schema": {"type": "string", "title": "Server Name"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ToolPermissionRequest"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MCPConfigResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/alerts/summary/status": {"get": {"tags": ["alerts"], "summary": "Get Alert Status Summary", "description": "Get a summary of alerts by status for the last 30 days.", "operationId": "get_alert_status_summary", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AlertStatusSummary"}}}}}, "security": [{"OAuth2PasswordBearer": []}]}}, "/api/v1/alerts/": {"post": {"tags": ["alerts"], "summary": "Create <PERSON><PERSON>", "description": "Create new alert.", "operationId": "create_alert", "security": [{"OAuth2PasswordBearer": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AlertCreate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AlertResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "get": {"tags": ["alerts"], "summary": "List Alerts", "description": "List alerts with optional filters.", "operationId": "list_alerts", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "severity", "in": "query", "required": false, "schema": {"anyOf": [{"$ref": "#/components/schemas/AlertSeverity"}, {"type": "null"}], "title": "Severity"}}, {"name": "status", "in": "query", "required": false, "schema": {"anyOf": [{"$ref": "#/components/schemas/AlertStatus"}, {"type": "null"}], "title": "Status"}}, {"name": "sort_by", "in": "query", "required": false, "schema": {"type": "string", "description": "Field to sort by", "default": "created_at", "title": "Sort By"}, "description": "Field to sort by"}, {"name": "sort_desc", "in": "query", "required": false, "schema": {"type": "boolean", "description": "Sort in descending order", "default": true, "title": "Sort Desc"}, "description": "Sort in descending order"}, {"name": "skip", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 0, "default": 0, "title": "<PERSON><PERSON>"}}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 500, "minimum": 1, "default": 100, "title": "Limit"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AlertList"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/alerts/{alert_id}": {"get": {"tags": ["alerts"], "summary": "<PERSON>ert", "description": "Get alert by ID.", "operationId": "get_alert", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "alert_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "<PERSON><PERSON>"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AlertResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "put": {"tags": ["alerts"], "summary": "Update Al<PERSON>", "description": "Update alert.", "operationId": "update_alert", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "alert_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "<PERSON><PERSON>"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AlertUpdate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AlertResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["alerts"], "summary": "Delete Alert", "description": "Delete alert.", "operationId": "delete_alert", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "alert_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "<PERSON><PERSON>"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"title": "Response Alerts-Delete <PERSON>ert"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/alerts/{alert_id}/status": {"patch": {"tags": ["alerts"], "summary": "Update Alert Status", "description": "Update alert status.", "operationId": "update_alert_status", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "alert_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "<PERSON><PERSON>"}}, {"name": "status", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/AlertStatus"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AlertResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/auth/signup": {"post": {"tags": ["auth"], "summary": "Register", "description": "Register new user and send activation email.", "operationId": "register", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserRegister"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ActivationResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/auth/activate/{token}": {"post": {"tags": ["auth"], "summary": "Activate Account", "description": "Activate a user account using the activation token.", "operationId": "activate_account", "parameters": [{"name": "token", "in": "path", "required": true, "schema": {"type": "string", "title": "Token"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ActivationResult"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/auth/resend-activation": {"post": {"tags": ["auth"], "summary": "Resend Activation", "description": "Resend activation email for unactivated accounts with reCAPTCHA v3 validation.", "operationId": "resend_activation", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResendActivationRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ActivationResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/notifications/": {"get": {"tags": ["notifications"], "summary": "List Notifications", "operationId": "list_notifications", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "requires_action", "in": "query", "required": false, "schema": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Requires Action"}}, {"name": "timeframe", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Timeframe"}}, {"name": "skip", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 0, "default": 0, "title": "<PERSON><PERSON>"}}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 100, "minimum": 0, "default": 50, "title": "Limit"}}], "requestBody": {"content": {"application/json": {"schema": {"anyOf": [{"type": "array", "items": {"$ref": "#/components/schemas/NotificationType"}}, {"type": "null"}], "title": "Type"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NotificationList"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/notifications/{notification_id}/mark-read": {"post": {"tags": ["notifications"], "summary": "Mark Notification Read", "operationId": "mark_notification_read", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "notification_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Notification Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NotificationResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/notifications/mark-all-read": {"post": {"tags": ["notifications"], "summary": "Mark All Notifications Read", "operationId": "mark_all_notifications_read", "requestBody": {"content": {"application/json": {"schema": {"anyOf": [{"items": {"$ref": "#/components/schemas/NotificationType"}, "type": "array"}, {"type": "null"}], "title": "Type"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"title": "Response Notifications-Mark All Notifications Read"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"OAuth2PasswordBearer": []}]}}, "/api/v1/message-feedback/message/{message_id}": {"get": {"tags": ["message-feedback"], "summary": "Get Message Feedback", "description": "Get feedback for a specific message.", "operationId": "get_message_feedback", "parameters": [{"name": "message_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Message Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"anyOf": [{"$ref": "#/components/schemas/MessageFeedbackPublic"}, {"type": "null"}], "title": "Response Message-Feedback-Get Message Feedback"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "put": {"tags": ["message-feedback"], "summary": "Update Message Feedback", "description": "Update feedback for a message.", "operationId": "update_message_feedback", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "message_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Message Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MessageFeedbackUpdate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MessageFeedbackPublic"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["message-feedback"], "summary": "Delete Message Feedback", "description": "Delete feedback for a message.", "operationId": "delete_message_feedback", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "message_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Message Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"title": "Response Message-Feedback-Delete Message Feedback"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/message-feedback/": {"post": {"tags": ["message-feedback"], "summary": "Create Message Feedback", "description": "Create feedback for a message.", "operationId": "create_message_feedback", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MessageFeedbackCreate"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MessageFeedbackPublic"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"OAuth2PasswordBearer": []}]}}, "/api/v1/builtin-connectors/{workspace_id}": {"get": {"tags": ["builtin-connectors"], "summary": "List Workspace Connectors", "description": "List all built-in connectors for a workspace", "operationId": "list_workspace_connectors", "parameters": [{"name": "workspace_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Workspace Id"}}, {"name": "skip", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 0, "default": 0, "title": "<PERSON><PERSON>"}}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 100, "minimum": 1, "default": 100, "title": "Limit"}}, {"name": "active_only", "in": "query", "required": false, "schema": {"type": "boolean", "default": false, "title": "Active Only"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ConnectorWithStatusResponse"}, "title": "Response Builtin-Connectors-List Workspace Connectors"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/builtin-connectors/{workspace_id}/{connector_id}/": {"post": {"tags": ["builtin-connectors"], "summary": "Update Connector For Workspace", "description": "Update the active status of a built-in connector for a workspace", "operationId": "update_connector_for_workspace", "parameters": [{"name": "workspace_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Workspace Id"}}, {"name": "connector_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Connector Id"}}, {"name": "is_active", "in": "query", "required": true, "schema": {"type": "boolean", "title": "Is Active"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "boolean", "title": "Response Builtin-Connectors-Update Connector For Workspace"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/builtin-connectors/{workspace_id}/{connector_id}/permission": {"put": {"tags": ["builtin-connectors"], "summary": "Update Connector Permission", "description": "Update whether a tool requires human approval before execution.\n\nArgs:\n    workspace_id: ID of the workspace\n    connector_id: ID of the connector\n    required_permission: Whether to require human approval for this tool\n    session: Database session\n\nReturns:\n    bool: True if the permission requirement was updated successfully", "operationId": "update_connector_permission", "parameters": [{"name": "workspace_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Workspace Id"}}, {"name": "connector_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Connector Id"}}, {"name": "required_permission", "in": "query", "required": true, "schema": {"type": "boolean", "title": "Required Permission"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "boolean", "title": "Response Builtin-Connectors-Update Connector Permission"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/agent-connectors": {"post": {"tags": ["agent-connectors"], "summary": "Create Agent Connector", "description": "Create a new agent connector.", "operationId": "create_agent_connector", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AgentConnectorCreate"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AgentConnectorResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/agent-connectors/{agent_id}": {"get": {"tags": ["agent-connectors"], "summary": "Get Agent Connector", "description": "Get an agent connector by agent ID.", "operationId": "get_agent_connector", "parameters": [{"name": "agent_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Agent Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AgentConnectorResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "put": {"tags": ["agent-connectors"], "summary": "Update Agent Connector", "description": "Update an existing agent connector.", "operationId": "update_agent_connector", "parameters": [{"name": "agent_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Agent Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AgentConnectorUpdate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AgentConnectorResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["agent-connectors"], "summary": "Delete Agent Connector", "description": "Delete an agent connector.", "operationId": "delete_agent_connector", "parameters": [{"name": "agent_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Agent Id"}}], "responses": {"204": {"description": "Successful Response"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/agent-connectors/workspace/{workspace_id}": {"get": {"tags": ["agent-connectors"], "summary": "Get Agent Connectors By Workspace Id", "description": "Get all agent connectors by workspace ID.", "operationId": "get_agent_connectors_by_workspace_id", "parameters": [{"name": "workspace_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Workspace Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/AgentConnectorResponse"}, "title": "Response Agent-Connectors-Get Agent Connectors By Workspace Id"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/agent-context/{agent_id}": {"put": {"tags": ["agent-context"], "summary": "Update Agent Context", "description": "Update agent context", "operationId": "update_agent_context", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "agent_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Agent Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AgentContextUpdate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AgentContextRead"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "get": {"tags": ["agent-context"], "summary": "Get Agent Context", "description": "Get context based on agent id", "operationId": "get_agent_context", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "agent_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Agent Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"anyOf": [{"$ref": "#/components/schemas/AgentContextRead"}, {"type": "null"}], "title": "Response Agent-Context-Get Agent Context"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/agent-context/": {"get": {"tags": ["agent-context"], "summary": "Get Agent Contexts", "description": "Get contexts based on a list of agent ids, for each agent id, get the latest none deleted context", "operationId": "get_agent_contexts", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AgentContextListInput"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AgentContextListResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"OAuth2PasswordBearer": []}]}}, "/api/v1/share-chat/conversations/{conversation_id}/share": {"post": {"tags": ["share-chat"], "summary": "Create Share Link", "description": "Create a share link for a conversation", "operationId": "create_share_link", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "conversation_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Conversation Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ShareResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["share-chat"], "summary": "Revoke Share Link", "description": "Revoke a share link for a conversation", "operationId": "revoke_share_link", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "conversation_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Conversation Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "get": {"tags": ["share-chat"], "summary": "Get Share Link", "description": "Get a share link for a conversation", "operationId": "get_share_link", "parameters": [{"name": "conversation_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Conversation Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ShareResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/share-chat/conversations/shared/{share_id}": {"get": {"tags": ["share-chat"], "summary": "Get Shared Conversation", "description": "Get message history for a shared conversation by share ID (no authentication required)", "operationId": "get_shared_conversation", "parameters": [{"name": "share_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Share Id"}}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 500, "minimum": 1, "default": 200, "title": "Limit"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MessageHistoryPublic"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}}, "components": {"schemas": {"AWSAccountCreate": {"properties": {"name": {"type": "string", "maxLength": 255, "minLength": 1, "title": "Name"}, "description": {"anyOf": [{"type": "string", "maxLength": 1000}, {"type": "null"}], "title": "Description"}, "environment": {"$ref": "#/components/schemas/AccountEnvironement"}, "account_id": {"type": "string", "maxLength": 12, "minLength": 12, "title": "Account Id"}, "access_key_id": {"type": "string", "maxLength": 128, "title": "Access Key Id"}, "secret_access_key": {"type": "string", "maxLength": 256, "title": "Secret Access Key"}, "workspace_id": {"type": "string", "format": "uuid", "title": "Workspace Id"}, "regions": {"items": {"type": "string"}, "type": "array", "title": "Regions", "default": []}, "types": {"items": {"type": "string"}, "type": "array", "title": "Types", "default": []}, "cron_pattern": {"type": "string", "maxLength": 20, "minLength": 1, "title": "<PERSON><PERSON>"}}, "type": "object", "required": ["name", "environment", "account_id", "access_key_id", "secret_access_key", "workspace_id", "cron_pattern"], "title": "AWSAccountCreate"}, "AWSAccountDetail": {"properties": {"name": {"type": "string", "maxLength": 255, "minLength": 1, "title": "Name"}, "description": {"anyOf": [{"type": "string", "maxLength": 1000}, {"type": "null"}], "title": "Description"}, "environment": {"$ref": "#/components/schemas/AccountEnvironement"}, "workspace_id": {"type": "string", "format": "uuid", "title": "Workspace Id"}, "id": {"type": "string", "format": "uuid", "title": "Id"}, "access_key_id": {"type": "string", "title": "Access Key Id"}, "secret_access_key": {"type": "string", "title": "Secret Access Key"}, "account_id": {"type": "string", "title": "Account Id"}}, "type": "object", "required": ["name", "environment", "workspace_id", "id", "access_key_id", "secret_access_key", "account_id"], "title": "AWSAccountDetail"}, "AWSAccountPublic": {"properties": {"name": {"type": "string", "maxLength": 255, "minLength": 1, "title": "Name"}, "description": {"anyOf": [{"type": "string", "maxLength": 1000}, {"type": "null"}], "title": "Description"}, "environment": {"$ref": "#/components/schemas/AccountEnvironement"}, "workspace_id": {"type": "string", "format": "uuid", "title": "Workspace Id"}, "id": {"type": "string", "format": "uuid", "title": "Id"}}, "type": "object", "required": ["name", "environment", "workspace_id", "id"], "title": "AWSAccountPublic"}, "AWSAccountUpdate": {"properties": {"name": {"anyOf": [{"type": "string", "maxLength": 255, "minLength": 1}, {"type": "null"}], "title": "Name"}, "description": {"anyOf": [{"type": "string", "maxLength": 1000}, {"type": "null"}], "title": "Description"}, "environment": {"anyOf": [{"type": "string", "maxLength": 50}, {"type": "null"}], "title": "Environment"}, "account_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Account Id"}, "regions": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Regions", "default": []}, "types": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Types", "default": []}, "cron_pattern": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "<PERSON><PERSON>"}, "access_key_id": {"anyOf": [{"type": "string", "maxLength": 128}, {"type": "null"}], "title": "Access Key Id"}, "secret_access_key": {"anyOf": [{"type": "string", "maxLength": 256}, {"type": "null"}], "title": "Secret Access Key"}}, "type": "object", "title": "AWSAccountUpdate"}, "AWSAccountsPublic": {"properties": {"data": {"items": {"$ref": "#/components/schemas/AWSAccountPublic"}, "type": "array", "title": "Data"}, "count": {"type": "integer", "title": "Count"}}, "type": "object", "required": ["data", "count"], "title": "AWSAccountsPublic"}, "AccountEnvironement": {"type": "string", "enum": ["production", "staging", "development"], "title": "AccountEnvironement"}, "ActivationResponse": {"properties": {"message": {"type": "string", "title": "Message"}, "expires_at": {"type": "string", "format": "date-time", "title": "Expires At"}}, "type": "object", "required": ["message", "expires_at"], "title": "ActivationResponse"}, "ActivationResult": {"properties": {"success": {"type": "boolean", "title": "Success"}, "message": {"type": "string", "title": "Message"}, "redirect_url": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Redirect Url"}, "welcome_message": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Welcome Message"}}, "type": "object", "required": ["success", "message"], "title": "ActivationResult"}, "Address": {"properties": {"city": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "City"}, "country": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Country"}, "line1": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Line1"}, "line2": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Line2"}, "postal_code": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Postal Code"}, "state": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "State"}}, "type": "object", "title": "Address"}, "AgentConnectorCreate": {"properties": {"agent_id": {"type": "string", "format": "uuid", "title": "Agent Id"}, "builtin_connector_ids": {"items": {"type": "string", "format": "uuid"}, "type": "array", "title": "Builtin Connector Ids"}, "mcp_servers": {"items": {"type": "string"}, "type": "array", "title": "Mcp Servers"}}, "type": "object", "required": ["agent_id", "builtin_connector_ids", "mcp_servers"], "title": "AgentConnectorCreate"}, "AgentConnectorResponse": {"properties": {"agent_id": {"type": "string", "format": "uuid", "title": "Agent Id"}, "builtin_connectors": {"items": {"$ref": "#/components/schemas/BuiltInConnectorResponse"}, "type": "array", "title": "Builtin Connectors"}, "mcp_servers": {"items": {"type": "string"}, "type": "array", "title": "Mcp Servers"}}, "type": "object", "required": ["agent_id", "builtin_connectors", "mcp_servers"], "title": "AgentConnectorResponse"}, "AgentConnectorUpdate": {"properties": {"builtin_connector_ids": {"items": {"type": "string", "format": "uuid"}, "type": "array", "title": "Builtin Connector Ids"}, "mcp_servers": {"items": {"type": "string"}, "type": "array", "title": "Mcp Servers"}}, "type": "object", "required": ["builtin_connector_ids", "mcp_servers"], "title": "AgentConnectorUpdate"}, "AgentContextListInput": {"properties": {"agent_ids": {"items": {"type": "string", "format": "uuid"}, "type": "array", "title": "Agent Ids"}}, "type": "object", "required": ["agent_ids"], "title": "AgentContextListInput"}, "AgentContextListResponse": {"properties": {"data": {"items": {"$ref": "#/components/schemas/AgentContextRead"}, "type": "array", "title": "Data"}, "count": {"type": "integer", "title": "Count"}}, "type": "object", "required": ["data", "count"], "title": "AgentContextListResponse", "description": "Response model for paginated agent context list.\n\nAttributes:\n    data: List of agent context items\n    count: Total number of items available (before pagination)"}, "AgentContextRead": {"properties": {"title": {"type": "string", "maxLength": 255, "minLength": 0, "title": "Title"}, "context": {"type": "string", "maxLength": 5000, "minLength": 0, "title": "Context"}, "is_active": {"type": "boolean", "title": "Is Active", "default": true}, "id": {"type": "string", "format": "uuid", "title": "Id"}, "agent_id": {"type": "string", "format": "uuid", "title": "Agent Id"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}}, "type": "object", "required": ["title", "context", "id", "agent_id", "created_at"], "title": "AgentContextRead"}, "AgentContextUpdate": {"properties": {"title": {"anyOf": [{"type": "string", "maxLength": 255}, {"type": "null"}], "title": "Title"}, "context": {"anyOf": [{"type": "string", "maxLength": 5000}, {"type": "null"}], "title": "Context"}, "is_active": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Is Active"}}, "type": "object", "title": "AgentContextUpdate"}, "AgentCreate": {"properties": {"title": {"type": "string", "maxLength": 255, "minLength": 1, "title": "Title", "description": "The title/name of the agent"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description", "description": "Detailed description of the agent's purpose"}, "type": {"$ref": "#/components/schemas/AgentType", "description": "Type of the agent", "default": "conversation_agent"}, "instructions": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Instructions", "description": "Custom instructions for the agent"}}, "type": "object", "required": ["title"], "title": "AgentCreate"}, "AgentPublic": {"properties": {"title": {"type": "string", "maxLength": 255, "minLength": 1, "title": "Title", "description": "The title/name of the agent"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description", "description": "Detailed description of the agent's purpose"}, "type": {"$ref": "#/components/schemas/AgentType", "description": "Type of the agent", "default": "conversation_agent"}, "instructions": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Instructions", "description": "Custom instructions for the agent"}, "id": {"type": "string", "format": "uuid", "title": "Id"}, "is_active": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Is Active"}}, "type": "object", "required": ["title", "id"], "title": "AgentPublic"}, "AgentType": {"type": "string", "enum": ["conversation_agent", "autonomous_agent"], "title": "AgentType", "description": "Defines the supported types of agents in the system."}, "AgentTypeUsage": {"properties": {"agent_type": {"type": "string", "title": "Agent Type"}, "total_tokens": {"type": "integer", "minimum": 0, "title": "Total Tokens"}}, "type": "object", "required": ["agent_type", "total_tokens"], "title": "AgentTypeUsage"}, "AgentUpdate": {"properties": {"title": {"anyOf": [{"type": "string", "maxLength": 255, "minLength": 1}, {"type": "null"}], "title": "Title"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description", "description": "Detailed description of the agent's purpose"}, "type": {"$ref": "#/components/schemas/AgentType", "description": "Type of the agent", "default": "conversation_agent"}, "instructions": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Instructions", "description": "Custom instructions for the agent"}, "workspace_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Workspace Id"}, "is_active": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Is Active"}}, "type": "object", "title": "AgentUpdate"}, "AgentsPublic": {"properties": {"data": {"items": {"$ref": "#/components/schemas/AgentPublic"}, "type": "array", "title": "Data"}, "count": {"type": "integer", "title": "Count"}}, "type": "object", "required": ["data", "count"], "title": "AgentsPublic"}, "AlertCreate": {"properties": {"title": {"type": "string", "maxLength": 200, "title": "Title", "description": "Alert title"}, "description": {"type": "string", "title": "Description", "description": "Detailed alert description"}, "severity": {"$ref": "#/components/schemas/AlertSeverity", "description": "Alert severity level"}}, "type": "object", "required": ["title", "description", "severity"], "title": "AlertCreate", "description": "Schema for creating a new alert"}, "AlertList": {"properties": {"data": {"items": {"$ref": "#/components/schemas/AlertResponse"}, "type": "array", "title": "Data"}, "total": {"type": "integer", "title": "Total"}}, "type": "object", "required": ["data", "total"], "title": "AlertList", "description": "Schema for list of alerts with pagination"}, "AlertResponse": {"properties": {"title": {"type": "string", "maxLength": 200, "title": "Title", "description": "Alert title"}, "description": {"type": "string", "title": "Description", "description": "Detailed alert description"}, "severity": {"$ref": "#/components/schemas/AlertSeverity", "description": "Alert severity level"}, "id": {"type": "string", "format": "uuid", "title": "Id"}, "workspace_id": {"type": "string", "format": "uuid", "title": "Workspace Id"}, "status": {"$ref": "#/components/schemas/AlertStatus"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}, "updated_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Updated At"}}, "type": "object", "required": ["title", "description", "severity", "id", "workspace_id", "status", "created_at"], "title": "AlertResponse", "description": "Schema for alert response"}, "AlertSeverity": {"type": "string", "enum": ["CRITICAL", "HIGH", "MEDIUM", "LOW", "INFO"], "title": "Alert<PERSON>everity"}, "AlertStatus": {"type": "string", "enum": ["OPEN", "ACKNOWLEDGED", "RESOLVED", "CLOSED"], "title": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "AlertStatusSummary": {"properties": {"status_counts": {"additionalProperties": {"type": "integer"}, "type": "object", "title": "Status Counts", "description": "Count of alerts by status"}, "total": {"type": "integer", "title": "Total", "description": "Total number of alerts in the period"}}, "type": "object", "required": ["status_counts", "total"], "title": "AlertStatusSummary", "description": "Summary of alerts by status for the last 30 days"}, "AlertUpdate": {"properties": {"title": {"anyOf": [{"type": "string", "maxLength": 200}, {"type": "null"}], "title": "Title"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description"}, "severity": {"anyOf": [{"$ref": "#/components/schemas/AlertSeverity"}, {"type": "null"}]}, "status": {"anyOf": [{"$ref": "#/components/schemas/AlertStatus"}, {"type": "null"}]}}, "type": "object", "title": "AlertUpdate", "description": "Schema for updating an existing alert"}, "AsyncTaskStatus": {"type": "string", "enum": ["PENDING", "PROGRESS", "SUCCESS", "FAILURE"], "title": "AsyncTaskStatus"}, "AvailableUser": {"properties": {"id": {"type": "string", "format": "uuid", "title": "Id"}, "email": {"type": "string", "title": "Email"}, "full_name": {"type": "string", "title": "Full Name"}}, "type": "object", "required": ["id", "email", "full_name"], "title": "AvailableUser"}, "AvailableUsersCurrentWorkspace": {"properties": {"data": {"items": {"$ref": "#/components/schemas/AvailableUser"}, "type": "array", "title": "Data"}, "count": {"type": "integer", "title": "Count"}}, "type": "object", "required": ["data", "count"], "title": "AvailableUsersCurrentWorkspace"}, "BillingDetails": {"properties": {"address": {"$ref": "#/components/schemas/Address"}, "email": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Email"}, "name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Name"}, "phone": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Phone"}}, "type": "object", "required": ["address"], "title": "BillingDetails"}, "Body_login-login_access_token": {"properties": {"grant_type": {"type": "string", "pattern": "password", "title": "Grant Type"}, "username": {"type": "string", "title": "Username"}, "password": {"type": "string", "title": "Password"}, "scope": {"type": "string", "title": "<PERSON><PERSON>", "default": ""}, "client_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Client Id"}, "client_secret": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Client Secret"}, "slackOAuth": {"type": "boolean", "title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "default": false}, "appId": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "App<PERSON>"}, "teamId": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "<PERSON><PERSON>"}}, "type": "object", "required": ["username", "password"], "title": "Body_login-login_access_token"}, "BuiltInConnector": {"properties": {"id": {"type": "string", "format": "uuid", "title": "Id"}, "name": {"type": "string", "title": "Name", "description": "Unique identifier for the connector"}, "display_name": {"type": "string", "title": "Display Name", "description": "Human-readable name for the connector"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description"}, "default_required_permission": {"type": "boolean", "title": "<PERSON><PERSON>ult Required Permission", "default": false}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}, "updated_at": {"type": "string", "format": "date-time", "title": "Updated At"}}, "type": "object", "required": ["name", "display_name"], "title": "BuiltInConnector", "description": "Definition of built-in connectors available in the system"}, "BuiltInConnectorResponse": {"properties": {"id": {"type": "string", "format": "uuid", "title": "Id"}, "name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Name"}, "display_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Display Name"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description"}}, "type": "object", "required": ["id"], "title": "BuiltInConnectorResponse"}, "CardDetails": {"properties": {"brand": {"type": "string", "title": "Brand"}, "country": {"type": "string", "title": "Country"}, "display_brand": {"type": "string", "title": "Display Brand"}, "exp_month": {"type": "integer", "title": "Exp Month"}, "exp_year": {"type": "integer", "title": "Exp Year"}, "last4": {"type": "string", "title": "Last4"}}, "type": "object", "required": ["brand", "country", "display_brand", "exp_month", "exp_year", "last4"], "title": "CardDetails"}, "ChartDataPoint": {"properties": {"date": {"type": "string", "format": "date-time", "title": "Date"}, "value": {"type": "number", "title": "Value"}}, "type": "object", "required": ["date", "value"], "title": "ChartDataPoint"}, "ChartType": {"type": "string", "enum": ["line", "bar", "pie", "doughnut", "area", "scatter", "radar", "step_area"], "title": "ChartType", "description": "Enum for different types of charts available in the system"}, "CheckoutSessionResponse": {"properties": {"checkout_session_url": {"type": "string", "title": "Checkout Session Url"}}, "type": "object", "required": ["checkout_session_url"], "title": "CheckoutSessionResponse"}, "CitationMetadata": {"properties": {"ref_id": {"type": "integer", "title": "Ref Id"}, "doc_name": {"type": "string", "title": "Doc Name"}, "doc_section": {"type": "string", "title": "Doc Section"}, "text_snippet": {"type": "string", "title": "Text Snippet"}}, "type": "object", "required": ["ref_id", "doc_name", "doc_section", "text_snippet"], "title": "CitationMetadata", "description": "Metadata for document citations"}, "CloudProvider": {"type": "string", "enum": ["AWS"], "title": "CloudProvider"}, "ConfirmUploadsRequest": {"properties": {"uploaded_files": {"items": {"$ref": "#/components/schemas/UploadedFileInfo"}, "type": "array", "title": "Uploaded Files", "description": "Information about successfully uploaded files"}}, "type": "object", "required": ["uploaded_files"], "title": "ConfirmUploadsRequest", "description": "Request to confirm file uploads and start ingestion"}, "ConnectorCreate": {"properties": {"name": {"type": "string", "title": "Name"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description"}, "type": {"$ref": "#/components/schemas/ConnectorType"}, "approval": {"type": "boolean", "title": "Approval", "default": false}, "config": {"type": "object", "title": "Config", "default": {}}}, "type": "object", "required": ["name", "type"], "title": "ConnectorCreate"}, "ConnectorList": {"properties": {"data": {"items": {"$ref": "#/components/schemas/BuiltInConnector"}, "type": "array", "title": "Data"}, "total": {"type": "integer", "title": "Total"}}, "type": "object", "required": ["data", "total"], "title": "ConnectorList"}, "ConnectorResponse": {"properties": {"id": {"type": "string", "format": "uuid", "title": "Id"}, "name": {"type": "string", "title": "Name"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description"}, "type": {"$ref": "#/components/schemas/ConnectorType"}, "approval": {"type": "boolean", "title": "Approval", "default": false}, "config": {"type": "object", "title": "Config"}, "workspace_id": {"type": "string", "format": "uuid", "title": "Workspace Id"}, "created_by": {"type": "string", "format": "uuid", "title": "Created By"}, "updated_by": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Updated By"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}, "updated_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Updated At"}}, "type": "object", "required": ["id", "name", "description", "type", "config", "workspace_id", "created_by", "updated_by", "created_at", "updated_at"], "title": "ConnectorResponse"}, "ConnectorType": {"type": "string", "enum": ["bedrock_kb", "open_api"], "title": "ConnectorType"}, "ConnectorUpdate": {"properties": {"name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Name"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description"}, "config": {"anyOf": [{"type": "object"}, {"type": "null"}], "title": "Config"}, "approval": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Approval"}}, "type": "object", "title": "ConnectorUpdate"}, "ConnectorWithStatusResponse": {"properties": {"id": {"type": "string", "format": "uuid", "title": "Id"}, "name": {"type": "string", "title": "Name"}, "display_name": {"type": "string", "title": "Display Name"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description"}, "is_active": {"type": "boolean", "title": "Is Active"}, "required_permission": {"type": "boolean", "title": "Required Permission", "default": false}}, "type": "object", "required": ["id", "name", "display_name", "is_active"], "title": "ConnectorWithStatusResponse", "description": "Response model for a connector with its active status and permission settings in a workspace.\n\nAttributes:\n    id: Unique identifier for the workspace-connector association\n    name: Unique name of the connector\n    display_name: Human-readable name for the connector\n    description: Optional description of the connector\n    is_active: Whether the connector is active in this workspace\n    required_permission: Whether this tool requires human approval before execution"}, "ConversationCreateRequest": {"properties": {"agent_id": {"type": "string", "format": "uuid4", "title": "Agent Id"}, "model_provider": {"type": "string", "title": "Model Provider", "default": "bedrock"}, "resource_id": {"anyOf": [{"type": "string", "format": "uuid4"}, {"type": "null"}], "title": "Resource Id"}, "instructions": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Instructions"}}, "type": "object", "required": ["agent_id"], "title": "ConversationCreateRequest"}, "ConversationPublic": {"properties": {"id": {"type": "string", "format": "uuid", "title": "Id"}, "agent_id": {"type": "string", "format": "uuid", "title": "Agent Id"}, "name": {"type": "string", "title": "Name"}, "model_provider": {"type": "string", "title": "Model Provider"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}}, "type": "object", "required": ["id", "agent_id", "name", "model_provider", "created_at"], "title": "ConversationPublic"}, "ConversationsPublic": {"properties": {"data": {"items": {"$ref": "#/components/schemas/ConversationPublic"}, "type": "array", "title": "Data"}, "count": {"type": "integer", "title": "Count"}}, "type": "object", "required": ["data", "count"], "title": "ConversationsPublic", "description": "Response model for paginated conversations list.\n\nAttributes:\n    data: List of conversation items\n    count: Total number of items available (before pagination)"}, "DailyMessageVolume": {"properties": {"date": {"type": "string", "format": "date-time", "title": "Date"}, "message_count": {"type": "integer", "title": "Message Count"}}, "type": "object", "required": ["date", "message_count"], "title": "DailyMessageVolume"}, "DailyTokenUsage": {"properties": {"date": {"type": "string", "format": "date-time", "title": "Date"}, "total_tokens": {"type": "integer", "title": "Tokens"}}, "type": "object", "required": ["date", "total_tokens"], "title": "DailyTokenUsage"}, "Document": {"properties": {"id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Id"}, "metadata": {"type": "object", "title": "<PERSON><PERSON><PERSON>"}, "page_content": {"type": "string", "title": "Page Content"}, "type": {"type": "string", "const": "Document", "title": "Type", "default": "Document"}}, "type": "object", "required": ["page_content"], "title": "Document", "description": "Class for storing a piece of text and associated metadata.\n\nExample:\n\n    .. code-block:: python\n\n        from langchain_core.documents import Document\n\n        document = Document(\n            page_content=\"Hello, world!\",\n            metadata={\"source\": \"https://example.com\"}\n        )"}, "DocumentKBRead": {"properties": {"name": {"type": "string", "maxLength": 255, "minLength": 0, "title": "Name"}, "type": {"$ref": "#/components/schemas/DocumentType"}, "url": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Url"}, "deep_crawl": {"type": "boolean", "title": "Deep Crawl", "default": false}, "file_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "File Name"}, "file_type": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "File Type"}, "object_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Object Name"}, "embed_status": {"$ref": "#/components/schemas/AsyncTaskStatus", "default": "PENDING"}, "id": {"type": "string", "format": "uuid", "title": "Id"}, "kb_id": {"type": "string", "format": "uuid", "title": "Kb Id"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}, "updated_at": {"type": "string", "format": "date-time", "title": "Updated At"}, "is_deleted": {"type": "boolean", "title": "Is Deleted"}, "parent_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Parent Id"}, "children": {"items": {"$ref": "#/components/schemas/DocumentKBRead"}, "type": "array", "title": "Children", "default": []}}, "type": "object", "required": ["name", "type", "id", "kb_id", "created_at", "updated_at", "is_deleted"], "title": "DocumentKBRead"}, "DocumentType": {"type": "string", "enum": ["url", "file"], "title": "DocumentType"}, "DocumentsKBRead": {"properties": {"data": {"items": {"$ref": "#/components/schemas/DocumentKBRead"}, "type": "array", "title": "Data"}, "count": {"type": "integer", "title": "Count"}}, "type": "object", "required": ["data", "count"], "title": "DocumentsKBRead"}, "EnterpriseEnquiryMessageResponse": {"properties": {"message": {"type": "string", "title": "Message"}}, "type": "object", "required": ["message"], "title": "EnterpriseEnquiryMessageResponse", "description": "Response model for enterprise enquiry status messages"}, "EnterpriseEnquiryRequest": {"properties": {"first_name": {"type": "string", "title": "First Name"}, "last_name": {"type": "string", "title": "Last Name"}, "work_title": {"type": "string", "title": "Work Title"}, "work_email": {"type": "string", "title": "Work Email"}, "company_name": {"type": "string", "title": "Company Name"}, "estimated_monthly_cost": {"type": "string", "title": "Estimated Monthly Cost"}, "message": {"type": "string", "title": "Message"}, "product_id": {"type": "string", "format": "uuid", "title": "Product Id"}}, "type": "object", "required": ["first_name", "last_name", "work_title", "work_email", "company_name", "estimated_monthly_cost", "message", "product_id"], "title": "EnterpriseEnquiryRequest"}, "ErrorResponse": {"properties": {"error": {"type": "string", "title": "Error"}, "details": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Details"}}, "type": "object", "required": ["error"], "title": "ErrorResponse"}, "FeedbackType": {"type": "string", "enum": ["good", "bad"], "title": "FeedbackType", "description": "Enumeration for feedback types on agent responses."}, "FileInfo": {"properties": {"file_id": {"type": "string", "title": "File Id", "description": "Client-side ID for tracking this file"}, "filename": {"type": "string", "maxLength": 255, "minLength": 1, "title": "Filename", "description": "Original filename"}, "content_type": {"type": "string", "title": "Content Type", "description": "File MIME type"}, "file_size": {"type": "integer", "maximum": 10485760, "exclusiveMinimum": 0, "title": "File Size", "description": "File size in bytes"}}, "type": "object", "required": ["file_id", "filename", "content_type", "file_size"], "title": "FileInfo", "description": "Information about a file to generate a presigned URL for"}, "HTTPValidationError": {"properties": {"detail": {"items": {"$ref": "#/components/schemas/ValidationError"}, "type": "array", "title": "Detail"}}, "type": "object", "title": "HTTPValidationError"}, "InvoiceResponse": {"properties": {"id": {"type": "string", "title": "Id"}, "customer": {"type": "string", "title": "Customer"}, "status": {"type": "string", "title": "Status"}, "amount_due": {"type": "integer", "title": "Amount Due"}, "amount_paid": {"type": "integer", "title": "Amount <PERSON>"}, "amount_remaining": {"type": "integer", "title": "Amount Re<PERSON>ining"}, "currency": {"type": "string", "title": "<PERSON><PERSON><PERSON><PERSON>"}, "invoice_pdf": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Invoice Pdf"}, "created": {"type": "integer", "title": "Created"}, "due_date": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Due Date"}, "paid": {"type": "boolean", "title": "Paid"}, "payment_intent": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Payment Intent"}, "subscription": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Subscription"}, "total": {"type": "integer", "title": "Total"}}, "type": "object", "required": ["id", "customer", "status", "amount_due", "amount_paid", "amount_remaining", "currency", "created", "paid", "total"], "title": "InvoiceResponse"}, "Item": {"properties": {"title": {"type": "string", "maxLength": 255, "title": "Title"}, "description": {"anyOf": [{"type": "string", "maxLength": 255}, {"type": "null"}], "title": "Description"}, "id": {"type": "string", "format": "uuid", "title": "Id"}}, "type": "object", "required": ["title"], "title": "<PERSON><PERSON>"}, "ItemCreate": {"properties": {"title": {"type": "string", "maxLength": 255, "minLength": 1, "title": "Title"}, "description": {"anyOf": [{"type": "string", "maxLength": 255}, {"type": "null"}], "title": "Description"}}, "type": "object", "required": ["title"], "title": "ItemCreate"}, "ItemPublic": {"properties": {"title": {"type": "string", "maxLength": 255, "minLength": 1, "title": "Title"}, "description": {"anyOf": [{"type": "string", "maxLength": 255}, {"type": "null"}], "title": "Description"}, "id": {"type": "string", "format": "uuid", "title": "Id"}, "owner_id": {"type": "string", "format": "uuid", "title": "Owner Id"}}, "type": "object", "required": ["title", "id", "owner_id"], "title": "ItemPublic"}, "ItemUpdate": {"properties": {"title": {"anyOf": [{"type": "string", "maxLength": 255, "minLength": 1}, {"type": "null"}], "title": "Title"}, "description": {"anyOf": [{"type": "string", "maxLength": 255}, {"type": "null"}], "title": "Description"}}, "type": "object", "title": "ItemUpdate"}, "ItemsPublic": {"properties": {"data": {"items": {"$ref": "#/components/schemas/ItemPublic"}, "type": "array", "title": "Data"}, "count": {"type": "integer", "title": "Count"}}, "type": "object", "required": ["data", "count"], "title": "ItemsPublic"}, "KBAccessLevel": {"type": "string", "enum": ["private", "shared"], "title": "KBAccessLevel"}, "KBCreate": {"properties": {"title": {"type": "string", "maxLength": 255, "minLength": 1, "title": "Title"}, "description": {"anyOf": [{"type": "string", "maxLength": 1000}, {"type": "null"}], "title": "Description"}, "access_level": {"$ref": "#/components/schemas/KBAccessLevel", "default": "private"}, "usage_mode": {"$ref": "#/components/schemas/KBUsageMode", "default": "manual"}, "tags": {"items": {"type": "string"}, "type": "array", "title": "Tags", "default": []}, "allowed_users": {"items": {"type": "string", "format": "uuid"}, "type": "array", "title": "Allowed Users", "default": []}}, "type": "object", "required": ["title"], "title": "KBCreate"}, "KBRead": {"properties": {"title": {"type": "string", "maxLength": 255, "minLength": 1, "title": "Title"}, "description": {"anyOf": [{"type": "string", "maxLength": 1000}, {"type": "null"}], "title": "Description"}, "access_level": {"$ref": "#/components/schemas/KBAccessLevel"}, "usage_mode": {"$ref": "#/components/schemas/KBUsageMode"}, "tags": {"items": {"type": "string"}, "type": "array", "title": "Tags", "default": []}, "id": {"type": "string", "format": "uuid", "title": "Id"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}, "updated_at": {"type": "string", "format": "date-time", "title": "Updated At"}, "is_deleted": {"type": "boolean", "title": "Is Deleted"}, "allowed_users": {"items": {"type": "string", "format": "uuid"}, "type": "array", "title": "Allowed Users"}, "owner_id": {"type": "string", "format": "uuid", "title": "Owner Id"}}, "type": "object", "required": ["title", "access_level", "usage_mode", "id", "created_at", "updated_at", "is_deleted", "owner_id"], "title": "KBRead"}, "KBUpdate": {"properties": {"title": {"anyOf": [{"type": "string", "maxLength": 255}, {"type": "null"}], "title": "Title"}, "description": {"anyOf": [{"type": "string", "maxLength": 1000}, {"type": "null"}], "title": "Description"}, "access_level": {"anyOf": [{"$ref": "#/components/schemas/KBAccessLevel"}, {"type": "null"}]}, "tags": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Tags"}, "allowed_users": {"anyOf": [{"items": {"type": "string", "format": "uuid"}, "type": "array"}, {"type": "null"}], "title": "Allowed Users"}, "usage_mode": {"anyOf": [{"$ref": "#/components/schemas/KBUsageMode"}, {"type": "null"}]}}, "type": "object", "title": "KBUpdate"}, "KBUsageMode": {"type": "string", "enum": ["manual", "agent_requested", "always"], "title": "KBUsageMode"}, "KBsRead": {"properties": {"data": {"items": {"$ref": "#/components/schemas/KBRead"}, "type": "array", "title": "Data"}, "count": {"type": "integer", "title": "Count"}}, "type": "object", "required": ["data", "count"], "title": "KBsRead"}, "MCPConfigCreate": {"properties": {"config": {"type": "object", "title": "Config"}}, "type": "object", "required": ["config"], "title": "MCPConfigCreate"}, "MCPConfigResponse": {"properties": {"id": {"type": "string", "format": "uuid", "title": "Id"}, "workspace_id": {"type": "string", "format": "uuid", "title": "Workspace Id"}, "config": {"type": "object", "title": "Config"}, "created_at": {"title": "Created At"}, "updated_at": {"title": "Updated At"}}, "type": "object", "required": ["id", "workspace_id", "config", "created_at", "updated_at"], "title": "MCPConfigResponse"}, "MCPServerInfo": {"properties": {"name": {"type": "string", "title": "Name"}, "type": {"$ref": "#/components/schemas/MCPServerTransport"}, "tool_list": {"items": {"type": "string"}, "type": "array", "title": "Tool List"}, "resource_list": {"items": {"type": "string"}, "type": "array", "title": "Resource List"}, "connected": {"type": "boolean", "title": "Connected", "default": false}, "connection_error": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Connection Error"}, "is_active": {"type": "boolean", "title": "Is Active", "default": true}, "is_builtin": {"type": "boolean", "title": "Is Builtin", "description": "Whether the server is built-in and cannot be edited", "default": false}, "tools_permissions": {"items": {"type": "string"}, "type": "array", "title": "Tools Permissions", "description": "Permissions for the tools of the server", "default": []}}, "type": "object", "required": ["name", "type", "tool_list", "resource_list"], "title": "MCPServerInfo", "description": "Basic information about an MCP server"}, "MCPServerListResponse": {"properties": {"workspace_id": {"type": "string", "format": "uuid", "title": "Workspace Id"}, "servers": {"items": {"anyOf": [{"$ref": "#/components/schemas/MCPSseServerResponse"}, {"$ref": "#/components/schemas/MCPStdioServerResponse"}]}, "type": "array", "title": "Servers"}}, "type": "object", "required": ["workspace_id", "servers"], "title": "MCPServerListResponse", "description": "Response model for listing all MCP servers"}, "MCPServerTransport": {"type": "string", "enum": ["stdio", "sse"], "title": "MCPServerTransport", "description": "Transport types for MCP servers"}, "MCPSseServerResponse": {"properties": {"name": {"type": "string", "title": "Name"}, "type": {"$ref": "#/components/schemas/MCPServerTransport"}, "tool_list": {"items": {"type": "string"}, "type": "array", "title": "Tool List"}, "resource_list": {"items": {"type": "string"}, "type": "array", "title": "Resource List"}, "connected": {"type": "boolean", "title": "Connected", "default": false}, "connection_error": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Connection Error"}, "is_active": {"type": "boolean", "title": "Is Active", "default": true}, "is_builtin": {"type": "boolean", "title": "Is Builtin", "description": "Whether the server is built-in and cannot be edited", "default": false}, "tools_permissions": {"items": {"type": "string"}, "type": "array", "title": "Tools Permissions", "description": "Permissions for the tools of the server", "default": []}, "url": {"type": "string", "title": "Url"}, "headers": {"additionalProperties": {"type": "string"}, "type": "object", "title": "Headers"}}, "type": "object", "required": ["name", "type", "tool_list", "resource_list", "url"], "title": "MCPSseServerResponse", "description": "Response model for SSE transport server"}, "MCPStdioServerResponse": {"properties": {"name": {"type": "string", "title": "Name"}, "type": {"$ref": "#/components/schemas/MCPServerTransport"}, "tool_list": {"items": {"type": "string"}, "type": "array", "title": "Tool List"}, "resource_list": {"items": {"type": "string"}, "type": "array", "title": "Resource List"}, "connected": {"type": "boolean", "title": "Connected", "default": false}, "connection_error": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Connection Error"}, "is_active": {"type": "boolean", "title": "Is Active", "default": true}, "is_builtin": {"type": "boolean", "title": "Is Builtin", "description": "Whether the server is built-in and cannot be edited", "default": false}, "tools_permissions": {"items": {"type": "string"}, "type": "array", "title": "Tools Permissions", "description": "Permissions for the tools of the server", "default": []}, "command": {"type": "string", "title": "Command"}, "args": {"items": {"type": "string"}, "type": "array", "title": "<PERSON><PERSON><PERSON>"}}, "type": "object", "required": ["name", "type", "tool_list", "resource_list", "command", "args"], "title": "MCPStdioServerResponse", "description": "Response model for stdio transport server"}, "Memory": {"properties": {"tags": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Tags", "metadata": {"description": "Tags extracted from the conversation."}}, "task": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Task", "metadata": {"description": "A specific task or problem (error, issue, question, etc.) that was addressed."}}, "solution": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Solution", "metadata": {"description": "Detailed solution or approach to solve the task or problem."}}, "links": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Links", "metadata": {"description": "References to related memories"}}, "id": {"type": "string", "title": "Id"}, "agent_role": {"type": "string", "title": "Agent Role"}}, "type": "object", "required": ["id", "agent_role"], "title": "Memory"}, "MemoryFilter": {"properties": {"agent_roles": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Agent Roles"}, "limit": {"type": "integer", "title": "Limit", "default": 50}}, "type": "object", "title": "<PERSON><PERSON><PERSON>er"}, "MemoryNode": {"properties": {"tags": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Tags", "metadata": {"description": "Tags extracted from the conversation."}}, "task": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Task", "metadata": {"description": "A specific task or problem (error, issue, question, etc.) that was addressed."}}, "solution": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Solution", "metadata": {"description": "Detailed solution or approach to solve the task or problem."}}, "links": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Links", "metadata": {"description": "References to related memories"}}}, "type": "object", "title": "MemoryNode", "description": "Model for a memory node extracted from a conversation."}, "MemoryUpdate": {"properties": {"id": {"type": "string", "title": "Id"}, "agent_role": {"type": "string", "title": "Agent Role"}, "memory": {"$ref": "#/components/schemas/MemoryNode"}}, "type": "object", "required": ["id", "agent_role", "memory"], "title": "MemoryUpdate"}, "MemorysRead": {"properties": {"memories": {"items": {"$ref": "#/components/schemas/Memory"}, "type": "array", "title": "Memories"}, "count": {"type": "integer", "title": "Count"}}, "type": "object", "required": ["memories", "count"], "title": "MemorysRead"}, "Message": {"properties": {"content": {"type": "string", "title": "Content"}, "role": {"type": "string", "maxLength": 50, "title": "Role", "default": "user"}, "is_interrupt": {"type": "boolean", "title": "Is Interrupt", "default": false}, "interrupt_message": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Interrupt Message"}, "action_type": {"$ref": "#/components/schemas/MessageActionType", "default": "none"}, "id": {"type": "string", "format": "uuid", "title": "Id"}, "conversation_id": {"type": "string", "format": "uuid", "title": "Conversation Id"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}, "updated_at": {"type": "string", "format": "date-time", "title": "Updated At"}, "message_metadata": {"type": "object", "title": "Message Metadata", "default": {}}, "is_deleted": {"type": "boolean", "title": "Is Deleted", "default": false}}, "type": "object", "required": ["content", "conversation_id"], "title": "Message"}, "MessageActionType": {"type": "string", "enum": ["none", "recommendation"], "title": "MessageActionType", "description": "Enum for the type of action that can be taken by the message"}, "MessageDisplayComponentPublic": {"properties": {"id": {"type": "string", "format": "uuid", "title": "Id"}, "type": {"$ref": "#/components/schemas/MessageDisplayComponentType"}, "chart_type": {"anyOf": [{"$ref": "#/components/schemas/ChartType"}, {"type": "null"}]}, "title": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Title"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description"}, "data": {"type": "object", "title": "Data"}, "config": {"type": "object", "title": "Config"}, "position": {"type": "integer", "title": "Position"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}}, "type": "object", "required": ["id", "type", "chart_type", "title", "description", "data", "config", "position", "created_at"], "title": "MessageDisplayComponentPublic", "description": "Public schema for message display components"}, "MessageDisplayComponentType": {"type": "string", "enum": ["table", "chart"], "title": "MessageDisplayComponentType", "description": "Enum for display component types (currently supporting only tables and charts)"}, "MessageFeedbackCreate": {"properties": {"feedback_type": {"$ref": "#/components/schemas/FeedbackType", "description": "Type of feedback (good/bad)"}, "reason": {"anyOf": [{"type": "string", "maxLength": 1000}, {"type": "null"}], "title": "Reason", "description": "Optional reason for the feedback, required when feedback_type is BAD"}, "additional_comments": {"anyOf": [{"type": "string", "maxLength": 2000}, {"type": "null"}], "title": "Additional Comments", "description": "Additional optional comments from the user"}, "message_id": {"type": "string", "format": "uuid", "title": "Message Id"}}, "type": "object", "required": ["feedback_type", "message_id"], "title": "MessageFeedbackCreate", "description": "Schema for creating message feedback"}, "MessageFeedbackPublic": {"properties": {"feedback_type": {"$ref": "#/components/schemas/FeedbackType", "description": "Type of feedback (good/bad)"}, "reason": {"anyOf": [{"type": "string", "maxLength": 1000}, {"type": "null"}], "title": "Reason", "description": "Optional reason for the feedback, required when feedback_type is BAD"}, "additional_comments": {"anyOf": [{"type": "string", "maxLength": 2000}, {"type": "null"}], "title": "Additional Comments", "description": "Additional optional comments from the user"}, "id": {"type": "string", "format": "uuid", "title": "Id"}, "message_id": {"type": "string", "format": "uuid", "title": "Message Id"}, "user_id": {"type": "string", "format": "uuid", "title": "User Id"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}, "updated_at": {"type": "string", "format": "date-time", "title": "Updated At"}}, "type": "object", "required": ["feedback_type", "id", "message_id", "user_id", "created_at", "updated_at"], "title": "MessageFeedbackPublic", "description": "Public schema for message feedback responses"}, "MessageFeedbackUpdate": {"properties": {"feedback_type": {"anyOf": [{"$ref": "#/components/schemas/FeedbackType"}, {"type": "null"}]}, "reason": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Reason"}, "additional_comments": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Additional Comments"}}, "type": "object", "title": "MessageFeedbackUpdate", "description": "Schema for updating message feedback"}, "MessageHistoryPublic": {"properties": {"limit": {"type": "integer", "title": "Limit"}, "has_more": {"type": "boolean", "title": "Has <PERSON>"}, "data": {"items": {"type": "object"}, "type": "array", "title": "Data", "description": "list of messages with agent thoughts. Each message contains: id, message_id, position, thought, tool, tool_input, created_at, observation", "default": []}}, "type": "object", "required": ["limit", "has_more"], "title": "MessageHistoryPublic"}, "MessagePublic": {"properties": {"content": {"type": "string", "title": "Content"}, "resume": {"type": "boolean", "title": "Resume"}, "approve": {"type": "boolean", "title": "Approve"}, "restore": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Rest<PERSON>"}, "message_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Message Id"}, "action_type": {"anyOf": [{"$ref": "#/components/schemas/MessageActionType"}, {"type": "null"}]}, "display_components": {"anyOf": [{"items": {"$ref": "#/components/schemas/MessageDisplayComponentPublic"}, "type": "array"}, {"type": "null"}], "title": "Display Components"}}, "type": "object", "required": ["content", "resume", "approve"], "title": "MessagePublic"}, "MessageStatistics": {"properties": {"total_messages": {"type": "integer", "title": "Total Messages"}, "average_response_time": {"type": "number", "title": "Average Response Time"}, "average_input_tokens_per_message": {"type": "number", "title": "Average Input Tokens per Message"}, "average_output_tokens_per_message": {"type": "number", "title": "Average Output Tokens per Message"}, "daily_message_volume": {"items": {"$ref": "#/components/schemas/DailyMessageVolume"}, "type": "array", "title": "Daily Message Volume"}, "token_distribution_by_message_length": {"items": {"$ref": "#/components/schemas/TokenDistributionCategory"}, "type": "array", "title": "Token Distribution by Message Length"}}, "type": "object", "required": ["total_messages", "average_response_time", "average_input_tokens_per_message", "average_output_tokens_per_message", "daily_message_volume", "token_distribution_by_message_length"], "title": "MessageStatistics"}, "MetricCreate": {"properties": {"name": {"type": "string", "maxLength": 100, "title": "Name"}, "value": {"type": "number", "title": "Value"}, "unit": {"type": "string", "maxLength": 50, "title": "Unit"}, "timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "type": {"$ref": "#/components/schemas/MetricType"}, "resource_id": {"type": "string", "format": "uuid", "title": "Resource Id"}}, "type": "object", "required": ["name", "value", "unit", "timestamp", "type", "resource_id"], "title": "MetricCreate"}, "MetricPublic": {"properties": {"name": {"type": "string", "maxLength": 100, "title": "Name"}, "value": {"type": "number", "title": "Value"}, "unit": {"type": "string", "maxLength": 50, "title": "Unit"}, "timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "type": {"$ref": "#/components/schemas/MetricType"}, "id": {"type": "string", "format": "uuid", "title": "Id"}, "resource_id": {"type": "string", "format": "uuid", "title": "Resource Id"}}, "type": "object", "required": ["name", "value", "unit", "timestamp", "type", "id", "resource_id"], "title": "MetricPublic"}, "MetricRead": {"properties": {"name": {"type": "string", "maxLength": 100, "title": "Name"}, "value": {"type": "number", "title": "Value"}, "unit": {"type": "string", "maxLength": 50, "title": "Unit"}, "timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "type": {"$ref": "#/components/schemas/MetricType"}, "id": {"type": "string", "format": "uuid", "title": "Id"}, "resource_id": {"type": "string", "format": "uuid", "title": "Resource Id"}}, "type": "object", "required": ["name", "value", "unit", "timestamp", "type", "id", "resource_id"], "title": "MetricRead"}, "MetricType": {"type": "string", "enum": ["usage", "performance", "cost"], "title": "MetricType"}, "MetricUpdate": {"properties": {"name": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Name"}, "value": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Value"}, "unit": {"anyOf": [{"type": "string", "maxLength": 50}, {"type": "null"}], "title": "Unit"}, "timestamp": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Timestamp"}, "type": {"anyOf": [{"$ref": "#/components/schemas/MetricType"}, {"type": "null"}]}}, "type": "object", "title": "MetricUpdate"}, "MetricsPublic": {"properties": {"data": {"items": {"$ref": "#/components/schemas/MetricPublic"}, "type": "array", "title": "Data"}, "count": {"type": "integer", "title": "Count"}}, "type": "object", "required": ["data", "count"], "title": "MetricsPublic"}, "ModuleSetting": {"properties": {"key": {"type": "string", "title": "Key"}, "value": {"type": "object", "title": "Value", "default": {}}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}, "updated_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Updated At"}}, "type": "object", "required": ["key"], "title": "ModuleSetting"}, "NewPassword": {"properties": {"token": {"type": "string", "title": "Token"}, "new_password": {"type": "string", "maxLength": 40, "minLength": 8, "title": "New Password"}}, "type": "object", "required": ["token", "new_password"], "title": "NewPassword"}, "NodeType": {"type": "string", "enum": ["start", "tool", "human_in_loop", "end", "output"], "title": "NodeType"}, "NotificationList": {"properties": {"data": {"items": {"$ref": "#/components/schemas/NotificationResponse"}, "type": "array", "title": "Data"}, "count": {"type": "integer", "title": "Count"}}, "type": "object", "required": ["data", "count"], "title": "NotificationList", "description": "Response model for paginated notifications list.\n\nAttributes:\n    data: List of notification items\n    count: Total number of items available (before pagination)"}, "NotificationResponse": {"properties": {"title": {"type": "string", "maxLength": 255, "title": "Title"}, "message": {"type": "string", "title": "Message"}, "type": {"$ref": "#/components/schemas/NotificationType", "default": "info"}, "status": {"$ref": "#/components/schemas/NotificationStatus", "default": "unread"}, "notification_metadata": {"type": "object", "title": "Notification Metadata", "description": "Metadata for the notification"}, "requires_action": {"type": "boolean", "title": "Requires Action", "default": false}, "action_url": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Action Url", "description": "URL for direct action"}, "id": {"type": "string", "format": "uuid", "title": "Id"}, "user_id": {"type": "string", "format": "uuid", "title": "User Id"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}, "updated_at": {"type": "string", "format": "date-time", "title": "Updated At"}, "read_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Read At"}, "expires_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Expires At"}}, "type": "object", "required": ["title", "message", "id", "user_id", "created_at", "updated_at"], "title": "NotificationResponse"}, "NotificationStatus": {"type": "string", "enum": ["unread", "read", "archived"], "title": "NotificationStatus"}, "NotificationType": {"type": "string", "enum": ["info", "warning", "error", "interrupt"], "title": "NotificationType"}, "PaymentMethodResponse": {"properties": {"id": {"type": "string", "title": "Id"}, "billing_details": {"$ref": "#/components/schemas/BillingDetails"}, "card": {"$ref": "#/components/schemas/CardDetails"}, "created": {"type": "integer", "title": "Created"}, "customer": {"type": "string", "title": "Customer"}, "livemode": {"type": "boolean", "title": "Livemode"}, "type": {"type": "string", "title": "Type"}}, "type": "object", "required": ["id", "billing_details", "card", "created", "customer", "livemode", "type"], "title": "PaymentMethodResponse"}, "PlanChangeRequestCreate": {"properties": {"first_name": {"type": "string", "title": "First Name"}, "last_name": {"type": "string", "title": "Last Name"}, "work_email": {"type": "string", "title": "Work Email"}, "work_title": {"type": "string", "title": "Work Title"}, "company_name": {"type": "string", "title": "Company Name"}, "reason": {"type": "string", "title": "Reason"}, "current_product_id": {"type": "string", "format": "uuid", "title": "Current Product Id"}, "requested_price_id": {"type": "string", "format": "uuid", "title": "Requested Price Id"}}, "type": "object", "required": ["first_name", "last_name", "work_email", "work_title", "company_name", "reason", "current_product_id", "requested_price_id"], "title": "PlanChangeRequestCreate"}, "PlanChangeRequestResponse": {"properties": {"id": {"type": "string", "format": "uuid", "title": "Id"}, "first_name": {"type": "string", "title": "First Name"}, "last_name": {"type": "string", "title": "Last Name"}, "work_email": {"type": "string", "title": "Work Email"}, "work_title": {"type": "string", "title": "Work Title"}, "company_name": {"type": "string", "title": "Company Name"}, "reason": {"type": "string", "title": "Reason"}, "status": {"type": "string", "title": "Status"}, "customer_id": {"type": "string", "format": "uuid", "title": "Customer Id"}, "current_product_id": {"type": "string", "format": "uuid", "title": "Current Product Id"}, "requested_product_id": {"type": "string", "format": "uuid", "title": "Requested Product Id"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}, "updated_at": {"type": "string", "format": "date-time", "title": "Updated At"}}, "type": "object", "required": ["id", "first_name", "last_name", "work_email", "work_title", "company_name", "reason", "status", "customer_id", "current_product_id", "requested_product_id", "created_at", "updated_at"], "title": "PlanChangeRequestResponse"}, "PresignedUrlInfo": {"properties": {"file_id": {"type": "string", "title": "File Id", "description": "Client-side ID for tracking this file"}, "filename": {"type": "string", "title": "Filename", "description": "Original filename"}, "storage_key": {"type": "string", "title": "Storage Key", "description": "Storage key for the file"}, "presigned_url": {"type": "string", "title": "Presigned Url", "description": "Presigned URL for file upload"}}, "type": "object", "required": ["file_id", "filename", "storage_key", "presigned_url"], "title": "PresignedUrlInfo", "description": "Information about a generated presigned URL"}, "PresignedUrlRequest": {"properties": {"kb_id": {"type": "string", "title": "Kb Id", "description": "ID of the knowledge base to upload files to"}, "files": {"items": {"$ref": "#/components/schemas/FileInfo"}, "type": "array", "title": "Files", "description": "Information about files to generate presigned URLs for"}}, "type": "object", "required": ["kb_id", "files"], "title": "PresignedUrlRequest", "description": "Request to generate presigned URLs for file uploads"}, "PresignedUrlResponse": {"properties": {"kb_id": {"type": "string", "title": "Kb Id", "description": "Knowledge base ID"}, "presigned_urls": {"items": {"$ref": "#/components/schemas/PresignedUrlInfo"}, "type": "array", "title": "Presigned Urls", "description": "Generated presigned URLs"}}, "type": "object", "required": ["kb_id", "presigned_urls"], "title": "PresignedUrlResponse", "description": "Response with presigned URLs for file uploads"}, "PriceResponse": {"properties": {"id": {"type": "string", "format": "uuid", "title": "Id"}, "stripe_price_id": {"type": "string", "title": "Stripe Price Id"}, "product_id": {"type": "string", "format": "uuid", "title": "Product Id"}, "active": {"type": "boolean", "title": "Active"}, "amount": {"type": "number", "title": "Amount"}, "currency": {"type": "string", "title": "<PERSON><PERSON><PERSON><PERSON>"}, "interval": {"type": "string", "title": "Interval"}}, "type": "object", "required": ["id", "stripe_price_id", "product_id", "active", "amount", "currency", "interval"], "title": "PriceResponse"}, "ProductResponse": {"properties": {"id": {"type": "string", "format": "uuid", "title": "Id"}, "name": {"type": "string", "title": "Name"}, "description": {"type": "string", "title": "Description"}, "stripe_product_id": {"type": "string", "title": "Stripe Product Id"}, "active": {"type": "boolean", "title": "Active"}, "prices": {"anyOf": [{"items": {"$ref": "#/components/schemas/PriceResponse"}, "type": "array"}, {"type": "null"}], "title": "Prices", "default": []}, "quota_definition": {"anyOf": [{"$ref": "#/components/schemas/QuotaDefinitionResponse"}, {"type": "null"}]}, "is_custom": {"type": "boolean", "title": "Is Custom"}}, "type": "object", "required": ["id", "name", "description", "stripe_product_id", "active", "is_custom"], "title": "ProductResponse"}, "QuotaDefinitionResponse": {"properties": {"max_workspaces": {"type": "integer", "title": "Max Workspaces"}, "max_members_per_workspace": {"type": "integer", "title": "Max Members Per Workspace"}, "max_fast_requests_per_month": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Max Fast Requests Per Month"}}, "type": "object", "required": ["max_workspaces", "max_members_per_workspace", "max_fast_requests_per_month"], "title": "QuotaDefinitionResponse"}, "QuotaInfo": {"properties": {"quota_used": {"type": "integer", "title": "Quota Used"}, "quota_limit": {"type": "integer", "title": "<PERSON><PERSON><PERSON>"}, "quota_remaining": {"type": "integer", "title": "<PERSON><PERSON><PERSON>"}, "usage_percentage": {"type": "number", "title": "Usage Percentage"}}, "type": "object", "required": ["quota_used", "quota_limit", "quota_remaining", "usage_percentage"], "title": "QuotaInfo"}, "RecommendationCreate": {"properties": {"type": {"$ref": "#/components/schemas/RecommendationType"}, "title": {"type": "string", "maxLength": 255, "title": "Title"}, "description": {"type": "string", "title": "Description"}, "potential_savings": {"type": "number", "title": "Potential Savings"}, "effort": {"type": "string", "maxLength": 50, "title": "<PERSON><PERSON><PERSON>"}, "risk": {"type": "string", "maxLength": 50, "title": "Risk"}, "status": {"$ref": "#/components/schemas/RecommendationStatus", "default": "pending"}, "resource_id": {"type": "string", "format": "uuid", "title": "Resource Id"}}, "type": "object", "required": ["type", "title", "description", "potential_savings", "effort", "risk", "resource_id"], "title": "RecommendationCreate"}, "RecommendationOveralPublic": {"properties": {"total_resource_scanned": {"type": "integer", "title": "Total Resource Scanned"}, "total_well_optimized": {"type": "integer", "title": "Total Well Optimized"}, "total_optimization_opportunities": {"type": "integer", "title": "Total Optimization Opportunities"}, "total_estimated_saving_amount": {"type": "number", "title": "Total Estimated Saving Amount"}}, "type": "object", "required": ["total_resource_scanned", "total_well_optimized", "total_optimization_opportunities", "total_estimated_saving_amount"], "title": "RecommendationOveralPublic"}, "RecommendationPublic": {"properties": {"type": {"$ref": "#/components/schemas/RecommendationType"}, "title": {"type": "string", "maxLength": 255, "title": "Title"}, "description": {"type": "string", "title": "Description"}, "potential_savings": {"type": "number", "title": "Potential Savings"}, "effort": {"type": "string", "maxLength": 50, "title": "<PERSON><PERSON><PERSON>"}, "risk": {"type": "string", "maxLength": 50, "title": "Risk"}, "status": {"$ref": "#/components/schemas/RecommendationStatus", "default": "pending"}, "id": {"type": "string", "format": "uuid", "title": "Id"}, "resource_id": {"type": "string", "format": "uuid", "title": "Resource Id"}, "resource": {"$ref": "#/components/schemas/ResourcePublic"}}, "type": "object", "required": ["type", "title", "description", "potential_savings", "effort", "risk", "id", "resource_id", "resource"], "title": "RecommendationPublic"}, "RecommendationStatus": {"type": "string", "enum": ["pending", "implemented", "ignored", "in_progress"], "title": "RecommendationStatus"}, "RecommendationType": {"type": "string", "enum": ["instance_rightsizing", "autoscaling_optimization", "auto_start_stop_optimization", "volume_optimization", "snapshot_cleanup", "reserved_instance_recommendation", "savings_plan_recommendation", "spot_instance_usage", "idle_resource_cleanup", "unused_eip_cleanup", "orphaned_snapshot_cleanup", "underutilized_ebs_cleanup", "serverless_migration", "container_adoption", "multi_az_optimization", "data_transfer_optimization", "cloudfront_optimization", "nat_gateway_optimization", "rds_optimization", "redshift_optimization", "dynamodb_optimization", "s3_storage_class_optimization", "lambda_optimization", "tagging_improvement", "cost_allocation_improvement", "cost_anomaly_detection", "budget_alert_setup", "cost_explorer_usage", "modernize_legacy_services", "migrate_to_graviton", "compliance_optimization", "governance_improvement", "cross_region_optimization", "cross_account_optimization", "predictive_scaling", "ai_driven_optimization", "quantum_computing_readiness", "carbon_footprint_reduction", "renewable_energy_usage", "marketplace_alternative", "third_party_tool_recommendation", "custom_optimization", "other", "ec2_fleet_optimization", "spot_fleet_optimization", "graviton_migration", "predictive_scaling_optimization", "instance_connect_endpoint"], "title": "RecommendationType"}, "RecommendationUpdate": {"properties": {"type": {"anyOf": [{"$ref": "#/components/schemas/RecommendationType"}, {"type": "null"}]}, "title": {"anyOf": [{"type": "string", "maxLength": 255}, {"type": "null"}], "title": "Title"}, "description": {"anyOf": [{"type": "string", "maxLength": 1000}, {"type": "null"}], "title": "Description"}, "potential_savings": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Potential Savings"}, "effort": {"anyOf": [{"type": "string", "maxLength": 50}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON>"}, "risk": {"anyOf": [{"type": "string", "maxLength": 50}, {"type": "null"}], "title": "Risk"}, "status": {"anyOf": [{"$ref": "#/components/schemas/RecommendationStatus"}, {"type": "null"}]}}, "type": "object", "title": "RecommendationUpdate"}, "RecommendationsPublic": {"properties": {"data": {"items": {"$ref": "#/components/schemas/RecommendationPublic"}, "type": "array", "title": "Data"}, "count": {"type": "integer", "title": "Count"}}, "type": "object", "required": ["data", "count"], "title": "RecommendationsPublic"}, "ResendActivationRequest": {"properties": {"email": {"type": "string", "format": "email", "title": "Email"}, "captcha_token": {"type": "string", "title": "<PERSON><PERSON>"}}, "type": "object", "required": ["email", "captcha_token"], "title": "ResendActivationRequest"}, "ResourceCreate": {"properties": {"name": {"type": "string", "maxLength": 255, "minLength": 1, "title": "Name"}, "arn": {"type": "string", "maxLength": 2048, "title": "<PERSON><PERSON>"}, "tags": {"type": "object", "title": "Tags", "default": {}}, "configurations": {"type": "object", "title": "Configurations", "default": {}}, "description": {"anyOf": [{"type": "string", "maxLength": 1000}, {"type": "null"}], "title": "Description"}, "type": {"type": "string", "title": "Type"}, "region": {"type": "string", "title": "Region"}, "status": {"$ref": "#/components/schemas/ResourceStatus", "default": "found"}, "workspace_id": {"type": "string", "format": "uuid", "title": "Workspace Id"}}, "type": "object", "required": ["name", "arn", "type", "region", "workspace_id"], "title": "ResourceCreate"}, "ResourcePublic": {"properties": {"name": {"type": "string", "maxLength": 255, "minLength": 1, "title": "Name"}, "arn": {"type": "string", "maxLength": 2048, "title": "<PERSON><PERSON>"}, "tags": {"type": "object", "title": "Tags", "default": {}}, "configurations": {"type": "object", "title": "Configurations", "default": {}}, "description": {"anyOf": [{"type": "string", "maxLength": 1000}, {"type": "null"}], "title": "Description"}, "type": {"type": "string", "title": "Type"}, "region": {"type": "string", "title": "Region"}, "status": {"$ref": "#/components/schemas/ResourceStatus", "default": "found"}, "id": {"type": "string", "format": "uuid", "title": "Id"}, "workspace": {"$ref": "#/components/schemas/WorkspacePublic"}, "total_recommendation": {"type": "integer", "title": "Total Recommendation"}, "total_potential_saving": {"type": "number", "title": "Total Potential Saving"}, "updated_at": {"type": "string", "format": "date-time", "title": "Updated At"}}, "type": "object", "required": ["name", "arn", "type", "region", "id", "workspace", "total_recommendation", "total_potential_saving", "updated_at"], "title": "ResourcePublic"}, "ResourceRead": {"properties": {"name": {"type": "string", "maxLength": 255, "minLength": 1, "title": "Name"}, "arn": {"type": "string", "maxLength": 2048, "title": "<PERSON><PERSON>"}, "tags": {"additionalProperties": {"type": "string"}, "type": "object", "title": "Tags"}, "configurations": {"type": "object", "title": "Configurations"}, "description": {"anyOf": [{"type": "string", "maxLength": 1000}, {"type": "null"}], "title": "Description"}, "type": {"type": "string", "title": "Type"}, "region": {"type": "string", "title": "Region"}, "status": {"$ref": "#/components/schemas/ResourceStatus", "default": "found"}, "recommendations": {"items": {"$ref": "#/components/schemas/RecommendationPublic"}, "type": "array", "title": "Recommendations"}, "metrics": {"anyOf": [{"additionalProperties": {"items": {"$ref": "#/components/schemas/MetricRead"}, "type": "array"}, "type": "object"}, {"type": "null"}], "title": "Metrics"}}, "type": "object", "required": ["name", "arn", "tags", "configurations", "type", "region", "recommendations"], "title": "ResourceRead"}, "ResourceSavingsReport": {"properties": {"rds_savings": {"items": {"$ref": "#/components/schemas/ChartDataPoint"}, "type": "array", "title": "Rds Savings"}, "ec2_savings": {"items": {"$ref": "#/components/schemas/ChartDataPoint"}, "type": "array", "title": "Ec2 Savings"}, "total_rds_savings": {"type": "number", "title": "Total Rds Savings"}, "total_ec2_savings": {"type": "number", "title": "Total Ec2 Savings"}}, "type": "object", "required": ["rds_savings", "ec2_savings", "total_rds_savings", "total_ec2_savings"], "title": "ResourceSavingsReport"}, "ResourceStatus": {"type": "string", "enum": ["stopped", "starting", "running", "found", "deleted"], "title": "ResourceStatus"}, "ResourceType": {"type": "string", "enum": ["EC2", "LAMBDA", "ECS", "EKS", "BATCH", "EC2_AUTO_SCALING", "ELASTIC_BEANSTALK", "APP_RUNNER", "RDS", "DYNAMODB", "ELASTICACHE", "NEPTUNE", "DOCUMENTDB", "OPENSEARCH", "REDSHIFT", "S3", "EBS", "EFS", "BACKUP", "VPC", "ELB", "CLOUDFORMATION", "CLOUDWATCH", "SQS", "SNS"], "title": "ResourceType"}, "ResourceUpdate": {"properties": {"name": {"anyOf": [{"type": "string", "maxLength": 255, "minLength": 1}, {"type": "null"}], "title": "Name"}, "arn": {"anyOf": [{"type": "string", "maxLength": 2048}, {"type": "null"}], "title": "<PERSON><PERSON>"}, "tags": {"type": "object", "title": "Tags", "default": {}}, "configurations": {"type": "object", "title": "Configurations", "default": {}}, "description": {"anyOf": [{"type": "string", "maxLength": 1000}, {"type": "null"}], "title": "Description"}, "type": {"type": "string", "maxLength": 50, "title": "Type"}, "region": {"type": "string", "maxLength": 50, "title": "Region"}, "status": {"$ref": "#/components/schemas/ResourceStatus", "default": "found"}}, "type": "object", "required": ["type", "region"], "title": "ResourceUpdate"}, "ResourcesPublic": {"properties": {"data": {"items": {"$ref": "#/components/schemas/ResourcePublic"}, "type": "array", "title": "Data"}, "count": {"type": "integer", "title": "Count"}}, "type": "object", "required": ["data", "count"], "title": "ResourcesPublic"}, "RetrieverConfig": {"properties": {"numberOfResults": {"type": "integer", "title": "Numberofresults", "default": 4}, "overrideSearchType": {"anyOf": [{"type": "string", "enum": ["HYBRID", "SEMANTIC"]}, {"type": "null"}], "title": "Overridesearchtype", "default": "SEMANTIC"}}, "type": "object", "title": "RetrieverConfig"}, "RunModeEnum": {"type": "string", "enum": ["autonomous", "agent"], "title": "RunModeEnum"}, "SavingSummaryReport": {"properties": {"potential_savings": {"type": "number", "title": "Potential Savings"}, "potential_savings_percentage_change": {"type": "number", "title": "Potential Savings Percentage Change"}, "save_opportunities": {"type": "number", "title": "Save Opportunities"}, "save_opportunities_percentage_change": {"type": "number", "title": "Save Opportunities Percentage Change"}, "total_saved": {"type": "number", "title": "Total Saved"}, "total_saved_percentage_change": {"type": "number", "title": "Total Saved Percentage Change"}, "active_saving": {"type": "number", "title": "Active Saving"}, "active_saving_percentage_change": {"type": "number", "title": "Active Saving Percentage Change"}}, "type": "object", "required": ["potential_savings", "potential_savings_percentage_change", "save_opportunities", "save_opportunities_percentage_change", "total_saved", "total_saved_percentage_change", "active_saving", "active_saving_percentage_change"], "title": "SavingSummaryReport"}, "ScriptExecutionResponse": {"properties": {"status": {"type": "string", "title": "Status"}, "result": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Result"}}, "type": "object", "required": ["status"], "title": "ScriptExecutionResponse"}, "SearchResponse": {"properties": {"query": {"type": "string", "title": "Query"}, "results": {"items": {"$ref": "#/components/schemas/Document"}, "type": "array", "title": "Results"}, "total_found": {"type": "integer", "title": "Total Found"}, "execution_time": {"type": "number", "title": "Execution Time"}}, "type": "object", "required": ["query", "results", "total_found", "execution_time"], "title": "SearchResponse"}, "ServiceSavingsData": {"properties": {"service": {"type": "string", "title": "Service"}, "savings": {"type": "number", "title": "Savings"}, "percentage": {"type": "number", "title": "Percentage"}}, "type": "object", "required": ["service", "savings", "percentage"], "title": "ServiceSavingsData"}, "ServiceSavingsReport": {"properties": {"data": {"items": {"$ref": "#/components/schemas/ServiceSavingsData"}, "type": "array", "title": "Data"}, "total_savings": {"type": "number", "title": "Total Savings"}}, "type": "object", "required": ["data", "total_savings"], "title": "ServiceSavingsReport"}, "Setting": {"properties": {"id": {"type": "string", "format": "uuid", "title": "Id"}, "provider_name": {"$ref": "#/components/schemas/CloudProvider", "default": "AWS"}, "regions": {"items": {"type": "string"}, "type": "array", "title": "Regions", "default": []}, "types": {"items": {"type": "string"}, "type": "array", "title": "Types", "default": []}, "cron_patterns": {"items": {"type": "string"}, "type": "array", "title": "<PERSON><PERSON>", "default": []}}, "type": "object", "title": "Setting"}, "ShareResponse": {"properties": {"share_id": {"type": "string", "format": "uuid", "title": "Share Id"}, "is_shared": {"type": "boolean", "title": "Is Shared"}, "shared_at": {"type": "string", "format": "date-time", "title": "Shared At"}, "shared_by": {"type": "string", "format": "uuid", "title": "Shared By"}}, "type": "object", "required": ["share_id", "is_shared", "shared_at", "shared_by"], "title": "ShareResponse"}, "StreamResponse": {"properties": {"type": {"type": "string", "title": "Type"}, "content": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Content"}, "message_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Message Id"}}, "type": "object", "required": ["type"], "title": "StreamResponse"}, "SubscriptionStatus": {"properties": {"id": {"type": "string", "title": "Id"}, "customer_id": {"type": "string", "title": "Customer Id"}, "status": {"type": "string", "title": "Status"}, "current_period_end": {"type": "string", "format": "date-time", "title": "Current Period End"}, "cancel_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Cancel At"}, "product_name": {"type": "string", "title": "Product Name"}, "product_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Product Id"}, "price_amount": {"type": "number", "title": "Price Amount"}, "price_currency": {"type": "string", "title": "Price Currency"}, "price_interval": {"type": "string", "title": "Price Interval"}}, "type": "object", "required": ["id", "customer_id", "status", "current_period_end", "product_name", "price_amount", "price_currency", "price_interval"], "title": "SubscriptionStatus"}, "SummaryResponse": {"properties": {"query": {"type": "string", "title": "Query"}, "summary": {"type": "string", "title": "Summary"}, "sources": {"items": {}, "type": "array", "title": "Sources"}, "citations": {"items": {"$ref": "#/components/schemas/CitationMetadata"}, "type": "array", "title": "Citations"}, "execution_time": {"type": "number", "title": "Execution Time"}}, "type": "object", "required": ["query", "summary", "sources", "citations", "execution_time"], "title": "SummaryResponse"}, "TaskCategoryEnum": {"type": "string", "enum": ["COST_OPTIMIZE", "OPERATIONAL", "SCALABILITY", "SECURITY", "OPERATIONAL_EFFICIENCY", "OTHER"], "title": "TaskCategoryEnum", "description": "Enumeration of possible task categories."}, "TaskCouldEnum": {"type": "string", "enum": ["AWS", "AZURE", "GCP", "ALL"], "title": "TaskCouldEnum"}, "TaskCreate": {"properties": {"title": {"type": "string", "maxLength": 255, "minLength": 1, "title": "Title"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description", "default": ""}, "priority": {"$ref": "#/components/schemas/TaskPriority", "default": 0}, "tags": {"items": {"type": "string"}, "type": "array", "title": "Tags"}, "schedule": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Schedule"}, "agent_config": {"type": "object", "title": "Agent Config"}}, "type": "object", "required": ["title"], "title": "TaskCreate", "description": "<PERSON><PERSON><PERSON> for creating a new task."}, "TaskDeleteResponse": {"properties": {"status": {"type": "string", "title": "Status", "default": "success"}}, "type": "object", "title": "TaskDeleteResponse", "description": "Schema for task delete response."}, "TaskExecutionStatus": {"type": "string", "enum": ["running", "succeeded", "failed", "cancelled", "required_approval"], "title": "TaskExecutionStatus", "description": "Enumeration of execution statuses for task.\n\nAttributes:\n    RUNNING: Currently executing\n    SUCCEEDED: Successfully completed\n    FAILED: Execution failed\n    CANCELLED: Execution cancelled\n    REQUIRED_APPROVAL: Execution requires approval"}, "TaskHistory": {"properties": {"id": {"type": "string", "format": "uuid", "title": "Id"}, "task_id": {"type": "string", "format": "uuid", "title": "Task Id"}, "conversation_id": {"type": "string", "format": "uuid", "title": "Conversation Id"}, "status": {"$ref": "#/components/schemas/TaskExecutionStatus", "description": "Current task status"}, "message": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Message", "description": "Message from the task execution: error or required action"}, "celery_task_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Celery Task Id", "description": "Celery task ID associated with the task"}, "start_time": {"type": "string", "format": "date-time", "title": "Start Time", "description": "Timestamp when the task history was started"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time", "description": "Timestamp when the task history was ended"}, "run_time": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Run Time", "description": "Time taken to run the task"}}, "type": "object", "required": ["task_id", "conversation_id", "status"], "title": "TaskHistory", "description": "Execution history of a task conversation."}, "TaskList": {"properties": {"data": {"items": {"$ref": "#/components/schemas/TaskResponse"}, "type": "array", "title": "Data"}, "total": {"type": "integer", "title": "Total", "default": 0}}, "type": "object", "title": "TaskList", "description": "Schema for paginated task list."}, "TaskPriority": {"type": "integer", "enum": [0, 1, 2, 3], "title": "TaskPriority", "description": "Enumeration of task priority levels.\n\nAttributes:\n    LOW (0): Regular priority, no urgency\n    MEDIUM (1): Moderate priority, should be done soon\n    HIGH (2): High priority, urgent attention needed\n    CRITICAL (3): Critical priority, requires immediate attention"}, "TaskResponse": {"properties": {"title": {"type": "string", "maxLength": 255, "minLength": 1, "title": "Title"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description", "default": ""}, "priority": {"$ref": "#/components/schemas/TaskPriority", "default": 0}, "tags": {"items": {"type": "string"}, "type": "array", "title": "Tags"}, "id": {"type": "string", "format": "uuid", "title": "Id"}, "workspace_id": {"type": "string", "format": "uuid", "title": "Workspace Id"}, "owner_id": {"type": "string", "format": "uuid", "title": "Owner Id"}, "scheduled_status": {"anyOf": [{"$ref": "#/components/schemas/TaskScheduledStatus"}, {"type": "null"}]}, "execution_status": {"anyOf": [{"$ref": "#/components/schemas/TaskExecutionStatus"}, {"type": "null"}]}, "error": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Error"}, "last_run": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Last Run"}, "next_run": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Next Run"}, "schedule": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Schedule"}, "agent_config": {"type": "object", "title": "Agent Config"}, "enable": {"type": "boolean", "title": "Enable", "default": true}, "task_history": {"items": {"$ref": "#/components/schemas/TaskHistory"}, "type": "array", "title": "Task History"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}, "updated_at": {"type": "string", "format": "date-time", "title": "Updated At"}, "created_by": {"type": "string", "format": "uuid", "title": "Created By"}, "updated_by": {"type": "string", "format": "uuid", "title": "Updated By"}}, "type": "object", "required": ["title", "id", "workspace_id", "owner_id", "created_at", "updated_at", "created_by", "updated_by"], "title": "TaskResponse", "description": "<PERSON><PERSON><PERSON> for task response."}, "TaskScheduledStatus": {"type": "string", "enum": ["pending", "scheduled"], "title": "TaskScheduledStatus", "description": "Enumeration of scheduled statuses for task."}, "TaskServiceEnum": {"type": "string", "enum": ["ALL", "OTHER", "COMPUTE", "STORAGE", "SERVERLESS", "DATABASE", "NETWORK", "MESSAGING", "MANAGEMENT", "BILLING", "CROSS_SERVICE", "MONITORING", "STREAMING", "SECURITY"], "title": "TaskServiceEnum", "description": "Enumeration of possible task services."}, "TaskStatusResponse": {"properties": {"task_id": {"type": "string", "title": "Task Id", "description": "Celery task ID"}, "status": {"$ref": "#/components/schemas/AsyncTaskStatus", "description": "Task status (PENDING, PROGRESS, SUCCESS, FAILURE)"}, "progress": {"type": "integer", "title": "Progress", "description": "Progress percentage (0-100)", "default": 0}, "result": {"anyOf": [{"type": "object"}, {"type": "null"}], "title": "Result", "description": "Task result if completed"}, "error": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Error", "description": "Error message if failed"}, "status_message": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Status Message", "description": "Human-readable status message"}}, "type": "object", "required": ["task_id", "status"], "title": "TaskStatusResponse", "description": "Response schema for task status operations"}, "TaskStopResponse": {"properties": {"task_id": {"type": "string", "format": "uuid", "title": "Task Id"}, "conversation_id": {"type": "string", "format": "uuid", "title": "Conversation Id"}, "status": {"type": "string", "title": "Status"}}, "type": "object", "required": ["task_id", "conversation_id", "status"], "title": "TaskStopResponse", "description": "<PERSON><PERSON><PERSON> for task stop response."}, "TaskTemplateCreate": {"properties": {"task": {"type": "string", "title": "Task"}, "category": {"anyOf": [{"$ref": "#/components/schemas/TaskCategoryEnum"}, {"type": "null"}], "default": "OTHER"}, "service": {"anyOf": [{"$ref": "#/components/schemas/TaskServiceEnum"}, {"type": "null"}], "default": "OTHER"}, "service_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Service Name", "default": ""}, "cloud": {"$ref": "#/components/schemas/TaskCouldEnum"}, "run_mode": {"$ref": "#/components/schemas/RunModeEnum"}, "schedule": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Schedule"}, "context": {"type": "string", "title": "Context"}}, "type": "object", "required": ["task", "cloud", "run_mode", "context"], "title": "TaskTemplateCreate"}, "TaskTemplateList": {"properties": {"data": {"items": {"$ref": "#/components/schemas/TaskTemplateResponse"}, "type": "array", "title": "Data"}, "total": {"type": "integer", "title": "Total"}}, "type": "object", "required": ["data", "total"], "title": "TaskTemplateList"}, "TaskTemplateResponse": {"properties": {"id": {"type": "string", "format": "uuid", "title": "Id"}, "task": {"type": "string", "title": "Task"}, "category": {"$ref": "#/components/schemas/TaskCategoryEnum"}, "service": {"$ref": "#/components/schemas/TaskServiceEnum"}, "service_name": {"type": "string", "title": "Service Name"}, "cloud": {"$ref": "#/components/schemas/TaskCouldEnum"}, "run_mode": {"$ref": "#/components/schemas/RunModeEnum"}, "schedule": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Schedule"}, "context": {"type": "string", "title": "Context"}, "is_default": {"type": "boolean", "title": "<PERSON>"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}, "updated_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Updated At"}}, "type": "object", "required": ["id", "task", "category", "service", "service_name", "cloud", "run_mode", "schedule", "context", "is_default", "created_at", "updated_at"], "title": "TaskTemplateResponse"}, "TaskTemplateUpdate": {"properties": {"task": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Task"}, "category": {"anyOf": [{"$ref": "#/components/schemas/TaskCategoryEnum"}, {"type": "null"}]}, "service": {"anyOf": [{"$ref": "#/components/schemas/TaskServiceEnum"}, {"type": "null"}]}, "run_mode": {"anyOf": [{"$ref": "#/components/schemas/RunModeEnum"}, {"type": "null"}]}, "schedule": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Schedule"}, "context": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Context"}}, "type": "object", "title": "TaskTemplateUpdate"}, "TaskUpdate": {"properties": {"title": {"anyOf": [{"type": "string", "maxLength": 255, "minLength": 1}, {"type": "null"}], "title": "Title"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description", "default": ""}, "priority": {"anyOf": [{"$ref": "#/components/schemas/TaskPriority"}, {"type": "null"}]}, "tags": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Tags"}, "schedule": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Schedule"}, "agent_config": {"type": "object", "title": "Agent Config"}}, "type": "object", "title": "TaskUpdate", "description": "<PERSON><PERSON><PERSON> for updating an existing task."}, "Token": {"properties": {"access_token": {"type": "string", "title": "Access Token"}, "token_type": {"type": "string", "title": "Token Type", "default": "bearer"}, "workspace_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Workspace Id"}, "is_first_login": {"type": "boolean", "title": "Is First Login", "default": false}, "slack_oauth": {"type": "boolean", "title": "<PERSON><PERSON><PERSON>", "default": false}, "app_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "App Id"}, "team_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Team Id"}}, "type": "object", "required": ["access_token"], "title": "Token"}, "TokenDistributionCategory": {"properties": {"category": {"type": "string", "title": "Category"}, "percentage": {"type": "number", "title": "Percentage"}}, "type": "object", "required": ["category", "percentage"], "title": "TokenDistributionCategory"}, "TokenUsageCreate": {"properties": {"message_id": {"type": "string", "format": "uuid", "title": "Message ID", "description": "Unique identifier of the associated message"}, "input_tokens": {"type": "integer", "minimum": 0, "title": "Input Tokens", "description": "Number of tokens in the input text", "example": 100}, "output_tokens": {"type": "integer", "minimum": 0, "title": "Output Tokens", "description": "Number of tokens in the output text", "example": 150}, "model_id": {"type": "string", "title": "Model ID", "description": "Identifier of the AI model used", "example": "gpt-4"}}, "type": "object", "required": ["message_id", "input_tokens", "output_tokens", "model_id"], "title": "TokenUsageCreate"}, "TokenUsageResponse": {"properties": {"id": {"type": "string", "format": "uuid", "title": "ID", "description": "Unique identifier for the usage record"}, "message_id": {"type": "string", "format": "uuid", "title": "Message ID", "description": "ID of the associated message"}, "input_tokens": {"type": "integer", "minimum": 0, "title": "Input Tokens", "description": "Number of tokens in the input text"}, "output_tokens": {"type": "integer", "minimum": 0, "title": "Output Tokens", "description": "Number of tokens in the output text"}, "model_id": {"type": "string", "title": "Model ID", "description": "Identifier of the AI model used"}, "workspace_id": {"type": "string", "format": "uuid", "title": "Workspace ID", "description": "ID of the workspace"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At", "description": "Timestamp of record creation"}, "updated_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Updated At", "description": "Timestamp of last update"}, "total_tokens": {"type": "integer", "title": "Total Tokens", "description": "Calculate total tokens from input and output tokens.", "readOnly": true}}, "type": "object", "required": ["id", "message_id", "input_tokens", "output_tokens", "model_id", "workspace_id", "created_at", "total_tokens"], "title": "TokenUsageResponse", "description": "Schema for token usage response.\n\nAttributes:\n    id: Unique identifier for the usage record\n    message_id: ID of the associated message\n    input_tokens: Number of tokens in input text\n    output_tokens: Number of tokens in output text\n    model_id: ID of the AI model used\n    total_tokens: Total number of tokens used\n    created_at: Timestamp of record creation"}, "ToolPermissionRequest": {"properties": {"permission": {"type": "string", "title": "Permission"}}, "type": "object", "required": ["permission"], "title": "ToolPermissionRequest"}, "TopSavingsReport": {"properties": {"data": {"items": {"$ref": "#/components/schemas/RecommendationPublic"}, "type": "array", "title": "Data"}}, "type": "object", "required": ["data"], "title": "TopSavingsReport"}, "URLsUploadRequest": {"properties": {"urls": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Urls", "description": "URLs to crawl (required if source_type is website)"}, "deep_crawls": {"anyOf": [{"items": {"type": "boolean"}, "type": "array"}, {"type": "null"}], "title": "Deep Crawls", "description": "Whether to enable deep crawling for each URL"}}, "type": "object", "title": "URLsUploadRequest"}, "UpdatePassword": {"properties": {"current_password": {"type": "string", "maxLength": 40, "minLength": 8, "title": "Current Password"}, "new_password": {"type": "string", "maxLength": 40, "minLength": 8, "title": "New Password"}}, "type": "object", "required": ["current_password", "new_password"], "title": "UpdatePassword"}, "UploadCreate": {"properties": {"filename": {"type": "string", "title": "Filename"}, "file_size": {"type": "integer", "title": "File Size"}, "file_type": {"type": "string", "title": "File Type"}}, "type": "object", "required": ["filename", "file_size", "file_type"], "title": "UploadCreate"}, "UploadPublic": {"properties": {"filename": {"type": "string", "title": "Filename"}, "file_size": {"type": "integer", "title": "File Size"}, "file_type": {"type": "string", "title": "File Type"}, "id": {"type": "string", "format": "uuid", "title": "Id"}, "status": {"$ref": "#/components/schemas/UploadStatus"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}}, "type": "object", "required": ["filename", "file_size", "file_type", "id", "status", "created_at"], "title": "UploadPublic"}, "UploadResponse": {"properties": {"id": {"type": "string", "format": "uuid", "title": "Id"}, "upload_url": {"type": "string", "title": "Upload Url"}, "expires_in": {"type": "integer", "title": "Expires In"}}, "type": "object", "required": ["id", "upload_url", "expires_in"], "title": "UploadResponse"}, "UploadStatus": {"type": "string", "enum": ["pending", "in_progress", "completed", "failed"], "title": "UploadStatus"}, "UploadedFileInfo": {"properties": {"file_id": {"type": "string", "title": "File Id", "description": "Client-side ID for tracking this file"}, "filename": {"type": "string", "title": "Filename", "description": "Original filename"}, "storage_key": {"type": "string", "title": "Storage Key", "description": "Storage key for the uploaded file"}, "content_type": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Content Type", "description": "File MIME type"}, "file_size": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "File Size", "description": "File size in bytes"}}, "type": "object", "required": ["file_id", "filename", "storage_key"], "title": "UploadedFileInfo", "description": "Information about a successfully uploaded file"}, "UsageQuotaResponse": {"properties": {"id": {"type": "string", "format": "uuid", "title": "ID"}, "user_id": {"type": "string", "format": "uuid", "title": "User ID"}, "quota_used_messages": {"type": "integer", "title": "Quota Used Messages"}, "quota_used_tokens": {"type": "integer", "title": "Quota Used Tokens"}, "reset_at": {"type": "string", "format": "date-time", "title": "Reset At"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}, "updated_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Updated At"}}, "type": "object", "required": ["id", "user_id", "quota_used_messages", "quota_used_tokens", "reset_at", "created_at"], "title": "UsageQuotaResponse", "description": "Response schema for usage quota information."}, "UsageStatistics": {"properties": {"input_tokens": {"type": "integer", "minimum": 0, "title": "Input Tokens"}, "output_tokens": {"type": "integer", "minimum": 0, "title": "Output Tokens"}, "total_tokens": {"type": "integer", "minimum": 0, "title": "Total Tokens"}, "quota_limit": {"type": "integer", "minimum": 0, "title": "<PERSON><PERSON><PERSON>"}, "quota_used": {"type": "integer", "minimum": 0, "title": "Quota Used"}, "quota_remaining": {"type": "integer", "minimum": 0, "title": "<PERSON><PERSON><PERSON>"}, "usage_percentage": {"type": "number", "minimum": 0, "title": "Usage Percentage"}, "daily_token_usage": {"items": {"$ref": "#/components/schemas/DailyTokenUsage"}, "type": "array", "title": "Daily Token Usage"}, "agent_type_stats": {"items": {"$ref": "#/components/schemas/AgentTypeUsage"}, "type": "array", "title": "Agent Type Stats"}}, "type": "object", "required": ["input_tokens", "output_tokens", "total_tokens", "quota_limit", "quota_used", "quota_remaining", "usage_percentage", "daily_token_usage", "agent_type_stats"], "title": "UsageStatistics", "description": "Response schema for usage statistics."}, "UserCreate": {"properties": {"email": {"type": "string", "maxLength": 255, "format": "email", "title": "Email"}, "is_active": {"type": "boolean", "title": "Is Active", "default": false}, "is_email_verified": {"type": "boolean", "title": "Is Email Verified", "default": false}, "is_superuser": {"type": "boolean", "title": "Is Superuser", "default": false}, "last_login_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Last Login Time"}, "full_name": {"anyOf": [{"type": "string", "maxLength": 255}, {"type": "null"}], "title": "Full Name"}, "avatar_url": {"anyOf": [{"type": "string", "maxLength": 1024}, {"type": "null"}], "title": "Avatar Url"}, "password": {"type": "string", "maxLength": 40, "minLength": 8, "title": "Password"}}, "type": "object", "required": ["email", "password"], "title": "UserCreate"}, "UserDetail": {"properties": {"email": {"type": "string", "maxLength": 255, "format": "email", "title": "Email"}, "is_active": {"type": "boolean", "title": "Is Active", "default": false}, "is_email_verified": {"type": "boolean", "title": "Is Email Verified", "default": false}, "is_superuser": {"type": "boolean", "title": "Is Superuser", "default": false}, "last_login_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Last Login Time"}, "full_name": {"anyOf": [{"type": "string", "maxLength": 255}, {"type": "null"}], "title": "Full Name"}, "avatar_url": {"anyOf": [{"type": "string", "maxLength": 1024}, {"type": "null"}], "title": "Avatar Url"}, "id": {"type": "string", "format": "uuid", "title": "Id"}, "workspaces": {"anyOf": [{"items": {"$ref": "#/components/schemas/Workspace"}, "type": "array"}, {"type": "null"}], "title": "Workspaces"}, "own_workspaces": {"anyOf": [{"items": {"$ref": "#/components/schemas/Workspace"}, "type": "array"}, {"type": "null"}], "title": "Own Workspaces"}, "items": {"anyOf": [{"items": {"$ref": "#/components/schemas/Item"}, "type": "array"}, {"type": "null"}], "title": "Items"}}, "type": "object", "required": ["email", "id"], "title": "UserDetail"}, "UserPublic": {"properties": {"email": {"type": "string", "maxLength": 255, "format": "email", "title": "Email"}, "is_active": {"type": "boolean", "title": "Is Active", "default": false}, "is_email_verified": {"type": "boolean", "title": "Is Email Verified", "default": false}, "is_superuser": {"type": "boolean", "title": "Is Superuser", "default": false}, "last_login_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Last Login Time"}, "full_name": {"anyOf": [{"type": "string", "maxLength": 255}, {"type": "null"}], "title": "Full Name"}, "avatar_url": {"anyOf": [{"type": "string", "maxLength": 1024}, {"type": "null"}], "title": "Avatar Url"}, "id": {"type": "string", "format": "uuid", "title": "Id"}}, "type": "object", "required": ["email", "id"], "title": "UserPublic"}, "UserRegister": {"properties": {"email": {"type": "string", "maxLength": 255, "format": "email", "title": "Email"}, "password": {"type": "string", "maxLength": 40, "minLength": 8, "title": "Password"}, "full_name": {"anyOf": [{"type": "string", "maxLength": 255}, {"type": "null"}], "title": "Full Name"}}, "type": "object", "required": ["email", "password"], "title": "UserRegister"}, "UserUpdate": {"properties": {"email": {"anyOf": [{"type": "string", "maxLength": 255, "format": "email"}, {"type": "null"}], "title": "Email"}, "is_active": {"type": "boolean", "title": "Is Active", "default": false}, "is_email_verified": {"type": "boolean", "title": "Is Email Verified", "default": false}, "is_superuser": {"type": "boolean", "title": "Is Superuser", "default": false}, "last_login_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Last Login Time"}, "full_name": {"anyOf": [{"type": "string", "maxLength": 255}, {"type": "null"}], "title": "Full Name"}, "avatar_url": {"anyOf": [{"type": "string", "maxLength": 1024}, {"type": "null"}], "title": "Avatar Url"}, "password": {"anyOf": [{"type": "string", "maxLength": 40, "minLength": 8}, {"type": "null"}], "title": "Password"}}, "type": "object", "title": "UserUpdate"}, "UserUpdateMe": {"properties": {"full_name": {"anyOf": [{"type": "string", "maxLength": 255}, {"type": "null"}], "title": "Full Name"}, "email": {"anyOf": [{"type": "string", "maxLength": 255, "format": "email"}, {"type": "null"}], "title": "Email"}, "avatar_url": {"anyOf": [{"type": "string", "maxLength": 1024}, {"type": "null"}], "title": "Avatar Url"}}, "type": "object", "title": "UserUpdateMe"}, "UsersPublic": {"properties": {"data": {"items": {"$ref": "#/components/schemas/UserPublic"}, "type": "array", "title": "Data"}, "count": {"type": "integer", "title": "Count"}}, "type": "object", "required": ["data", "count"], "title": "UsersPublic"}, "ValidationError": {"properties": {"loc": {"items": {"anyOf": [{"type": "string"}, {"type": "integer"}]}, "type": "array", "title": "Location"}, "msg": {"type": "string", "title": "Message"}, "type": {"type": "string", "title": "Error Type"}}, "type": "object", "required": ["loc", "msg", "type"], "title": "ValidationError"}, "WorkflowCreate": {"properties": {"name": {"type": "string", "maxLength": 255, "title": "Name"}, "description": {"anyOf": [{"type": "string", "maxLength": 1000}, {"type": "null"}], "title": "Description"}, "workspace_id": {"type": "string", "format": "uuid", "title": "Workspace Id"}}, "type": "object", "required": ["name", "workspace_id"], "title": "WorkflowCreate"}, "WorkflowNodeCreate": {"properties": {"type": {"$ref": "#/components/schemas/NodeType"}, "name": {"type": "string", "maxLength": 255, "title": "Name"}, "description": {"anyOf": [{"type": "string", "maxLength": 1000}, {"type": "null"}], "title": "Description"}, "position": {"type": "integer", "title": "Position"}, "data": {"type": "object", "title": "Data", "default": {}}, "workflow_id": {"type": "string", "format": "uuid", "title": "Workflow Id"}, "parent_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Parent Id"}}, "type": "object", "required": ["type", "name", "position", "workflow_id"], "title": "WorkflowNodeCreate"}, "WorkflowNodePublic": {"properties": {"type": {"$ref": "#/components/schemas/NodeType"}, "name": {"type": "string", "maxLength": 255, "title": "Name"}, "description": {"anyOf": [{"type": "string", "maxLength": 1000}, {"type": "null"}], "title": "Description"}, "position": {"type": "integer", "title": "Position"}, "data": {"type": "object", "title": "Data", "default": {}}, "id": {"type": "string", "format": "uuid", "title": "Id"}, "status": {"$ref": "#/components/schemas/WorkflowStatus"}}, "type": "object", "required": ["type", "name", "position", "id", "status"], "title": "WorkflowNodePublic"}, "WorkflowNodeUpdate": {"properties": {"name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Name"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description"}, "position": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Position"}}, "type": "object", "title": "WorkflowNodeUpdate"}, "WorkflowPublic": {"properties": {"name": {"type": "string", "maxLength": 255, "title": "Name"}, "description": {"anyOf": [{"type": "string", "maxLength": 1000}, {"type": "null"}], "title": "Description"}, "workspace_id": {"type": "string", "format": "uuid", "title": "Workspace Id"}, "id": {"type": "string", "format": "uuid", "title": "Id"}, "nodes": {"items": {"$ref": "#/components/schemas/WorkflowNodePublic"}, "type": "array", "title": "Nodes"}, "status": {"$ref": "#/components/schemas/WorkflowStatus", "default": "created"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}, "updated_at": {"type": "string", "format": "date-time", "title": "Updated At"}}, "type": "object", "required": ["name", "workspace_id", "id", "nodes"], "title": "WorkflowPublic"}, "WorkflowStatus": {"type": "string", "enum": ["created", "unvalidated", "running", "pending", "completed", "error"], "title": "WorkflowStatus"}, "WorkflowUpdate": {"properties": {"name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Name"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description"}, "workspace_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Workspace Id"}}, "type": "object", "title": "WorkflowUpdate"}, "WorkflowsPublic": {"properties": {"data": {"items": {"$ref": "#/components/schemas/WorkflowPublic"}, "type": "array", "title": "Data"}, "count": {"type": "integer", "title": "Count"}}, "type": "object", "required": ["data", "count"], "title": "WorkflowsPublic"}, "Workspace": {"properties": {"name": {"type": "string", "maxLength": 255, "minLength": 1, "title": "Name"}, "description": {"anyOf": [{"type": "string", "maxLength": 1000}, {"type": "null"}], "title": "Description"}, "id": {"type": "string", "format": "uuid", "title": "Id"}, "owner_id": {"type": "string", "format": "uuid", "title": "Owner Id"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}, "updated_at": {"type": "string", "format": "date-time", "title": "Updated At"}, "is_default": {"type": "boolean", "title": "<PERSON>", "default": false}, "is_deleted": {"type": "boolean", "title": "Is Deleted", "default": false}}, "type": "object", "required": ["name", "owner_id"], "title": "Workspace"}, "WorkspaceCreate": {"properties": {"name": {"type": "string", "maxLength": 255, "minLength": 1, "title": "Name"}, "description": {"anyOf": [{"type": "string", "maxLength": 1000}, {"type": "null"}], "title": "Description"}, "owner_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Owner Id"}}, "type": "object", "required": ["name"], "title": "WorkspaceCreate"}, "WorkspaceDetail": {"properties": {"name": {"type": "string", "maxLength": 255, "minLength": 1, "title": "Name"}, "description": {"anyOf": [{"type": "string", "maxLength": 1000}, {"type": "null"}], "title": "Description"}, "id": {"type": "string", "format": "uuid", "title": "Id"}, "is_default": {"type": "boolean", "title": "<PERSON>"}, "is_deleted": {"type": "boolean", "title": "Is Deleted", "default": false}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}, "updated_at": {"type": "string", "format": "date-time", "title": "Updated At"}, "aws_account": {"anyOf": [{"$ref": "#/components/schemas/AWSAccountDetail"}, {"type": "null"}]}, "settings": {"anyOf": [{"$ref": "#/components/schemas/WorkspaceSetting"}, {"type": "null"}]}, "provider_settings": {"$ref": "#/components/schemas/Setting"}}, "type": "object", "required": ["name", "id", "is_default", "settings", "provider_settings"], "title": "WorkspaceDetail"}, "WorkspacePublic": {"properties": {"name": {"type": "string", "maxLength": 255, "minLength": 1, "title": "Name"}, "description": {"anyOf": [{"type": "string", "maxLength": 1000}, {"type": "null"}], "title": "Description"}, "id": {"type": "string", "format": "uuid", "title": "Id"}, "is_default": {"type": "boolean", "title": "<PERSON>"}, "is_deleted": {"type": "boolean", "title": "Is Deleted", "default": false}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}, "updated_at": {"type": "string", "format": "date-time", "title": "Updated At"}}, "type": "object", "required": ["name", "id", "is_default"], "title": "WorkspacePublic"}, "WorkspaceSetting": {"properties": {"id": {"type": "string", "format": "uuid", "title": "Id"}, "workspace_id": {"type": "string", "format": "uuid", "title": "Workspace Id"}, "regions": {"items": {"type": "string"}, "type": "array", "title": "Regions", "default": []}, "types": {"items": {"type": "string"}, "type": "array", "title": "Types", "default": []}, "cron_pattern": {"type": "string", "maxLength": 250, "title": "<PERSON><PERSON>"}}, "type": "object", "required": ["workspace_id", "cron_pattern"], "title": "WorkspaceSetting", "description": "Settings for a workspace"}, "WorkspaceUpdate": {"properties": {"name": {"type": "string", "maxLength": 255, "minLength": 1, "title": "Name"}, "description": {"anyOf": [{"type": "string", "maxLength": 1000}, {"type": "null"}], "title": "Description"}}, "type": "object", "required": ["name"], "title": "WorkspaceUpdate"}, "WorkspacesPublic": {"properties": {"data": {"items": {"$ref": "#/components/schemas/WorkspacePublic"}, "type": "array", "title": "Data"}, "count": {"type": "integer", "title": "Count"}}, "type": "object", "required": ["data", "count"], "title": "WorkspacesPublic"}}, "securitySchemes": {"OAuth2PasswordBearer": {"type": "oauth2", "flows": {"password": {"scopes": {}, "tokenUrl": "/api/v1/login/access-token"}}}, "APIKeyHeader": {"type": "<PERSON><PERSON><PERSON><PERSON>", "in": "header", "name": "X-API-Key"}}}}