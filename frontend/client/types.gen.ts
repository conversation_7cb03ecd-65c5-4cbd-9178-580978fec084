// This file is auto-generated by @hey-api/openapi-ts

export type AccountEnvironement = 'production' | 'staging' | 'development';

export type ActivationResponse = {
    message: string;
    expires_at: string;
};

export type ActivationResult = {
    success: boolean;
    message: string;
    redirect_url?: (string | null);
    welcome_message?: (string | null);
};

export type Address = {
    city?: (string | null);
    country?: (string | null);
    line1?: (string | null);
    line2?: (string | null);
    postal_code?: (string | null);
    state?: (string | null);
};

export type AgentConnectorCreate = {
    agent_id: string;
    builtin_connector_ids: Array<(string)>;
    mcp_servers: Array<(string)>;
};

export type AgentConnectorResponse = {
    agent_id: string;
    builtin_connectors: Array<BuiltInConnectorResponse>;
    mcp_servers: Array<(string)>;
};

export type AgentConnectorUpdate = {
    builtin_connector_ids: Array<(string)>;
    mcp_servers: Array<(string)>;
};

export type AgentContextListInput = {
    agent_ids: Array<(string)>;
};

/**
 * Response model for paginated agent context list.
 *
 * Attributes:
 * data: List of agent context items
 * count: Total number of items available (before pagination)
 */
export type AgentContextListResponse = {
    data: Array<AgentContextRead>;
    count: number;
};

export type AgentContextRead = {
    title: string;
    context: string;
    is_active?: boolean;
    id: string;
    agent_id: string;
    created_at: string;
};

export type AgentContextUpdate = {
    title?: (string | null);
    context?: (string | null);
    is_active?: (boolean | null);
};

export type AgentCreate = {
    /**
     * The title/name of the agent
     */
    title: string;
    /**
     * Detailed description of the agent's purpose
     */
    description?: (string | null);
    /**
     * Type of the agent
     */
    type?: AgentType;
    /**
     * Custom instructions for the agent
     */
    instructions?: (string | null);
};

export type AgentPublic = {
    /**
     * The title/name of the agent
     */
    title: string;
    /**
     * Detailed description of the agent's purpose
     */
    description?: (string | null);
    /**
     * Type of the agent
     */
    type?: AgentType;
    /**
     * Custom instructions for the agent
     */
    instructions?: (string | null);
    id: string;
    is_active?: (boolean | null);
};

export type AgentsPublic = {
    data: Array<AgentPublic>;
    count: number;
};

/**
 * Defines the supported types of agents in the system.
 */
export type AgentType = 'conversation_agent' | 'autonomous_agent';

export type AgentTypeUsage = {
    agent_type: string;
    total_tokens: number;
};

export type AgentUpdate = {
    title?: (string | null);
    /**
     * Detailed description of the agent's purpose
     */
    description?: (string | null);
    /**
     * Type of the agent
     */
    type?: AgentType;
    /**
     * Custom instructions for the agent
     */
    instructions?: (string | null);
    workspace_id?: (string | null);
    is_active?: (boolean | null);
};

/**
 * Schema for creating a new alert
 */
export type AlertCreate = {
    /**
     * Alert title
     */
    title: string;
    /**
     * Detailed alert description
     */
    description: string;
    /**
     * Alert severity level
     */
    severity: AlertSeverity;
};

/**
 * Schema for list of alerts with pagination
 */
export type AlertList = {
    data: Array<AlertResponse>;
    total: number;
};

/**
 * Schema for alert response
 */
export type AlertResponse = {
    /**
     * Alert title
     */
    title: string;
    /**
     * Detailed alert description
     */
    description: string;
    /**
     * Alert severity level
     */
    severity: AlertSeverity;
    id: string;
    workspace_id: string;
    status: AlertStatus;
    created_at: string;
    updated_at?: (string | null);
};

export type AlertSeverity = 'CRITICAL' | 'HIGH' | 'MEDIUM' | 'LOW' | 'INFO';

export type AlertStatus = 'OPEN' | 'ACKNOWLEDGED' | 'RESOLVED' | 'CLOSED';

/**
 * Summary of alerts by status for the last 30 days
 */
export type AlertStatusSummary = {
    /**
     * Count of alerts by status
     */
    status_counts: {
        [key: string]: (number);
    };
    /**
     * Total number of alerts in the period
     */
    total: number;
};

/**
 * Schema for updating an existing alert
 */
export type AlertUpdate = {
    title?: (string | null);
    description?: (string | null);
    severity?: (AlertSeverity | null);
    status?: (AlertStatus | null);
};

export type AsyncTaskStatus = 'PENDING' | 'PROGRESS' | 'SUCCESS' | 'FAILURE';

export type AvailableUser = {
    id: string;
    email: string;
    full_name: string;
};

export type AvailableUsersCurrentWorkspace = {
    data: Array<AvailableUser>;
    count: number;
};

export type AWSAccountCreate = {
    name: string;
    description?: (string | null);
    environment: AccountEnvironement;
    account_id: string;
    access_key_id: string;
    secret_access_key: string;
    workspace_id: string;
    regions?: Array<(string)>;
    types?: Array<(string)>;
    cron_pattern: string;
};

export type AWSAccountDetail = {
    name: string;
    description?: (string | null);
    environment: AccountEnvironement;
    workspace_id: string;
    id: string;
    access_key_id: string;
    secret_access_key: string;
    account_id: string;
};

export type AWSAccountPublic = {
    name: string;
    description?: (string | null);
    environment: AccountEnvironement;
    workspace_id: string;
    id: string;
};

export type AWSAccountsPublic = {
    data: Array<AWSAccountPublic>;
    count: number;
};

export type AWSAccountUpdate = {
    name?: (string | null);
    description?: (string | null);
    environment?: (string | null);
    account_id?: (string | null);
    regions?: (Array<(string)> | null);
    types?: (Array<(string)> | null);
    cron_pattern?: (string | null);
    access_key_id?: (string | null);
    secret_access_key?: (string | null);
};

export type BillingDetails = {
    address: Address;
    email?: (string | null);
    name?: (string | null);
    phone?: (string | null);
};

export type Body_login_login_access_token = {
    grant_type?: string;
    username: string;
    password: string;
    scope?: string;
    client_id?: (string | null);
    client_secret?: (string | null);
    slackOAuth?: boolean;
    appId?: (string | null);
    teamId?: (string | null);
};

/**
 * Definition of built-in connectors available in the system
 */
export type BuiltInConnector = {
    id?: string;
    /**
     * Unique identifier for the connector
     */
    name: string;
    /**
     * Human-readable name for the connector
     */
    display_name: string;
    description?: (string | null);
    default_required_permission?: boolean;
    created_at?: string;
    updated_at?: string;
};

export type BuiltInConnectorResponse = {
    id: string;
    name?: (string | null);
    display_name?: (string | null);
    description?: (string | null);
};

export type CardDetails = {
    brand: string;
    country: string;
    display_brand: string;
    exp_month: number;
    exp_year: number;
    last4: string;
};

export type ChartDataPoint = {
    date: string;
    value: number;
};

/**
 * Enum for different types of charts available in the system
 */
export type ChartType = 'line' | 'bar' | 'pie' | 'doughnut' | 'area' | 'scatter' | 'radar' | 'step_area';

export type CheckoutSessionResponse = {
    checkout_session_url: string;
};

/**
 * Metadata for document citations
 */
export type CitationMetadata = {
    ref_id: number;
    doc_name: string;
    doc_section: string;
    text_snippet: string;
};

export type CloudProvider = 'AWS';

/**
 * Request to confirm file uploads and start ingestion
 */
export type ConfirmUploadsRequest = {
    /**
     * Information about successfully uploaded files
     */
    uploaded_files: Array<UploadedFileInfo>;
};

export type ConnectorCreate = {
    name: string;
    description?: (string | null);
    type: ConnectorType;
    approval?: boolean;
    config?: {
        [key: string]: unknown;
    };
};

export type ConnectorList = {
    data: Array<BuiltInConnector>;
    total: number;
};

export type ConnectorResponse = {
    id: string;
    name: string;
    description: (string | null);
    type: ConnectorType;
    approval?: boolean;
    config: {
        [key: string]: unknown;
    };
    workspace_id: string;
    created_by: string;
    updated_by: (string | null);
    created_at: string;
    updated_at: (string | null);
};

export type ConnectorType = 'bedrock_kb' | 'open_api';

export type ConnectorUpdate = {
    name?: (string | null);
    description?: (string | null);
    config?: ({
    [key: string]: unknown;
} | null);
    approval?: (boolean | null);
};

/**
 * Response model for a connector with its active status and permission settings in a workspace.
 *
 * Attributes:
 * id: Unique identifier for the workspace-connector association
 * name: Unique name of the connector
 * display_name: Human-readable name for the connector
 * description: Optional description of the connector
 * is_active: Whether the connector is active in this workspace
 * required_permission: Whether this tool requires human approval before execution
 */
export type ConnectorWithStatusResponse = {
    id: string;
    name: string;
    display_name: string;
    description?: (string | null);
    is_active: boolean;
    required_permission?: boolean;
};

export type ConversationCreateRequest = {
    agent_id: string;
    model_provider?: string;
    resource_id?: (string | null);
    instructions?: (string | null);
};

export type ConversationPublic = {
    id: string;
    agent_id: string;
    name: string;
    model_provider: string;
    created_at: string;
};

/**
 * Response model for paginated conversations list.
 *
 * Attributes:
 * data: List of conversation items
 * count: Total number of items available (before pagination)
 */
export type ConversationsPublic = {
    data: Array<ConversationPublic>;
    count: number;
};

export type DailyMessageVolume = {
    date: string;
    message_count: number;
};

export type DailyTokenUsage = {
    date: string;
    total_tokens: number;
};

/**
 * Class for storing a piece of text and associated metadata.
 *
 * Example:
 *
 * .. code-block:: python
 *
 * from langchain_core.documents import Document
 *
 * document = Document(
 * page_content="Hello, world!",
 * metadata={"source": "https://example.com"}
 * )
 */
export type Document = {
    id?: (string | null);
    metadata?: {
        [key: string]: unknown;
    };
    page_content: string;
    type?: "Document";
};

export type DocumentKBRead = {
    name: string;
    type: DocumentType;
    url?: (string | null);
    deep_crawl?: boolean;
    file_name?: (string | null);
    file_type?: (string | null);
    object_name?: (string | null);
    embed_status?: AsyncTaskStatus;
    id: string;
    kb_id: string;
    created_at: string;
    updated_at: string;
    is_deleted: boolean;
    parent_id?: (string | null);
    children?: Array<DocumentKBRead>;
};

export type DocumentsKBRead = {
    data: Array<DocumentKBRead>;
    count: number;
};

export type DocumentType = 'url' | 'file';

/**
 * Response model for enterprise enquiry status messages
 */
export type EnterpriseEnquiryMessageResponse = {
    message: string;
};

export type EnterpriseEnquiryRequest = {
    first_name: string;
    last_name: string;
    work_title: string;
    work_email: string;
    company_name: string;
    estimated_monthly_cost: string;
    message: string;
    product_id: string;
};

export type ErrorResponse = {
    error: string;
    details?: (string | null);
};

/**
 * Enumeration for feedback types on agent responses.
 */
export type FeedbackType = 'good' | 'bad';

/**
 * Information about a file to generate a presigned URL for
 */
export type FileInfo = {
    /**
     * Client-side ID for tracking this file
     */
    file_id: string;
    /**
     * Original filename
     */
    filename: string;
    /**
     * File MIME type
     */
    content_type: string;
    /**
     * File size in bytes
     */
    file_size: number;
};

export type HTTPValidationError = {
    detail?: Array<ValidationError>;
};

export type InvoiceResponse = {
    id: string;
    customer: string;
    status: string;
    amount_due: number;
    amount_paid: number;
    amount_remaining: number;
    currency: string;
    invoice_pdf?: (string | null);
    created: number;
    due_date?: (number | null);
    paid: boolean;
    payment_intent?: (string | null);
    subscription?: (string | null);
    total: number;
};

export type Item = {
    title: string;
    description?: (string | null);
    id?: string;
};

export type ItemCreate = {
    title: string;
    description?: (string | null);
};

export type ItemPublic = {
    title: string;
    description?: (string | null);
    id: string;
    owner_id: string;
};

export type ItemsPublic = {
    data: Array<ItemPublic>;
    count: number;
};

export type ItemUpdate = {
    title?: (string | null);
    description?: (string | null);
};

export type KBAccessLevel = 'private' | 'shared';

export type KBCreate = {
    title: string;
    description?: (string | null);
    access_level?: KBAccessLevel;
    usage_mode?: KBUsageMode;
    tags?: Array<(string)>;
    allowed_users?: Array<(string)>;
};

export type KBRead = {
    title: string;
    description?: (string | null);
    access_level: KBAccessLevel;
    usage_mode: KBUsageMode;
    tags?: Array<(string)>;
    id: string;
    created_at: string;
    updated_at: string;
    is_deleted: boolean;
    allowed_users?: Array<(string)>;
    owner_id: string;
};

export type KBsRead = {
    data: Array<KBRead>;
    count: number;
};

export type KBUpdate = {
    title?: (string | null);
    description?: (string | null);
    access_level?: (KBAccessLevel | null);
    tags?: (Array<(string)> | null);
    allowed_users?: (Array<(string)> | null);
    usage_mode?: (KBUsageMode | null);
};

export type KBUsageMode = 'manual' | 'agent_requested' | 'always';

export type MCPConfigCreate = {
    config: {
        [key: string]: unknown;
    };
};

export type MCPConfigResponse = {
    id: string;
    workspace_id: string;
    config: {
        [key: string]: unknown;
    };
    created_at: unknown;
    updated_at: unknown;
};

/**
 * Basic information about an MCP server
 */
export type MCPServerInfo = {
    name: string;
    type: MCPServerTransport;
    tool_list: Array<(string)>;
    resource_list: Array<(string)>;
    connected?: boolean;
    connection_error?: (string | null);
    is_active?: boolean;
    /**
     * Whether the server is built-in and cannot be edited
     */
    is_builtin?: boolean;
    /**
     * Permissions for the tools of the server
     */
    tools_permissions?: Array<(string)>;
};

/**
 * Response model for listing all MCP servers
 */
export type MCPServerListResponse = {
    workspace_id: string;
    servers: Array<(MCPSseServerResponse | MCPStdioServerResponse)>;
};

/**
 * Transport types for MCP servers
 */
export type MCPServerTransport = 'stdio' | 'sse';

/**
 * Response model for SSE transport server
 */
export type MCPSseServerResponse = {
    name: string;
    type: MCPServerTransport;
    tool_list: Array<(string)>;
    resource_list: Array<(string)>;
    connected?: boolean;
    connection_error?: (string | null);
    is_active?: boolean;
    /**
     * Whether the server is built-in and cannot be edited
     */
    is_builtin?: boolean;
    /**
     * Permissions for the tools of the server
     */
    tools_permissions?: Array<(string)>;
    url: string;
    headers?: {
        [key: string]: (string);
    };
};

/**
 * Response model for stdio transport server
 */
export type MCPStdioServerResponse = {
    name: string;
    type: MCPServerTransport;
    tool_list: Array<(string)>;
    resource_list: Array<(string)>;
    connected?: boolean;
    connection_error?: (string | null);
    is_active?: boolean;
    /**
     * Whether the server is built-in and cannot be edited
     */
    is_builtin?: boolean;
    /**
     * Permissions for the tools of the server
     */
    tools_permissions?: Array<(string)>;
    command: string;
    args: Array<(string)>;
};

export type Memory = {
    tags?: (Array<(string)> | null);
    task?: (string | null);
    solution?: (string | null);
    links?: (Array<(string)> | null);
    id: string;
    agent_role: string;
};

export type MemoryFilter = {
    agent_roles?: (Array<(string)> | null);
    limit?: number;
};

/**
 * Model for a memory node extracted from a conversation.
 */
export type MemoryNode = {
    tags?: (Array<(string)> | null);
    task?: (string | null);
    solution?: (string | null);
    links?: (Array<(string)> | null);
};

export type MemorysRead = {
    memories: Array<Memory>;
    count: number;
};

export type MemoryUpdate = {
    id: string;
    agent_role: string;
    memory: MemoryNode;
};

export type Message = {
    content: string;
    role?: string;
    is_interrupt?: boolean;
    interrupt_message?: (string | null);
    action_type?: MessageActionType;
    id?: string;
    conversation_id: string;
    created_at?: string;
    updated_at?: string;
    message_metadata?: {
        [key: string]: unknown;
    };
    is_deleted?: boolean;
};

/**
 * Enum for the type of action that can be taken by the message
 */
export type MessageActionType = 'none' | 'recommendation';

/**
 * Public schema for message display components
 */
export type MessageDisplayComponentPublic = {
    id: string;
    type: MessageDisplayComponentType;
    chart_type: (ChartType | null);
    title: (string | null);
    description: (string | null);
    data: {
        [key: string]: unknown;
    };
    config: {
        [key: string]: unknown;
    };
    position: number;
    created_at: string;
};

/**
 * Enum for display component types (currently supporting only tables and charts)
 */
export type MessageDisplayComponentType = 'table' | 'chart';

/**
 * Schema for creating message feedback
 */
export type MessageFeedbackCreate = {
    /**
     * Type of feedback (good/bad)
     */
    feedback_type: FeedbackType;
    /**
     * Optional reason for the feedback, required when feedback_type is BAD
     */
    reason?: (string | null);
    /**
     * Additional optional comments from the user
     */
    additional_comments?: (string | null);
    message_id: string;
};

/**
 * Public schema for message feedback responses
 */
export type MessageFeedbackPublic = {
    /**
     * Type of feedback (good/bad)
     */
    feedback_type: FeedbackType;
    /**
     * Optional reason for the feedback, required when feedback_type is BAD
     */
    reason?: (string | null);
    /**
     * Additional optional comments from the user
     */
    additional_comments?: (string | null);
    id: string;
    message_id: string;
    user_id: string;
    created_at: string;
    updated_at: string;
};

/**
 * Schema for updating message feedback
 */
export type MessageFeedbackUpdate = {
    feedback_type?: (FeedbackType | null);
    reason?: (string | null);
    additional_comments?: (string | null);
};

export type MessageHistoryPublic = {
    limit: number;
    has_more: boolean;
    /**
     * list of messages with agent thoughts. Each message contains: id, message_id, position, thought, tool, tool_input, created_at, observation
     */
    data?: Array<{
        [key: string]: unknown;
    }>;
};

export type MessagePublic = {
    content: string;
    resume: boolean;
    approve: boolean;
    restore?: (boolean | null);
    message_id?: (string | null);
    action_type?: (MessageActionType | null);
    display_components?: (Array<MessageDisplayComponentPublic> | null);
};

export type MessageStatistics = {
    total_messages: number;
    average_response_time: number;
    average_input_tokens_per_message: number;
    average_output_tokens_per_message: number;
    daily_message_volume: Array<DailyMessageVolume>;
    token_distribution_by_message_length: Array<TokenDistributionCategory>;
};

export type MetricCreate = {
    name: string;
    value: number;
    unit: string;
    timestamp: string;
    type: MetricType;
    resource_id: string;
};

export type MetricPublic = {
    name: string;
    value: number;
    unit: string;
    timestamp: string;
    type: MetricType;
    id: string;
    resource_id: string;
};

export type MetricRead = {
    name: string;
    value: number;
    unit: string;
    timestamp: string;
    type: MetricType;
    id: string;
    resource_id: string;
};

export type MetricsPublic = {
    data: Array<MetricPublic>;
    count: number;
};

export type MetricType = 'usage' | 'performance' | 'cost';

export type MetricUpdate = {
    name?: (string | null);
    value?: (number | null);
    unit?: (string | null);
    timestamp?: (string | null);
    type?: (MetricType | null);
};

export type ModuleSetting = {
    key: string;
    value?: {
        [key: string]: unknown;
    };
    created_at?: string;
    updated_at?: (string | null);
};

export type NewPassword = {
    token: string;
    new_password: string;
};

export type NodeType = 'start' | 'tool' | 'human_in_loop' | 'end' | 'output';

/**
 * Response model for paginated notifications list.
 *
 * Attributes:
 * data: List of notification items
 * count: Total number of items available (before pagination)
 */
export type NotificationList = {
    data: Array<NotificationResponse>;
    count: number;
};

export type NotificationResponse = {
    title: string;
    message: string;
    type?: NotificationType;
    status?: NotificationStatus;
    /**
     * Metadata for the notification
     */
    notification_metadata?: {
        [key: string]: unknown;
    };
    requires_action?: boolean;
    /**
     * URL for direct action
     */
    action_url?: (string | null);
    id: string;
    user_id: string;
    created_at: string;
    updated_at: string;
    read_at?: (string | null);
    expires_at?: (string | null);
};

export type NotificationStatus = 'unread' | 'read' | 'archived';

export type NotificationType = 'info' | 'warning' | 'error' | 'interrupt';

export type PaymentMethodResponse = {
    id: string;
    billing_details: BillingDetails;
    card: CardDetails;
    created: number;
    customer: string;
    livemode: boolean;
    type: string;
};

export type PlanChangeRequestCreate = {
    first_name: string;
    last_name: string;
    work_email: string;
    work_title: string;
    company_name: string;
    reason: string;
    current_product_id: string;
    requested_price_id: string;
};

export type PlanChangeRequestResponse = {
    id: string;
    first_name: string;
    last_name: string;
    work_email: string;
    work_title: string;
    company_name: string;
    reason: string;
    status: string;
    customer_id: string;
    current_product_id: string;
    requested_product_id: string;
    created_at: string;
    updated_at: string;
};

/**
 * Information about a generated presigned URL
 */
export type PresignedUrlInfo = {
    /**
     * Client-side ID for tracking this file
     */
    file_id: string;
    /**
     * Original filename
     */
    filename: string;
    /**
     * Storage key for the file
     */
    storage_key: string;
    /**
     * Presigned URL for file upload
     */
    presigned_url: string;
};

/**
 * Request to generate presigned URLs for file uploads
 */
export type PresignedUrlRequest = {
    /**
     * ID of the knowledge base to upload files to
     */
    kb_id: string;
    /**
     * Information about files to generate presigned URLs for
     */
    files: Array<FileInfo>;
};

/**
 * Response with presigned URLs for file uploads
 */
export type PresignedUrlResponse = {
    /**
     * Knowledge base ID
     */
    kb_id: string;
    /**
     * Generated presigned URLs
     */
    presigned_urls: Array<PresignedUrlInfo>;
};

export type PriceResponse = {
    id: string;
    stripe_price_id: string;
    product_id: string;
    active: boolean;
    amount: number;
    currency: string;
    interval: string;
};

export type ProductResponse = {
    id: string;
    name: string;
    description: string;
    stripe_product_id: string;
    active: boolean;
    prices?: (Array<PriceResponse> | null);
    quota_definition?: (QuotaDefinitionResponse | null);
    is_custom: boolean;
};

export type QuotaDefinitionResponse = {
    max_workspaces: number;
    max_members_per_workspace: number;
    max_fast_requests_per_month: (number | null);
};

export type QuotaInfo = {
    quota_used: number;
    quota_limit: number;
    quota_remaining: number;
    usage_percentage: number;
};

export type RecommendationCreate = {
    type: RecommendationType;
    title: string;
    description: string;
    potential_savings: number;
    effort: string;
    risk: string;
    status?: RecommendationStatus;
    resource_id: string;
};

export type RecommendationOveralPublic = {
    total_resource_scanned: number;
    total_well_optimized: number;
    total_optimization_opportunities: number;
    total_estimated_saving_amount: number;
};

export type RecommendationPublic = {
    type: RecommendationType;
    title: string;
    description: string;
    potential_savings: number;
    effort: string;
    risk: string;
    status?: RecommendationStatus;
    id: string;
    resource_id: string;
    resource: ResourcePublic;
};

export type RecommendationsPublic = {
    data: Array<RecommendationPublic>;
    count: number;
};

export type RecommendationStatus = 'pending' | 'implemented' | 'ignored' | 'in_progress';

export type RecommendationType = 'instance_rightsizing' | 'autoscaling_optimization' | 'auto_start_stop_optimization' | 'volume_optimization' | 'snapshot_cleanup' | 'reserved_instance_recommendation' | 'savings_plan_recommendation' | 'spot_instance_usage' | 'idle_resource_cleanup' | 'unused_eip_cleanup' | 'orphaned_snapshot_cleanup' | 'underutilized_ebs_cleanup' | 'serverless_migration' | 'container_adoption' | 'multi_az_optimization' | 'data_transfer_optimization' | 'cloudfront_optimization' | 'nat_gateway_optimization' | 'rds_optimization' | 'redshift_optimization' | 'dynamodb_optimization' | 's3_storage_class_optimization' | 'lambda_optimization' | 'tagging_improvement' | 'cost_allocation_improvement' | 'cost_anomaly_detection' | 'budget_alert_setup' | 'cost_explorer_usage' | 'modernize_legacy_services' | 'migrate_to_graviton' | 'compliance_optimization' | 'governance_improvement' | 'cross_region_optimization' | 'cross_account_optimization' | 'predictive_scaling' | 'ai_driven_optimization' | 'quantum_computing_readiness' | 'carbon_footprint_reduction' | 'renewable_energy_usage' | 'marketplace_alternative' | 'third_party_tool_recommendation' | 'custom_optimization' | 'other' | 'ec2_fleet_optimization' | 'spot_fleet_optimization' | 'graviton_migration' | 'predictive_scaling_optimization' | 'instance_connect_endpoint';

export type RecommendationUpdate = {
    type?: (RecommendationType | null);
    title?: (string | null);
    description?: (string | null);
    potential_savings?: (number | null);
    effort?: (string | null);
    risk?: (string | null);
    status?: (RecommendationStatus | null);
};

export type ResendActivationRequest = {
    email: string;
    captcha_token: string;
};

export type ResourceCreate = {
    name: string;
    arn: string;
    tags?: {
        [key: string]: unknown;
    };
    configurations?: {
        [key: string]: unknown;
    };
    description?: (string | null);
    type: string;
    region: string;
    status?: ResourceStatus;
    workspace_id: string;
};

export type ResourcePublic = {
    name: string;
    arn: string;
    tags?: {
        [key: string]: unknown;
    };
    configurations?: {
        [key: string]: unknown;
    };
    description?: (string | null);
    type: string;
    region: string;
    status?: ResourceStatus;
    id: string;
    workspace: WorkspacePublic;
    total_recommendation: number;
    total_potential_saving: number;
    updated_at: string;
};

export type ResourceRead = {
    name: string;
    arn: string;
    tags: {
        [key: string]: (string);
    };
    configurations: {
        [key: string]: unknown;
    };
    description?: (string | null);
    type: string;
    region: string;
    status?: ResourceStatus;
    recommendations: Array<RecommendationPublic>;
    metrics?: ({
    [key: string]: Array<MetricRead>;
} | null);
};

export type ResourceSavingsReport = {
    rds_savings: Array<ChartDataPoint>;
    ec2_savings: Array<ChartDataPoint>;
    total_rds_savings: number;
    total_ec2_savings: number;
};

export type ResourcesPublic = {
    data: Array<ResourcePublic>;
    count: number;
};

export type ResourceStatus = 'stopped' | 'starting' | 'running' | 'found' | 'deleted';

export type ResourceType = 'EC2' | 'LAMBDA' | 'ECS' | 'EKS' | 'BATCH' | 'EC2_AUTO_SCALING' | 'ELASTIC_BEANSTALK' | 'APP_RUNNER' | 'RDS' | 'DYNAMODB' | 'ELASTICACHE' | 'NEPTUNE' | 'DOCUMENTDB' | 'OPENSEARCH' | 'REDSHIFT' | 'S3' | 'EBS' | 'EFS' | 'BACKUP' | 'VPC' | 'ELB' | 'CLOUDFORMATION' | 'CLOUDWATCH' | 'SQS' | 'SNS';

export type ResourceUpdate = {
    name?: (string | null);
    arn?: (string | null);
    tags?: {
        [key: string]: unknown;
    };
    configurations?: {
        [key: string]: unknown;
    };
    description?: (string | null);
    type: string;
    region: string;
    status?: ResourceStatus;
};

export type RetrieverConfig = {
    numberOfResults?: number;
    overrideSearchType?: ('HYBRID' | 'SEMANTIC' | null);
};

export type RunModeEnum = 'autonomous' | 'agent';

export type SavingSummaryReport = {
    potential_savings: number;
    potential_savings_percentage_change: number;
    save_opportunities: number;
    save_opportunities_percentage_change: number;
    total_saved: number;
    total_saved_percentage_change: number;
    active_saving: number;
    active_saving_percentage_change: number;
};

export type ScriptExecutionResponse = {
    status: string;
    result?: (string | null);
};

export type SearchResponse = {
    query: string;
    results: Array<Document>;
    total_found: number;
    execution_time: number;
};

export type ServiceSavingsData = {
    service: string;
    savings: number;
    percentage: number;
};

export type ServiceSavingsReport = {
    data: Array<ServiceSavingsData>;
    total_savings: number;
};

export type Setting = {
    id?: string;
    provider_name?: CloudProvider;
    regions?: Array<(string)>;
    types?: Array<(string)>;
    cron_patterns?: Array<(string)>;
};

export type ShareResponse = {
    share_id: string;
    is_shared: boolean;
    shared_at: string;
    shared_by: string;
};

export type StreamResponse = {
    type: string;
    content?: (string | null);
    message_id?: (string | null);
};

export type SubscriptionStatus = {
    id: string;
    customer_id: string;
    status: string;
    current_period_end: string;
    cancel_at?: (string | null);
    product_name: string;
    product_id?: (string | null);
    price_amount: number;
    price_currency: string;
    price_interval: string;
};

export type SummaryResponse = {
    query: string;
    summary: string;
    sources: Array<unknown>;
    citations: Array<CitationMetadata>;
    execution_time: number;
};

/**
 * Enumeration of possible task categories.
 */
export type TaskCategoryEnum = 'COST_OPTIMIZE' | 'OPERATIONAL' | 'SCALABILITY' | 'SECURITY' | 'OPERATIONAL_EFFICIENCY' | 'OTHER';

export type TaskCouldEnum = 'AWS' | 'AZURE' | 'GCP' | 'ALL';

/**
 * Schema for creating a new task.
 */
export type TaskCreate = {
    title: string;
    description?: (string | null);
    priority?: TaskPriority;
    tags?: Array<(string)>;
    schedule?: (string | null);
    agent_config?: {
        [key: string]: unknown;
    };
};

/**
 * Schema for task delete response.
 */
export type TaskDeleteResponse = {
    status?: string;
};

/**
 * Enumeration of execution statuses for task.
 *
 * Attributes:
 * RUNNING: Currently executing
 * SUCCEEDED: Successfully completed
 * FAILED: Execution failed
 * CANCELLED: Execution cancelled
 * REQUIRED_APPROVAL: Execution requires approval
 */
export type TaskExecutionStatus = 'running' | 'succeeded' | 'failed' | 'cancelled' | 'required_approval';

/**
 * Execution history of a task conversation.
 */
export type TaskHistory = {
    id?: string;
    task_id: string;
    conversation_id: string;
    /**
     * Current task status
     */
    status: TaskExecutionStatus;
    /**
     * Message from the task execution: error or required action
     */
    message?: (string | null);
    /**
     * Celery task ID associated with the task
     */
    celery_task_id?: (string | null);
    /**
     * Timestamp when the task history was started
     */
    start_time?: string;
    /**
     * Timestamp when the task history was ended
     */
    end_time?: (string | null);
    /**
     * Time taken to run the task
     */
    run_time?: (number | null);
};

/**
 * Schema for paginated task list.
 */
export type TaskList = {
    data?: Array<TaskResponse>;
    total?: number;
};

/**
 * Enumeration of task priority levels.
 *
 * Attributes:
 * LOW (0): Regular priority, no urgency
 * MEDIUM (1): Moderate priority, should be done soon
 * HIGH (2): High priority, urgent attention needed
 * CRITICAL (3): Critical priority, requires immediate attention
 */
export type TaskPriority = 0 | 1 | 2 | 3;

/**
 * Schema for task response.
 */
export type TaskResponse = {
    title: string;
    description?: (string | null);
    priority?: TaskPriority;
    tags?: Array<(string)>;
    id: string;
    workspace_id: string;
    owner_id: string;
    scheduled_status?: (TaskScheduledStatus | null);
    execution_status?: (TaskExecutionStatus | null);
    error?: (string | null);
    last_run?: (string | null);
    next_run?: (string | null);
    schedule?: (string | null);
    agent_config?: {
        [key: string]: unknown;
    };
    enable?: boolean;
    task_history?: Array<TaskHistory>;
    created_at: string;
    updated_at: string;
    created_by: string;
    updated_by: string;
};

/**
 * Enumeration of scheduled statuses for task.
 */
export type TaskScheduledStatus = 'pending' | 'scheduled';

/**
 * Enumeration of possible task services.
 */
export type TaskServiceEnum = 'ALL' | 'OTHER' | 'COMPUTE' | 'STORAGE' | 'SERVERLESS' | 'DATABASE' | 'NETWORK' | 'MESSAGING' | 'MANAGEMENT' | 'BILLING' | 'CROSS_SERVICE' | 'MONITORING' | 'STREAMING' | 'SECURITY';

/**
 * Response schema for task status operations
 */
export type TaskStatusResponse = {
    /**
     * Celery task ID
     */
    task_id: string;
    /**
     * Task status (PENDING, PROGRESS, SUCCESS, FAILURE)
     */
    status: AsyncTaskStatus;
    /**
     * Progress percentage (0-100)
     */
    progress?: number;
    /**
     * Task result if completed
     */
    result?: ({
    [key: string]: unknown;
} | null);
    /**
     * Error message if failed
     */
    error?: (string | null);
    /**
     * Human-readable status message
     */
    status_message?: (string | null);
};

/**
 * Schema for task stop response.
 */
export type TaskStopResponse = {
    task_id: string;
    conversation_id: string;
    status: string;
};

export type TaskTemplateCreate = {
    task: string;
    category?: (TaskCategoryEnum | null);
    service?: (TaskServiceEnum | null);
    service_name?: (string | null);
    cloud: TaskCouldEnum;
    run_mode: RunModeEnum;
    schedule?: (string | null);
    context: string;
};

export type TaskTemplateList = {
    data: Array<TaskTemplateResponse>;
    total: number;
};

export type TaskTemplateResponse = {
    id: string;
    task: string;
    category: TaskCategoryEnum;
    service: TaskServiceEnum;
    service_name: string;
    cloud: TaskCouldEnum;
    run_mode: RunModeEnum;
    schedule: (string | null);
    context: string;
    is_default: boolean;
    created_at: string;
    updated_at: (string | null);
};

export type TaskTemplateUpdate = {
    task?: (string | null);
    category?: (TaskCategoryEnum | null);
    service?: (TaskServiceEnum | null);
    run_mode?: (RunModeEnum | null);
    schedule?: (string | null);
    context?: (string | null);
};

/**
 * Schema for updating an existing task.
 */
export type TaskUpdate = {
    title?: (string | null);
    description?: (string | null);
    priority?: (TaskPriority | null);
    tags?: (Array<(string)> | null);
    schedule?: (string | null);
    agent_config?: {
        [key: string]: unknown;
    };
};

export type Token = {
    access_token: string;
    token_type?: string;
    workspace_id?: (string | null);
    is_first_login?: boolean;
    slack_oauth?: boolean;
    app_id?: (string | null);
    team_id?: (string | null);
};

export type TokenDistributionCategory = {
    category: string;
    percentage: number;
};

export type TokenUsageCreate = {
    /**
     * Unique identifier of the associated message
     */
    message_id: string;
    /**
     * Number of tokens in the input text
     */
    input_tokens: number;
    /**
     * Number of tokens in the output text
     */
    output_tokens: number;
    /**
     * Identifier of the AI model used
     */
    model_id: string;
};

/**
 * Schema for token usage response.
 *
 * Attributes:
 * id: Unique identifier for the usage record
 * message_id: ID of the associated message
 * input_tokens: Number of tokens in input text
 * output_tokens: Number of tokens in output text
 * model_id: ID of the AI model used
 * total_tokens: Total number of tokens used
 * created_at: Timestamp of record creation
 */
export type TokenUsageResponse = {
    /**
     * Unique identifier for the usage record
     */
    id: string;
    /**
     * ID of the associated message
     */
    message_id: string;
    /**
     * Number of tokens in the input text
     */
    input_tokens: number;
    /**
     * Number of tokens in the output text
     */
    output_tokens: number;
    /**
     * Identifier of the AI model used
     */
    model_id: string;
    /**
     * ID of the workspace
     */
    workspace_id: string;
    /**
     * Timestamp of record creation
     */
    created_at: string;
    /**
     * Timestamp of last update
     */
    updated_at?: (string | null);
    /**
     * Calculate total tokens from input and output tokens.
     */
    readonly total_tokens: number;
};

export type ToolPermissionRequest = {
    permission: string;
};

export type TopSavingsReport = {
    data: Array<RecommendationPublic>;
};

export type UpdatePassword = {
    current_password: string;
    new_password: string;
};

export type UploadCreate = {
    filename: string;
    file_size: number;
    file_type: string;
};

/**
 * Information about a successfully uploaded file
 */
export type UploadedFileInfo = {
    /**
     * Client-side ID for tracking this file
     */
    file_id: string;
    /**
     * Original filename
     */
    filename: string;
    /**
     * Storage key for the uploaded file
     */
    storage_key: string;
    /**
     * File MIME type
     */
    content_type?: (string | null);
    /**
     * File size in bytes
     */
    file_size?: (number | null);
};

export type UploadPublic = {
    filename: string;
    file_size: number;
    file_type: string;
    id: string;
    status: UploadStatus;
    created_at: string;
};

export type UploadResponse = {
    id: string;
    upload_url: string;
    expires_in: number;
};

export type UploadStatus = 'pending' | 'in_progress' | 'completed' | 'failed';

export type URLsUploadRequest = {
    /**
     * URLs to crawl (required if source_type is website)
     */
    urls?: (Array<(string)> | null);
    /**
     * Whether to enable deep crawling for each URL
     */
    deep_crawls?: (Array<(boolean)> | null);
};

/**
 * Response schema for usage quota information.
 */
export type UsageQuotaResponse = {
    id: string;
    user_id: string;
    quota_used_messages: number;
    quota_used_tokens: number;
    reset_at: string;
    created_at: string;
    updated_at?: (string | null);
};

/**
 * Response schema for usage statistics.
 */
export type UsageStatistics = {
    input_tokens: number;
    output_tokens: number;
    total_tokens: number;
    quota_limit: number;
    quota_used: number;
    quota_remaining: number;
    usage_percentage: number;
    daily_token_usage: Array<DailyTokenUsage>;
    agent_type_stats: Array<AgentTypeUsage>;
};

export type UserCreate = {
    email: string;
    is_active?: boolean;
    is_email_verified?: boolean;
    is_superuser?: boolean;
    last_login_time?: (string | null);
    full_name?: (string | null);
    avatar_url?: (string | null);
    password: string;
};

export type UserDetail = {
    email: string;
    is_active?: boolean;
    is_email_verified?: boolean;
    is_superuser?: boolean;
    last_login_time?: (string | null);
    full_name?: (string | null);
    avatar_url?: (string | null);
    id: string;
    workspaces?: (Array<Workspace> | null);
    own_workspaces?: (Array<Workspace> | null);
    items?: (Array<Item> | null);
};

export type UserPublic = {
    email: string;
    is_active?: boolean;
    is_email_verified?: boolean;
    is_superuser?: boolean;
    last_login_time?: (string | null);
    full_name?: (string | null);
    avatar_url?: (string | null);
    id: string;
};

export type UserRegister = {
    email: string;
    password: string;
    full_name?: (string | null);
};

export type UsersPublic = {
    data: Array<UserPublic>;
    count: number;
};

export type UserUpdate = {
    email?: (string | null);
    is_active?: boolean;
    is_email_verified?: boolean;
    is_superuser?: boolean;
    last_login_time?: (string | null);
    full_name?: (string | null);
    avatar_url?: (string | null);
    password?: (string | null);
};

export type UserUpdateMe = {
    full_name?: (string | null);
    email?: (string | null);
    avatar_url?: (string | null);
};

export type ValidationError = {
    loc: Array<(string | number)>;
    msg: string;
    type: string;
};

export type WorkflowCreate = {
    name: string;
    description?: (string | null);
    workspace_id: string;
};

export type WorkflowNodeCreate = {
    type: NodeType;
    name: string;
    description?: (string | null);
    position: number;
    data?: {
        [key: string]: unknown;
    };
    workflow_id: string;
    parent_id?: (string | null);
};

export type WorkflowNodePublic = {
    type: NodeType;
    name: string;
    description?: (string | null);
    position: number;
    data?: {
        [key: string]: unknown;
    };
    id: string;
    status: WorkflowStatus;
};

export type WorkflowNodeUpdate = {
    name?: (string | null);
    description?: (string | null);
    position?: (number | null);
};

export type WorkflowPublic = {
    name: string;
    description?: (string | null);
    workspace_id: string;
    id: string;
    nodes: Array<WorkflowNodePublic>;
    status?: WorkflowStatus;
    created_at?: string;
    updated_at?: string;
};

export type WorkflowsPublic = {
    data: Array<WorkflowPublic>;
    count: number;
};

export type WorkflowStatus = 'created' | 'unvalidated' | 'running' | 'pending' | 'completed' | 'error';

export type WorkflowUpdate = {
    name?: (string | null);
    description?: (string | null);
    workspace_id?: (string | null);
};

export type Workspace = {
    name: string;
    description?: (string | null);
    id?: string;
    owner_id: string;
    created_at?: string;
    updated_at?: string;
    is_default?: boolean;
    is_deleted?: boolean;
};

export type WorkspaceCreate = {
    name: string;
    description?: (string | null);
    owner_id?: (string | null);
};

export type WorkspaceDetail = {
    name: string;
    description?: (string | null);
    id: string;
    is_default: boolean;
    is_deleted?: boolean;
    created_at?: string;
    updated_at?: string;
    aws_account?: (AWSAccountDetail | null);
    settings: (WorkspaceSetting | null);
    provider_settings: Setting;
};

export type WorkspacePublic = {
    name: string;
    description?: (string | null);
    id: string;
    is_default: boolean;
    is_deleted?: boolean;
    created_at?: string;
    updated_at?: string;
};

/**
 * Settings for a workspace
 */
export type WorkspaceSetting = {
    id?: string;
    workspace_id: string;
    regions?: Array<(string)>;
    types?: Array<(string)>;
    cron_pattern: string;
};

export type WorkspacesPublic = {
    data: Array<WorkspacePublic>;
    count: number;
};

export type WorkspaceUpdate = {
    name: string;
    description?: (string | null);
};

export type CreateAgentConnectorData = {
    requestBody: AgentConnectorCreate;
};

export type CreateAgentConnectorResponse = (AgentConnectorResponse);

export type GetAgentConnectorData = {
    agentId: string;
};

export type GetAgentConnectorResponse = (AgentConnectorResponse);

export type UpdateAgentConnectorData = {
    agentId: string;
    requestBody: AgentConnectorUpdate;
};

export type UpdateAgentConnectorResponse = (AgentConnectorResponse);

export type DeleteAgentConnectorData = {
    agentId: string;
};

export type DeleteAgentConnectorResponse = (void);

export type GetAgentConnectorsByWorkspaceIdData = {
    workspaceId: string;
};

export type GetAgentConnectorsByWorkspaceIdResponse = (Array<AgentConnectorResponse>);

export type UpdateAgentContextData = {
    agentId: string;
    requestBody: AgentContextUpdate;
};

export type UpdateAgentContextResponse = (AgentContextRead);

export type GetAgentContextData = {
    agentId: string;
};

export type GetAgentContextResponse = ((AgentContextRead | null));

export type GetAgentContextsData = {
    requestBody: AgentContextListInput;
};

export type GetAgentContextsResponse = (AgentContextListResponse);

export type ReadAgentsData = {
    limit?: number;
    skip?: number;
};

export type ReadAgentsResponse = (AgentsPublic);

export type CreateAgentData = {
    requestBody: AgentCreate;
};

export type CreateAgentResponse = (AgentPublic);

export type ReadAgentData = {
    id: string;
};

export type ReadAgentResponse = (AgentPublic);

export type UpdateAgentData = {
    id: string;
    requestBody: AgentUpdate;
};

export type UpdateAgentResponse = (AgentPublic);

export type DeleteAgentData = {
    id: string;
};

export type DeleteAgentResponse = (Message);

export type InitDefaultAgentsData = {
    workspaceId: string;
};

export type InitDefaultAgentsResponse = (Message);

export type GetAlertStatusSummaryResponse = (AlertStatusSummary);

export type CreateAlertData = {
    requestBody: AlertCreate;
};

export type CreateAlertResponse = (AlertResponse);

export type ListAlertsData = {
    limit?: number;
    severity?: (AlertSeverity | null);
    skip?: number;
    /**
     * Field to sort by
     */
    sortBy?: string;
    /**
     * Sort in descending order
     */
    sortDesc?: boolean;
    status?: (AlertStatus | null);
};

export type ListAlertsResponse = (AlertList);

export type GetAlertData = {
    alertId: string;
};

export type GetAlertResponse = (AlertResponse);

export type UpdateAlertData = {
    alertId: string;
    requestBody: AlertUpdate;
};

export type UpdateAlertResponse = (AlertResponse);

export type DeleteAlertData = {
    alertId: string;
};

export type DeleteAlertResponse = (unknown);

export type UpdateAlertStatusData = {
    alertId: string;
    status: AlertStatus;
};

export type UpdateAlertStatusResponse = (AlertResponse);

export type RegisterData = {
    requestBody: UserRegister;
};

export type RegisterResponse = (ActivationResponse);

export type ActivateAccountData = {
    token: string;
};

export type ActivateAccountResponse = (ActivationResult);

export type ResendActivationData = {
    requestBody: ResendActivationRequest;
};

export type ResendActivationResponse = (ActivationResponse);

export type CreateConversationData = {
    requestBody: ConversationCreateRequest;
};

export type CreateConversationResponse = (ConversationPublic);

export type GetConversationsData = {
    agentId?: (string | null);
    /**
     * Maximum number of records to return
     */
    limit?: number;
    modelProvider?: (string | null);
    resourceId?: (string | null);
    /**
     * Number of records to skip for pagination
     */
    skip?: number;
};

export type GetConversationsResponse = (ConversationsPublic);

export type GetMessagesHistoryData = {
    conversationId: string;
    limit?: number;
};

export type GetMessagesHistoryResponse = (MessageHistoryPublic);

export type ChatStreamData = {
    conversationId: string;
    requestBody: MessagePublic;
};

export type ChatStreamResponse = (StreamResponse);

export type RenameConversationData = {
    conversationId: string;
    name: string;
};

export type RenameConversationResponse = (unknown);

export type DeleteConversationData = {
    conversationId: string;
};

export type DeleteConversationResponse = (unknown);

export type ReadAwsAccountsData = {
    limit?: number;
    skip?: number;
};

export type ReadAwsAccountsResponse = (AWSAccountsPublic);

export type CreateAwsAccountData = {
    requestBody: AWSAccountCreate;
};

export type CreateAwsAccountResponse = (AWSAccountPublic);

export type ReadAwsAccountData = {
    id: string;
};

export type ReadAwsAccountResponse = (AWSAccountPublic);

export type UpdateAwsAccountData = {
    id: string;
    requestBody: AWSAccountUpdate;
};

export type UpdateAwsAccountResponse = (AWSAccountPublic);

export type DeleteAwsAccountData = {
    id: string;
};

export type DeleteAwsAccountResponse = (Message);

export type ListWorkspaceConnectorsData = {
    activeOnly?: boolean;
    limit?: number;
    skip?: number;
    workspaceId: string;
};

export type ListWorkspaceConnectorsResponse = (Array<ConnectorWithStatusResponse>);

export type UpdateConnectorForWorkspaceData = {
    connectorId: string;
    isActive: boolean;
    workspaceId: string;
};

export type UpdateConnectorForWorkspaceResponse = (boolean);

export type UpdateConnectorPermissionData = {
    connectorId: string;
    requiredPermission: boolean;
    workspaceId: string;
};

export type UpdateConnectorPermissionResponse = (boolean);

export type CreateConnectorData = {
    requestBody: ConnectorCreate;
};

export type CreateConnectorResponse = (ConnectorResponse);

export type ListConnectorsData = {
    limit?: number;
    skip?: number;
};

export type ListConnectorsResponse = (ConnectorList);

export type GetConnectorData = {
    id: string;
};

export type GetConnectorResponse = (ConnectorResponse);

export type UpdateConnectorData = {
    id: string;
    requestBody: ConnectorUpdate;
};

export type UpdateConnectorResponse = (ConnectorResponse);

export type DeleteConnectorData = {
    id: string;
};

export type DeleteConnectorResponse = (Message);

export type CreateUploadUrlData = {
    requestBody: UploadCreate;
};

export type CreateUploadUrlResponse = (UploadResponse);

export type CheckUploadStatusData = {
    id: string;
};

export type CheckUploadStatusResponse = (UploadPublic);

export type GoogleLoginResponse = (unknown);

export type GoogleCallbackResponse = (Token);

export type ReadItemsData = {
    limit?: number;
    skip?: number;
};

export type ReadItemsResponse = (ItemsPublic);

export type CreateItemData = {
    requestBody: ItemCreate;
};

export type CreateItemResponse = (ItemPublic);

export type ReadItemData = {
    id: string;
};

export type ReadItemResponse = (ItemPublic);

export type UpdateItemData = {
    id: string;
    requestBody: ItemUpdate;
};

export type UpdateItemResponse = (ItemPublic);

export type DeleteItemData = {
    id: string;
};

export type DeleteItemResponse = (Message);

export type CreateKbData = {
    requestBody: KBCreate;
};

export type CreateKbResponse = (KBRead);

export type GetKbsData = {
    limit?: number;
    skip?: number;
};

export type GetKbsResponse = (KBsRead);

export type GetAvailableUsersResponse = (AvailableUsersCurrentWorkspace);

export type GetPointUsageResponse = ({
    [key: string]: unknown;
});

export type GetKbByIdData = {
    kbId: string;
};

export type GetKbByIdResponse = (KBRead);

export type UpdateKbData = {
    kbId: string;
    requestBody: KBUpdate;
};

export type UpdateKbResponse = (KBRead);

export type DeleteKbData = {
    kbId: string;
};

export type DeleteKbResponse = ({
    [key: string]: unknown;
});

export type GeneratePresignedUrlsData = {
    kbId: string;
    requestBody: PresignedUrlRequest;
};

export type GeneratePresignedUrlsResponse = (PresignedUrlResponse);

export type ConfirmFileUploadsData = {
    kbId: string;
    requestBody: ConfirmUploadsRequest;
};

export type ConfirmFileUploadsResponse = (TaskStatusResponse);

export type UploadUrlsData = {
    kbId: string;
    requestBody: URLsUploadRequest;
};

export type UploadUrlsResponse = (TaskStatusResponse);

export type ListDocumentsData = {
    kbId: string;
    limit?: number;
    search?: (string | null);
    skip?: number;
};

export type ListDocumentsResponse = (DocumentsKBRead);

export type GetDocumentContentData = {
    kbId: string;
    objectName: string;
};

export type GetDocumentContentResponse = (string);

export type DeleteDocumentData = {
    documentId: string;
    kbId: string;
    objectName: string;
};

export type DeleteDocumentResponse = ({
    [key: string]: unknown;
});

export type GetTaskStatusData = {
    taskId: string;
};

export type GetTaskStatusResponse = (TaskStatusResponse);

export type SearchData = {
    kbId: string;
    query: string;
    requestBody: RetrieverConfig;
};

export type SearchResponse2 = (SearchResponse);

export type SummarizeData = {
    kbId: string;
    query: string;
    requestBody: RetrieverConfig;
};

export type SummarizeResponse = (SummaryResponse);

export type LoginAccessTokenData = {
    formData: Body_login_login_access_token;
};

export type LoginAccessTokenResponse = (Token);

export type TestTokenResponse = (UserPublic);

export type RecoverPasswordData = {
    email: string;
};

export type RecoverPasswordResponse = (Message);

export type ResetPasswordData = {
    requestBody: NewPassword;
};

export type ResetPasswordResponse = (Message);

export type RecoverPasswordHtmlContentData = {
    email: string;
};

export type RecoverPasswordHtmlContentResponse = (string);

export type GetMcpConfigData = {
    workspaceId: string;
};

export type GetMcpConfigResponse = (MCPConfigResponse);

export type CreateMcpConfigData = {
    requestBody: MCPConfigCreate;
    workspaceId: string;
};

export type CreateMcpConfigResponse = (MCPConfigResponse);

export type UpdateMcpConfigData = {
    requestBody: MCPConfigCreate;
    workspaceId: string;
};

export type UpdateMcpConfigResponse = (MCPConfigResponse);

export type DeleteMcpConfigData = {
    workspaceId: string;
};

export type DeleteMcpConfigResponse = (Message);

export type GetMcpServersData = {
    workspaceId: string;
};

export type GetMcpServersResponse = (MCPServerListResponse);

export type ToggleMcpServerActiveStateData = {
    serverName: string;
    workspaceId: string;
};

export type ToggleMcpServerActiveStateResponse = (MCPConfigResponse);

export type RefreshMcpServerStatusData = {
    serverName: string;
    workspaceId: string;
};

export type RefreshMcpServerStatusResponse = (MCPServerInfo);

export type AddServerToolPermissionData = {
    requestBody: ToolPermissionRequest;
    serverName: string;
    workspaceId: string;
};

export type AddServerToolPermissionResponse = (MCPConfigResponse);

export type RemoveServerToolPermissionData = {
    requestBody: ToolPermissionRequest;
    serverName: string;
    workspaceId: string;
};

export type RemoveServerToolPermissionResponse = (MCPConfigResponse);

export type GetMemoryData = {
    requestBody: MemoryFilter;
};

export type GetMemoryResponse = (MemorysRead);

export type DeleteMemoryData = {
    agentRole: string;
    id: string;
};

export type DeleteMemoryResponse = (unknown);

export type UpdateMemoryData = {
    requestBody: MemoryUpdate;
};

export type UpdateMemoryResponse = (unknown);

export type GetMessageFeedbackData = {
    messageId: string;
};

export type GetMessageFeedbackResponse = ((MessageFeedbackPublic | null));

export type UpdateMessageFeedbackData = {
    messageId: string;
    requestBody: MessageFeedbackUpdate;
};

export type UpdateMessageFeedbackResponse = (MessageFeedbackPublic);

export type DeleteMessageFeedbackData = {
    messageId: string;
};

export type DeleteMessageFeedbackResponse = (unknown);

export type CreateMessageFeedbackData = {
    requestBody: MessageFeedbackCreate;
};

export type CreateMessageFeedbackResponse = (MessageFeedbackPublic);

export type ReadMetricsData = {
    endDate?: (string | null);
    limit?: number;
    resourceId?: (string | null);
    skip?: number;
    startDate?: (string | null);
};

export type ReadMetricsResponse = (MetricsPublic);

export type CreateMetricData = {
    requestBody: MetricCreate;
};

export type CreateMetricResponse = (MetricPublic);

export type ReadMetricData = {
    id: string;
};

export type ReadMetricResponse = (MetricPublic);

export type UpdateMetricData = {
    id: string;
    requestBody: MetricUpdate;
};

export type UpdateMetricResponse = (MetricPublic);

export type DeleteMetricData = {
    id: string;
};

export type DeleteMetricResponse = (Message);

export type GetModuleSettingsResponse = (Array<ModuleSetting>);

export type ListNotificationsData = {
    limit?: number;
    requestBody?: (Array<NotificationType> | null);
    requiresAction?: (boolean | null);
    skip?: number;
    timeframe?: (string | null);
};

export type ListNotificationsResponse = (NotificationList);

export type MarkNotificationReadData = {
    notificationId: string;
};

export type MarkNotificationReadResponse = (NotificationResponse);

export type MarkAllNotificationsReadData = {
    requestBody?: (Array<NotificationType> | null);
};

export type MarkAllNotificationsReadResponse = (unknown);

export type CreateUsageData = {
    requestBody: TokenUsageCreate;
};

export type CreateUsageResponse = (TokenUsageResponse);

export type CreateUsageQuotaData = {
    userId: string;
};

export type CreateUsageQuotaResponse = (UsageQuotaResponse);

export type GetUsageQuotaData = {
    userId: string;
};

export type GetUsageQuotaResponse = (UsageQuotaResponse);

export type ResetUserQuotaData = {
    userId: string;
};

export type ResetUserQuotaResponse = (UsageQuotaResponse);

export type GetUsageStatisticsData = {
    endDate?: (string | null);
    startDate?: (string | null);
    userId: string;
};

export type GetUsageStatisticsResponse = (UsageStatistics);

export type GetMessagesStatisticsData = {
    endDate?: (string | null);
    startDate?: (string | null);
    workspaceId: string;
};

export type GetMessagesStatisticsResponse = (MessageStatistics);

export type GetQuotaInfoData = {
    userId: string;
};

export type GetQuotaInfoResponse = (QuotaInfo);

export type GetRecomendationOveralResponse = (RecommendationOveralPublic);

export type ReadRecommendationsData = {
    endDate?: (string | null);
    limit?: number;
    orderBy?: (string | null);
    orderDirection?: string;
    recommendationType?: Array<(string)>;
    resourceId?: Array<(string)>;
    resourceType?: Array<(string)>;
    search?: (string | null);
    skip?: number;
    startDate?: (string | null);
    status?: Array<(string)>;
};

export type ReadRecommendationsResponse = (RecommendationsPublic);

export type CreateRecommendationData = {
    requestBody: RecommendationCreate;
};

export type CreateRecommendationResponse = (RecommendationPublic);

export type ReadRecommendationData = {
    id: string;
};

export type ReadRecommendationResponse = (RecommendationPublic);

export type UpdateRecommendationData = {
    id: string;
    requestBody: RecommendationUpdate;
};

export type UpdateRecommendationResponse = (RecommendationPublic);

export type DeleteRecommendationData = {
    id: string;
};

export type DeleteRecommendationResponse = (Message);

export type UpdateRecommendationStatusData = {
    id: string;
    status: RecommendationStatus;
};

export type UpdateRecommendationStatusResponse = (RecommendationPublic);

export type GetSavingsSummaryData = {
    endDate?: (string | null);
    previousEndDate?: (string | null);
    previousStartDate?: (string | null);
    startDate?: (string | null);
};

export type GetSavingsSummaryResponse = (SavingSummaryReport);

export type GetSavingsByResourceData = {
    endDate?: (string | null);
    startDate?: (string | null);
};

export type GetSavingsByResourceResponse = (ResourceSavingsReport);

export type GetTopPotentialSavingsData = {
    /**
     * End date in ISO format
     */
    endDate?: (string | null);
    limit?: number;
    /**
     * Start date in ISO format
     */
    startDate?: (string | null);
};

export type GetTopPotentialSavingsResponse = (TopSavingsReport);

export type GetSavingsByServiceData = {
    endDate?: (string | null);
    startDate?: (string | null);
};

export type GetSavingsByServiceResponse = (ServiceSavingsReport);

export type ReadResourcesData = {
    limit?: number;
    name?: string;
    region?: Array<(string)>;
    resourceType?: Array<(string)>;
    skip?: number;
    status?: Array<(string)>;
};

export type ReadResourcesResponse = (ResourcesPublic);

export type CreateResourceData = {
    requestBody: ResourceCreate;
};

export type CreateResourceResponse = (ResourcePublic);

export type ReadResourceData = {
    id: string;
};

export type ReadResourceResponse = (ResourceRead);

export type UpdateResourceData = {
    id: string;
    requestBody: ResourceUpdate;
};

export type UpdateResourceResponse = (ResourcePublic);

export type DeleteResourceData = {
    id: string;
};

export type DeleteResourceResponse = (Message);

export type CreateSampleResourcesData = {
    daysBack?: number;
    metricsPerResource?: number;
    resourceCount?: number;
    resourceType: ResourceType;
};

export type CreateSampleResourcesResponse = (unknown);

export type CreateSampleMetricsData = {
    daysBack?: number;
    numPoints?: number;
    resourceType: ResourceType;
};

export type CreateSampleMetricsResponse = (unknown);

export type CreateSampleRecommendationsData = {
    totalRecord?: number;
};

export type CreateSampleRecommendationsResponse = (RecommendationsPublic);

export type CreateShareLinkData = {
    conversationId: string;
};

export type CreateShareLinkResponse = (ShareResponse);

export type RevokeShareLinkData = {
    conversationId: string;
};

export type RevokeShareLinkResponse = (unknown);

export type GetShareLinkData = {
    conversationId: string;
};

export type GetShareLinkResponse = (ShareResponse);

export type GetSharedConversationData = {
    limit?: number;
    shareId: string;
};

export type GetSharedConversationResponse = (MessageHistoryPublic);

export type GetAvailablePlansResponse = (Array<ProductResponse>);

export type GetUserSubscriptionStatusResponse = (Array<SubscriptionStatus>);

export type GetWorkspaceSubscriptionStatusData = {
    workspaceId: string;
};

export type GetWorkspaceSubscriptionStatusResponse = (Array<SubscriptionStatus>);

export type CreateCheckoutSessionData = {
    priceId: string;
};

export type CreateCheckoutSessionResponse = (CheckoutSessionResponse);

export type GetUserPaymentMethodsData = {
    paymentType?: string;
};

export type GetUserPaymentMethodsResponse = (Array<PaymentMethodResponse>);

export type GetUserInvoicesData = {
    limit?: number;
    status?: (string | null);
};

export type GetUserInvoicesResponse = (Array<InvoiceResponse>);

export type SubmitEnterpriseEnquiryData = {
    requestBody: EnterpriseEnquiryRequest;
};

export type SubmitEnterpriseEnquiryResponse = (EnterpriseEnquiryMessageResponse);

export type SubmitPlanChangeRequestData = {
    requestBody: PlanChangeRequestCreate;
};

export type SubmitPlanChangeRequestResponse = (PlanChangeRequestResponse);

export type WebhookResponse = (unknown);

export type CancelSubscriptionResponse = ({
    [key: string]: unknown;
});

export type CreateTaskData = {
    requestBody: TaskCreate;
};

export type CreateTaskResponse = (TaskResponse);

export type ListTasksData = {
    executionStatus?: (TaskExecutionStatus | null);
    historyLimit?: number;
    includeHistory?: boolean;
    limit?: number;
    skip?: number;
};

export type ListTasksResponse = (TaskList);

export type GetTaskData = {
    historyLimit?: number;
    includeHistory?: boolean;
    taskId: string;
};

export type GetTaskResponse = (TaskResponse);

export type UpdateTaskData = {
    requestBody: TaskUpdate;
    taskId: string;
};

export type UpdateTaskResponse = (TaskResponse);

export type DeleteTaskData = {
    taskId: string;
};

export type DeleteTaskResponse = (TaskDeleteResponse);

export type UpdateTaskEnableData = {
    enable?: boolean;
    taskId: string;
};

export type UpdateTaskEnableResponse = (TaskResponse);

export type StopTaskExecutionData = {
    conversationId: string;
    taskId: string;
};

export type StopTaskExecutionResponse = (TaskStopResponse);

export type GetTaskProgressData = {
    conversationId: string;
    taskId: string;
};

export type GetTaskProgressResponse = (TaskExecutionStatus);

export type GenerateData = {
    input: string;
};

export type GenerateResponse = (TaskTemplateResponse);

export type CreateTemplateData = {
    isDefault?: boolean;
    requestBody: TaskTemplateCreate;
};

export type CreateTemplateResponse = (TaskTemplateResponse);

export type ListTemplatesData = {
    category?: (TaskCategoryEnum | null);
    includeDefaults?: boolean;
    limit?: number;
    searchQuery?: (string | null);
    services?: (Array<TaskServiceEnum> | null);
    skip?: number;
};

export type ListTemplatesResponse = (TaskTemplateList);

export type GetTemplateData = {
    templateId: string;
};

export type GetTemplateResponse = (TaskTemplateResponse);

export type UpdateTemplateData = {
    requestBody: TaskTemplateUpdate;
    templateId: string;
};

export type UpdateTemplateResponse = (TaskTemplateResponse);

export type DeleteTemplateData = {
    templateId: string;
};

export type DeleteTemplateResponse = (unknown);

export type ScriptExecutionData = {
    script: string;
};

export type ScriptExecutionResponse2 = (ScriptExecutionResponse);

export type ReadUsersData = {
    limit?: number;
    skip?: number;
};

export type ReadUsersResponse = (UsersPublic);

export type CreateUserData = {
    requestBody: UserCreate;
};

export type CreateUserResponse = (UserPublic);

export type ReadUserMeResponse = (UserDetail);

export type DeleteUserMeResponse = (Message);

export type UpdateUserMeData = {
    requestBody: UserUpdateMe;
};

export type UpdateUserMeResponse = (UserPublic);

export type UpdatePasswordMeData = {
    requestBody: UpdatePassword;
};

export type UpdatePasswordMeResponse = (Message);

export type ReadUserByIdData = {
    userId: string;
};

export type ReadUserByIdResponse = (UserPublic);

export type UpdateUserData = {
    requestBody: UserUpdate;
    userId: string;
};

export type UpdateUserResponse = (UserPublic);

export type DeleteUserData = {
    userId: string;
};

export type DeleteUserResponse = (Message);

export type SwitchWorkspaceData = {
    workspaceId: string;
};

export type SwitchWorkspaceResponse = (Token);

export type TestEmailData = {
    emailTo: string;
};

export type TestEmailResponse = (unknown);

export type HealthCheckResponse = (boolean);

export type PublishMessageData = {
    requestBody: Message;
};

export type PublishMessageResponse = (unknown);

export type EnqueueMessageData = {
    requestBody: Message;
    taskName: string;
};

export type EnqueueMessageResponse = (unknown);

export type ReadWorkflowsData = {
    limit?: number;
    skip?: number;
};

export type ReadWorkflowsResponse = (WorkflowsPublic);

export type CreateWorkflowData = {
    requestBody: WorkflowCreate;
};

export type CreateWorkflowResponse = (WorkflowPublic);

export type ReadWorkflowData = {
    id: string;
};

export type ReadWorkflowResponse = (WorkflowPublic);

export type UpdateWorkflowData = {
    id: string;
    requestBody: WorkflowUpdate;
};

export type UpdateWorkflowResponse = (WorkflowPublic);

export type DeleteWorkflowData = {
    id: string;
};

export type DeleteWorkflowResponse = (Message);

export type CreateWorkflowFromTemplateData = {
    workspaceId: string;
};

export type CreateWorkflowFromTemplateResponse = (WorkflowPublic);

export type CreateWorkflowNodeData = {
    requestBody: WorkflowNodeCreate;
    workflowId: string;
};

export type CreateWorkflowNodeResponse = (WorkflowNodePublic);

export type ReadWorkflowNodesData = {
    limit?: number;
    skip?: number;
    workflowId: string;
};

export type ReadWorkflowNodesResponse = (Array<WorkflowNodePublic>);

export type ReadWorkflowNodeData = {
    nodeId: string;
    workflowId: string;
};

export type ReadWorkflowNodeResponse = (WorkflowNodePublic);

export type UpdateWorkflowNodeData = {
    nodeId: string;
    requestBody: WorkflowNodeUpdate;
    workflowId: string;
};

export type UpdateWorkflowNodeResponse = (WorkflowNodePublic);

export type DeleteWorkflowNodeData = {
    nodeId: string;
    workflowId: string;
};

export type DeleteWorkflowNodeResponse = (Message);

export type RunWorkflowNodeData = {
    nodeId: string;
    workflowId: string;
};

export type RunWorkflowNodeResponse = (Message);

export type RunWorkflowData = {
    workflowId: string;
};

export type RunWorkflowResponse = (Message);

export type ReadWorkspacesData = {
    limit?: number;
    skip?: number;
};

export type ReadWorkspacesResponse = (WorkspacesPublic);

export type CreateWorkspaceData = {
    requestBody: WorkspaceCreate;
};

export type CreateWorkspaceResponse = (WorkspacePublic);

export type ReadWorkspaceData = {
    id: string;
};

export type ReadWorkspaceResponse = (WorkspaceDetail);

export type UpdateWorkspaceData = {
    id: string;
    requestBody: WorkspaceUpdate;
};

export type UpdateWorkspaceResponse = (WorkspacePublic);

export type DeleteWorkspaceData = {
    id: string;
};

export type DeleteWorkspaceResponse = (Message);