import { BrandingSection } from "@/components/BrandingSection";
import UserAuthForm from "./user-auth-form";
import { cn } from "@/lib/utils";

interface SignInViewPageProps {
  slackOAuth?: boolean;
  appId?: string;
  teamId?: string;
}

export default function SignInViewPage({ slackOAuth, appId, teamId }: SignInViewPageProps) {
  return (
    <div className="relative h-screen flex-col items-center justify-center md:grid lg:max-w-none lg:grid-cols-2 lg:px-0">
      <BrandingSection
        title="The Future of Cloud Operations"
        description="Our advanced AI agents work tirelessly to help streamline operations, cut costs, and boost efficiency—empowering your team to focus on what matters most."
        image={{
          src: "/hero-banner.png",
          alt: "Cloud Thinker Dashboard Preview"
        }}
      />

      {/* Right side - Enhanced login form */}
      <div className="relative flex h-full min-h-screen items-center justify-center
                      lg:min-h-0 lg:p-8">
        {/* Background effects */}
        <div className="absolute inset-0
                      bg-[radial-gradient(ellipse_at_top,_var(--tw-gradient-stops))]
                      from-zinc-50 via-zinc-100 to-zinc-200
                      dark:from-zinc-900 dark:via-zinc-900 dark:to-black
                      transition-all duration-300"></div>

        {/* Main container */}
        <div className="relative w-full px-4 sm:px-6 lg:px-8 max-w-lg mx-auto">
          {/* Glass card container */}
          <div className={cn(
            // Base styles
            "relative overflow-hidden",
            "p-4 sm:p-6 lg:p-8 rounded-3xl",
            // Background and border
            "bg-white dark:bg-zinc-900",
            "border border-zinc-200 dark:border-zinc-800",
            // Shadow effects
            "shadow-lg dark:shadow-2xl-dark",
            // Transitions
            "transition-all duration-300"
          )}>
            {/* Welcome text */}
            <div className="space-y-2 text-center mb-6 lg:mb-8">
              <h1 className="text-2xl lg:text-3xl font-semibold tracking-tight
                           text-zinc-900 dark:text-zinc-100">
                {slackOAuth ? "Complete Your Slack Integration" : "Welcome Back!"}
              </h1>
              <p className="text-sm text-zinc-600 dark:text-zinc-400">
                {slackOAuth ? "Sign in to complete your Slack integration" : "Sign in to continue to CloudThinker"}
              </p>
            </div>

            {/* Auth form */}
            <div className="space-y-6">
              <UserAuthForm
                slackOAuth={slackOAuth}
                appId={appId}
                teamId={teamId}
              />
            </div>
          </div>

          {/* Terms and privacy links */}
          <div className="mt-6 text-center space-y-2 text-sm">
            <p className="text-xs text-zinc-500 dark:text-zinc-400">
              By continuing, you agree to our{' '}
              <a href="https://www.cloudthinker.io/terms-of-service" className="underline hover:text-zinc-900
                                   dark:hover:text-zinc-100
                                   transition-colors duration-200">
                Terms
              </a>
              {' '}and{' '}
              <a href="https://www.cloudthinker.io/privacy-policy" className="underline hover:text-zinc-900
                                   dark:hover:text-zinc-100
                                   transition-colors duration-200">
                Privacy Policy
              </a>
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
