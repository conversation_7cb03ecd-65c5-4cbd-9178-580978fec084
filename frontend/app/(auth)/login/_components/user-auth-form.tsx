'use client';
import * as z from 'zod';

import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import useAuth from '@/hooks/useAuth';
import { zodResolver } from '@hookform/resolvers/zod';
import { useTransition } from 'react';
import { useForm } from 'react-hook-form';
import type { Body_login_login_access_token as AccessToken } from '@/client';
import Link from 'next/link';
import { PagePath } from '@/constants/route';
import { GoogleButton } from '@/components/ui/google-button';
import { cn } from '@/lib/utils';
import { Icons } from '@/components/icons';

const formSchema = z.object({
  username: z.string().email({ message: 'Enter a valid email address' }),
  password: z
    .string()
    .min(8, { message: 'Password must be at least 8 characters' }),
  slackOAuth: z.boolean().optional(),
  appId: z.string().optional(),
  teamId: z.string().optional()
});

interface UserAuthFormProps {
  slackOAuth?: boolean;
  appId?: string;
  teamId?: string;
}

export default function UserAuthForm({ slackOAuth, appId, teamId }: UserAuthFormProps) {
  const [loading, startTransition] = useTransition();

  const { loginMutation, handleGoogleLogin, isGoogleLoading } = useAuth();

  const defaultValues = {
    username: '',
    password: '',
    slackOAuth: slackOAuth || false,
    appId: appId || '',
    teamId: teamId || ''
  };

  const form = useForm<AccessToken & { slackOAuth?: boolean; appId?: string; teamId?: string }>({
    resolver: zodResolver(formSchema),
    defaultValues
  });

  const onSubmit = async (data: AccessToken & { slackOAuth?: boolean; appId?: string; teamId?: string }) => {
    startTransition(async () => {
      try {
        await loginMutation.mutateAsync({
          ...data,
          slackOAuth: slackOAuth,
          appId: appId,
          teamId: teamId
        });
      } catch {
        // error is handled by useAuth hook
      }
    });
  };

  return (
    <div className="space-y-6">
      <Form {...form}>
        <form
          onSubmit={form.handleSubmit(onSubmit)}
          className="space-y-4"
        >
          <FormField
            control={form.control}
            name="username"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="text-zinc-700 dark:text-zinc-300">
                  Email
                </FormLabel>
                <FormControl>
                  <Input
                    type="email"
                    placeholder="<EMAIL>"
                    disabled={loading}
                    className={cn(
                      "bg-zinc-50 dark:bg-zinc-800",
                      "border-zinc-200 dark:border-zinc-700",
                      "text-zinc-900 dark:text-zinc-100",
                      "placeholder:text-zinc-500 dark:placeholder:text-zinc-400",
                      "focus:ring-2 focus:ring-blue-500/20 dark:focus:ring-blue-500/30"
                    )}
                    {...field}
                  />
                </FormControl>
                <FormMessage className="text-red-500 dark:text-red-400" />
              </FormItem>
            )}
          />

          <div className="space-y-2">
            <FormField
              control={form.control}
              name="password"
              render={({ field }) => (
                <FormItem>
                  <div className="flex items-center justify-between">
                    <FormLabel className="text-zinc-700 dark:text-zinc-300">
                      Password
                    </FormLabel>
                    <Link
                      className="text-sm text-blue-600 hover:text-blue-500 dark:text-blue-400 dark:hover:text-blue-300 transition-colors duration-200"
                      href={PagePath.ForgotPassword}
                    >
                      Forgot password?
                    </Link>
                  </div>
                  <FormControl>
                    <Input
                      type="password"
                      placeholder="••••••••"
                      disabled={loading}
                      className={cn(
                        "bg-zinc-50 dark:bg-zinc-800",
                        "border-zinc-200 dark:border-zinc-700",
                        "text-zinc-900 dark:text-zinc-100",
                        "placeholder:text-zinc-500 dark:placeholder:text-zinc-400",
                        "focus:ring-2 focus:ring-blue-500/20 dark:focus:ring-blue-500/30"
                      )}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage className="text-red-500 dark:text-red-400" />
                </FormItem>
              )}
            />
          </div>

          <Button
            disabled={loading}
            className={cn(
              "w-full",
              "bg-zinc-900 hover:bg-zinc-800",
              "dark:bg-zinc-100 dark:hover:bg-zinc-200",
              "text-white dark:text-zinc-900",
              "transition-colors duration-200"
            )}
            type="submit"
          >
            {loading && (
              <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
            )}
            Sign in
          </Button>

          <div className="space-y-2 text-center text-sm">
            <p className="text-zinc-600 dark:text-zinc-400">
              Don&apos;t have an account?{' '}
              <Link
                className={cn(
                  "text-blue-600 hover:text-blue-500",
                  "dark:text-blue-400 dark:hover:text-blue-300",
                  "transition-colors duration-200",
                  "font-medium"
                )}
                href={PagePath.Signup}
              >
                Sign up
              </Link>
            </p>
          </div>
        </form>
      </Form>

      {/* Divider */}
      <div className="relative">
        <div className="absolute inset-0 flex items-center">
          <span className="w-full border-t border-zinc-200 dark:border-zinc-700"></span>
        </div>
        <div className="relative flex justify-center text-xs uppercase">
          <span className="bg-white dark:bg-zinc-900 px-2 text-zinc-500 dark:text-zinc-400">
            Or continue with
          </span>
        </div>
      </div>

      <GoogleButton
        onClick={() => handleGoogleLogin(slackOAuth, appId, teamId)}
        isLoading={isGoogleLoading}
      />
    </div>
  );
}
