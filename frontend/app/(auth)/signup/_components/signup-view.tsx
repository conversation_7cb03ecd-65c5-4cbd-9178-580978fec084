import { Metadata } from 'next';
import UserSignUpForm from './user-signup-form';
import { cn } from '@/lib/utils';
import { BrandingSection } from '@/components/BrandingSection';

export const metadata: Metadata = {
  title: 'Sign Up - CloudThinker',
  description:
    'Create your CloudThinker account to start optimizing cloud costs.'
};

export default function SignUpViewPage() {
  return (
    <div className="relative h-screen flex-col items-center justify-center md:grid lg:max-w-none lg:grid-cols-2 lg:px-0">
      {' '}
      <BrandingSection
        title="The Future of Cloud Operations"
        description="Our advanced AI agents work tirelessly to help streamline operations, cut costs, and boost efficiency—empowering your team to focus on what matters most."
        image={{
          src: '/hero-banner.png',
          alt: 'Cloud Thinker Dashboard Preview'
        }}
      />
      {/* Enhanced right side with better styling */}
      <div
        className="relative flex h-full min-h-screen items-center justify-center
                      lg:min-h-0 lg:p-8"
      >
        {/* Background effects */}
        <div
          className="absolute inset-0
                      bg-[radial-gradient(ellipse_at_top,_var(--tw-gradient-stops))]
                      from-zinc-50 via-zinc-100 to-zinc-200
                      transition-all duration-300 dark:from-zinc-900
                      dark:via-zinc-900 dark:to-black"
        ></div>

        {/* Subtle animated gradient */}
        <div
          className="animate-gradient-slow absolute inset-0
                      bg-gradient-to-br from-blue-50/50 via-white/50
                      to-pink-50/50 dark:from-blue-900/10 dark:via-zinc-900/10
                      dark:to-purple-900/10"
        ></div>

        {/* Main container */}
        <div className="relative mx-auto w-full max-w-lg px-4 sm:px-6 lg:px-8">
          {/* Glass card container */}
          <div
            className={cn(
              // Base styles
              'relative overflow-hidden',
              'rounded-3xl p-4 sm:p-6 lg:p-8',
              // Background and border
              'bg-white/80 dark:bg-zinc-900/80',
              'backdrop-blur-xl',
              'border border-zinc-200/50 dark:border-zinc-800/50',
              // Shadow effects
              'shadow-[0_2px_4px_rgba(0,0,0,0.02),0_8px_24px_rgba(0,0,0,0.06)]',
              'dark:shadow-[0_2px_4px_rgba(0,0,0,0.2),0_8px_24px_rgba(0,0,0,0.3)]',
              // Transitions
              'transition-all duration-300'
            )}
          >
            {/* Welcome text */}
            <div className="mb-8 space-y-2 text-center">
              <h1
                className={cn(
                  'text-2xl font-semibold tracking-tight lg:text-3xl',
                  'bg-gradient-to-br from-zinc-900 via-zinc-700 to-zinc-900',
                  'dark:from-white dark:via-zinc-200 dark:to-white',
                  'bg-clip-text text-transparent',
                  'transition-all duration-300'
                )}
              >
                Create your account
              </h1>
              <p className="text-sm text-zinc-600 dark:text-zinc-400">
                Join CloudThinker to start optimizing your cloud costs
              </p>
            </div>

            {/* Sign Up form */}
            <div className="space-y-6">
              <UserSignUpForm />
            </div>
          </div>

          {/* Terms and privacy links */}
          <div className="mt-6 space-y-2 text-center">
            <p className="text-xs text-zinc-500 dark:text-zinc-400">
              By creating an account, you agree to our{' '}
              <a
                href="#"
                className="underline transition-colors
                                   duration-200
                                   hover:text-zinc-900 dark:hover:text-zinc-100"
              >
                Terms of Service
              </a>{' '}
              and{' '}
              <a
                href="#"
                className="underline transition-colors
                                   duration-200
                                   hover:text-zinc-900 dark:hover:text-zinc-100"
              >
                Privacy Policy
              </a>
            </p>
          </div>
        </div>

        {/* Bottom decoration */}
        <div
          className="absolute bottom-0 left-0 right-0 h-px
                       bg-gradient-to-r from-transparent
                       via-zinc-200 to-transparent dark:via-zinc-700"
        ></div>
      </div>
    </div>
  );
}
