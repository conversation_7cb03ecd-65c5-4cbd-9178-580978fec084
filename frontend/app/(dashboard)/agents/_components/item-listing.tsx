'use client';

import { AgentPublic } from '@/client';
import { useAgents } from '@/hooks/use-agents';
import { useAgentTools, Tool } from '@/hooks/use-agent-tools';
import { useMcpServers, DisplayMCPServerInfo } from '@/hooks/use-mcp-servers';
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from '@/components/ui/scroll-area';
import { AgentCard } from '@/components/agents/agent-card';
import { LoadingSkeleton } from '@/components/agents/loading-skeleton';
import Image from 'next/image';
import { createContext, useContext } from 'react';

// Create a context to share the data
interface AgentDataContextType {
  agentTools: Record<string, Tool[]>;
  agentMcpServerMap: Record<string, DisplayMCPServerInfo[]>;
}

export const AgentDataContext = createContext<AgentDataContextType | null>(null);

// Custom hook to use the context
export const useAgentData = () => {
  const context = useContext(AgentDataContext);
  if (!context) {
    throw new Error('useAgentData must be used within an AgentDataProvider');
  }
  return context;
};

interface ItemListingPageProps {
  page?: number;
  limit?: number;
}

const ItemListingPage = ({
  page = 1,
  limit = 10,
}: ItemListingPageProps) => {
  const { data: agentsResponse, isLoading } = useAgents({
    skip: (page - 1) * limit,
    limit
  });

  // Fetch data once at the top level
  const { agentTools } = useAgentTools();
  const { agentMcpServerMap } = useMcpServers();

  if (isLoading) {
    return (
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
        {[...Array(6)].map((_, i) => (
          <LoadingSkeleton key={i} />
        ))}
      </div>
    );
  }

  if (!agentsResponse?.data?.length) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[400px] text-muted-foreground">
        <div className="h-12 w-12 mb-4 rounded-full overflow-hidden">
          <Image
            src="/avatars/anna.webp"
            alt="Assistant Avatar"
            width={48}
            height={48}
            className="object-cover"
          />
        </div>
        <p className="text-lg">No agents found</p>
      </div>
    );
  }

  // Sort agents by title to maintain consistent order
  const sortedAgents = [...agentsResponse.data].sort((a, b) => {
    return a.title.localeCompare(b.title);
  });

  const autonomousAgents = sortedAgents.filter(
    (agent) => agent.type === 'autonomous_agent'
  );

  const conversationalAgents = sortedAgents.filter(
    (agent) => agent.type === 'conversation_agent'
  );

  // Provide the data through context
  const contextValue = {
    agentTools: agentTools || {},
    agentMcpServerMap: agentMcpServerMap || {}
  };

  return (
    <AgentDataContext.Provider value={contextValue}>
      <div className="flex flex-col h-[calc(100vh-10rem)] gap-4">
        {autonomousAgents.length > 0 && (
          <div className="flex flex-col gap-2">
            <div className="flex items-center gap-2">
              <h2 className="text-xl font-semibold tracking-tight">
                Group Chat
              </h2>
              <Badge variant="secondary" className="text-sm">
                {autonomousAgents.length}
              </Badge>
            </div>
            <ScrollArea className="h-auto">
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 pr-4">
                {autonomousAgents.map((item: AgentPublic) => (
                  <AgentCard
                    key={item.id}
                    item={item}
                  />
                ))}
              </div>
            </ScrollArea>
          </div>
        )}

        {conversationalAgents.length > 0 && (
          <div className="flex flex-col flex-1 min-h-0 gap-2">
            <div className="flex items-center gap-2">
              <h2 className="text-xl font-semibold tracking-tight">
                Team Members
              </h2>
              <Badge variant="secondary" className="text-sm">
                {conversationalAgents.length}
              </Badge>
            </div>
            <div className="flex-1 min-h-0">
              <ScrollArea className="h-full">
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 pb-4">
                  {conversationalAgents.map((item: AgentPublic) => (
                    <AgentCard
                      key={item.id}
                      item={item}
                    />
                  ))}
                </div>
              </ScrollArea>
            </div>
          </div>
        )}
      </div>
    </AgentDataContext.Provider>
  );
};

export default ItemListingPage;
