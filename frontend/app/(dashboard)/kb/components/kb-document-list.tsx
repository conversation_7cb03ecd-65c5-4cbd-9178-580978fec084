import React, { useState, useMemo } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ColumnDef } from '@tanstack/react-table';
import {
  Eye,
  Trash2,
  ExternalLink,
  ArrowUpDown,
  Globe,
  ChevronRight,
  ChevronDown,
  Folder,
  FolderOpen,
  CheckCircle2,
  Clock,
  Loader2,
  XCircle,
  Circle,
  File,
  ArrowDown
} from 'lucide-react';
import { FileTypeIcon } from './FileTypeIcon';
import { DateFormat, formatUtcDate } from '@/lib/date-utils';
import { cn } from '@/lib/utils';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { DataTable } from '@/components/ui/table/data-table';

interface DocumentKBRead {
  id: string;
  kb_id: string;
  name: string;
  type: 'url' | 'file';
  url?: string;
  deep_crawl: boolean;
  file_name?: string;
  file_type?: string;
  object_name?: string;
  embed_status: 'PENDING' | 'PROGRESS' | 'SUCCESS' | 'FAILURE';
  created_at: string;
  updated_at: string;
  is_deleted: boolean;
  size?: number; // May not always be available
  parent_id?: string | null;
  children?: DocumentKBRead[];
  isDeleting?: boolean;
}

// Extended interface for tree display
interface TreeDocumentItem extends DocumentKBRead {
  level: number; // Indentation level
  isExpanded?: boolean; // For parents with children
  hasChildren: boolean; // Whether this item has children
}

interface KBDocumentListProps {
  documents: DocumentKBRead[];
  isLoading: boolean;
  totalDocuments: number;
  onView: (document: DocumentKBRead) => void;
  onDelete: (document: DocumentKBRead) => void;
  onPaginate: (skip: number, limit: number) => void;
  skip: number;
  limit: number;
  searchQuery?: string;
  showExpandControls?: boolean;
  onExpandAll?: () => void;
  onCollapseAll?: () => void;
  // External state management
  expandedFolders?: Set<string>;
  setExpandedFolders?: (folders: Set<string> | ((prev: Set<string>) => Set<string>)) => void;
}

function EmbedStatusBadge({ status }: { status: string }) {
  const getStatusConfig = (status: string) => {
    switch (status.toLowerCase()) {
      case 'completed':
      case 'success':
        return {
          variant: 'default' as const,
          label: 'Completed',
          className: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300',
          showLoader: false,
          icon: 'CheckCircle2'
        };
      case 'pending':
        return {
          variant: 'secondary' as const,
          label: 'Pending',
          className: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300',
          showLoader: false,
          icon: 'Clock'
        };
      case 'progress':
      case 'in_progress':
        return {
          variant: 'outline' as const,
          label: 'Processing',
          className: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300',
          showLoader: true,
          icon: 'Loader2'
        };
      case 'failed':
      case 'failure':
        return {
          variant: 'destructive' as const,
          label: 'Failed',
          className: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300',
          showLoader: false,
          icon: 'XCircle'
        };
      default:
        return {
          variant: 'outline' as const,
          label: status,
          className: '',
          showLoader: false,
          icon: 'Circle'
        };
    }
  };

  const config = getStatusConfig(status);

  // Import icons dynamically based on config
  const iconMap = {
    CheckCircle2: <CheckCircle2 className="h-3 w-3 flex-shrink-0" />,
    Clock: <Clock className="h-3 w-3 flex-shrink-0" />,
    Loader2: <Loader2 className="h-3 w-3 flex-shrink-0 animate-spin" />,
    XCircle: <XCircle className="h-3 w-3 flex-shrink-0" />,
    Circle: <Circle className="h-3 w-3 flex-shrink-0" />
  };

  return (
    <Badge variant={config.variant} className={cn('text-xs font-medium flex items-center gap-1 w-fit', config.className)}>
      {iconMap[config.icon as keyof typeof iconMap]}
      <span>{config.label}</span>
    </Badge>
  );
}

function DocumentTypeBadge({ type, isDeepCrawl }: { type: string; isDeepCrawl?: boolean }) {
  if (type === 'url') {
    return (
      <div className="flex items-center gap-1">
        <Badge variant="outline" className="text-xs bg-green-50 text-green-700 border-green-200 dark:bg-green-950 dark:text-green-300 dark:border-green-800 flex items-center gap-1">
          <Globe className="h-3 w-3 flex-shrink-0" />
          <span>URL</span>
        </Badge>
        {isDeepCrawl && (
          <Badge variant="secondary" className="text-xs bg-purple-50 text-purple-700 border-purple-200 dark:bg-purple-950 dark:text-purple-300 flex items-center gap-1">
            <ArrowDown className="h-3 w-3 flex-shrink-0" />
            <span>Deep</span>
          </Badge>
        )}
      </div>
    );
  }

  return (
    <Badge variant="outline" className="text-xs bg-gray-50 text-gray-700 border-gray-200 dark:bg-gray-950 dark:text-gray-300 dark:border-gray-800 flex items-center gap-1">
      <File className="h-3 w-3 flex-shrink-0" />
      <span>File</span>
    </Badge>
  );
}

export function KBDocumentList({
  documents,
  isLoading,
  totalDocuments,
  onView,
  onDelete,
  onPaginate,
  skip,
  limit,
  searchQuery,
  showExpandControls = true,
  onExpandAll,
  onCollapseAll,
  expandedFolders: externalExpandedFolders,
  setExpandedFolders: externalSetExpandedFolders
}: KBDocumentListProps) {
  // State for expanded folders (use external state if provided, otherwise local state)
  const [internalExpandedFolders, setInternalExpandedFolders] = useState<Set<string>>(new Set());

  const expandedFolders = externalExpandedFolders ?? internalExpandedFolders;
  const setExpandedFolders = externalSetExpandedFolders ?? setInternalExpandedFolders;

  // COMMENTED OUT: Folder functionality disabled - no auto-expand needed
  // Auto-expand folders when searching to show matching children
  // React.useEffect(() => {
  //   if (searchQuery && searchQuery.trim()) {
  //     // Find all folders that contain matching items
  //     const foldersWithMatches = new Set<string>();

  //     const findMatchingFolders = (docs: DocumentKBRead[]): void => {
  //       docs.forEach(doc => {
  //         if (doc.children && doc.children.length > 0) {
  //           // Check if this folder or any child matches the search
  //           const hasMatchingChild = doc.children.some(child =>
  //             child.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
  //             child.file_name?.toLowerCase().includes(searchQuery.toLowerCase())
  //           );

  //           if (hasMatchingChild || doc.name.toLowerCase().includes(searchQuery.toLowerCase())) {
  //             foldersWithMatches.add(doc.id);
  //           }

  //           // Recursively check children
  //           findMatchingFolders(doc.children);
  //         }
  //       });
  //     };

  //     findMatchingFolders(documents);
  //     setExpandedFolders(foldersWithMatches);
  //   }
  // }, [searchQuery, documents]);

  // COMMENTED OUT: Folder functionality disabled
  // Expand all folders
  // const expandAll = React.useCallback(() => {
  //   const allFolderIds = documents
  //     .filter(doc => doc.children && doc.children.length > 0)
  //     .map(doc => doc.id);
  //   setExpandedFolders(new Set(allFolderIds));
  //   onExpandAll?.();
  // }, [documents, onExpandAll]);

  // Collapse all folders
  // const collapseAll = React.useCallback(() => {
  //   setExpandedFolders(new Set());
  //   onCollapseAll?.();
  // }, [onCollapseAll]);

  // Transform flat document list into hierarchical tree structure
  // COMMENTED OUT: Folder functionality causing pagination issues
  // const hierarchicalData = useMemo(() => {
  //   if (!documents || documents.length === 0) return [];

  //   // Build tree structure with proper nesting
  //   const buildTree = (docs: DocumentKBRead[], level = 0, parentPath = ''): TreeDocumentItem[] => {
  //     const result: TreeDocumentItem[] = [];

  //     for (const doc of docs) {
  //       const hasChildren = Boolean(doc.children && doc.children.length > 0);
  //       const treeItem: TreeDocumentItem = {
  //         ...doc,
  //         level,
  //         hasChildren,
  //         isExpanded: expandedFolders.has(doc.id)
  //       };

  //       result.push(treeItem);

  //       // If expanded and has children, add them recursively
  //       if (hasChildren && expandedFolders.has(doc.id) && doc.children) {
  //         const childTree = buildTree(doc.children, level + 1, `${parentPath}/${doc.name}`);
  //         result.push(...childTree);
  //       }
  //     }

  //     return result;
  //   };

  //   // Start with root documents (documents without parent_id) and build tree
  //   const rootDocuments = documents.filter(doc => !doc.parent_id);

  //   // If we have any documents with parent_id but no matching parent in the list,
  //   // treat them as root documents too (orphaned items)
  //   const documentIds = new Set(documents.map(doc => doc.id));
  //   const orphanedDocuments = documents.filter(doc =>
  //     doc.parent_id && !documentIds.has(doc.parent_id)
  //   );

  //   const allRootDocuments = [...rootDocuments, ...orphanedDocuments];

  //   return buildTree(allRootDocuments);
  // }, [documents, expandedFolders]);

  // Flattened data for simpler pagination
  const flattenedData = useMemo(() => {
    if (!documents || documents.length === 0) return [];

    // Flatten all documents recursively to display all items
    const flattenDocuments = (docs: DocumentKBRead[]): TreeDocumentItem[] => {
      const result: TreeDocumentItem[] = [];

      for (const doc of docs) {
        // Add the document itself
        result.push({
          ...doc,
          level: 0, // Set all to level 0 for flat display
          hasChildren: false, // Disable folder functionality
          isExpanded: false
        });

        // Add children recursively if they exist
        if (doc.children && doc.children.length > 0) {
          result.push(...flattenDocuments(doc.children));
        }
      }

      return result;
    };

    return flattenDocuments(documents);
  }, [documents]);

  // Toggle folder expansion - COMMENTED OUT: Folder functionality disabled
  // const toggleFolder = (documentId: string) => {
  //   setExpandedFolders(prev => {
  //     const newSet = new Set(prev);
  //     if (newSet.has(documentId)) {
  //       newSet.delete(documentId);
  //     } else {
  //       newSet.add(documentId);
  //     }
  //     return newSet;
  //   });
  // };

  // Handle row click for folders - COMMENTED OUT: Folder functionality disabled
  // const handleRowClick = (document: TreeDocumentItem) => {
  //   if (document.hasChildren) {
  //     toggleFolder(document.id);
  //   }
  // };

  // COMMENTED OUT: Folder functionality disabled - no keyboard navigation needed
  // Keyboard navigation support
  // React.useEffect(() => {
  //   const handleKeyDown = (event: KeyboardEvent) => {
  //     // Only handle keyboard shortcuts when not in search mode and not focused on an input
  //     if (searchQuery || (event.target as HTMLElement)?.tagName === 'INPUT') {
  //       return;
  //     }

  //     switch (event.key) {
  //       case 'e':
  //       case 'E':
  //         // Expand all folders
  //         if (!event.ctrlKey && !event.metaKey) {
  //           event.preventDefault();
  //           expandAll();
  //         }
  //         break;
  //       case 'c':
  //       case 'C':
  //         // Collapse all folders
  //         if (!event.ctrlKey && !event.metaKey) {
  //           event.preventDefault();
  //           collapseAll();
  //         }
  //         break;
  //     }
  //   };

  //   document.addEventListener('keydown', handleKeyDown);
  //   return () => document.removeEventListener('keydown', handleKeyDown);
  // }, [searchQuery, expandAll, collapseAll]);

  // Create columns for the DataTable with hierarchical support
  const columns: ColumnDef<TreeDocumentItem>[] = [
    {
      accessorKey: 'name',
      header: 'Name',
      size: 450,
      minSize: 350,
      cell: ({ row }) => {
        const document = row.original;
        const highlightMatch = (text: string, query: string | undefined): React.ReactNode => {
          if (!query || !text) return text;

          const regex = new RegExp(`(${query.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'gi');
          const parts = text.split(regex);

          return parts.map((part, index) =>
            regex.test(part) ? (
              <mark key={index} className="bg-yellow-200 dark:bg-yellow-800 rounded px-0.5">
                {part}
              </mark>
            ) : (
              part
            )
          );
        };

        // Calculate indentation based on level
        const indentStyle = { marginLeft: `${document.level * 20}px` };

        return (
          <div
            className={cn(
              "flex items-center gap-1 w-full min-w-[300px]"
              // COMMENTED OUT: Folder functionality disabled
              // document.hasChildren && "cursor-pointer hover:bg-muted/30 rounded transition-colors"
            )}
            style={indentStyle}
            // COMMENTED OUT: Folder functionality disabled
            // onClick={(e) => {
            //   if (document.hasChildren) {
            //     e.stopPropagation();
            //     handleRowClick(document);
            //   }
            // }}
          >
            {/* Expand/collapse button for parents - COMMENTED OUT: Folder functionality disabled */}
            {/* {document.hasChildren ? (
              <Button
                variant="ghost"
                size="sm"
                className="h-5 w-5 p-0 hover:bg-muted/50 transition-colors"
                onClick={(e) => {
                  e.stopPropagation();
                  toggleFolder(document.id);
                }}
              >
                {document.isExpanded ? (
                  <ChevronDown className="h-3 w-3 text-muted-foreground hover:text-foreground" />
                ) : (
                  <ChevronRight className="h-3 w-3 text-muted-foreground hover:text-foreground" />
                )}
              </Button>
            ) : (
              <div className="w-5 flex items-center justify-center">
                {/* Connection line for child items */}
                {/* {document.level > 0 && (
                  <div className="w-px h-4 bg-border/40" />
                )} */}
              {/* </div>
            )} */}

            {/* Simplified icon display without folder functionality */}
            <div className="w-5 flex items-center justify-center">
              {/* No special folder handling - all documents are treated equally */}
            </div>

            {/* File/Folder icon - COMMENTED OUT: Folder functionality disabled */}
            {/* {document.hasChildren ? (
              document.isExpanded ? (
                <FolderOpen className="h-4 w-4 text-blue-600 dark:text-blue-400 flex-shrink-0 ml-1" />
              ) : (
                <Folder className="h-4 w-4 text-blue-600 dark:text-blue-400 flex-shrink-0 ml-1" />
              )
            ) : (
              <FileTypeIcon
                filename={document.file_name || document.name}
                className="h-4 w-4 flex-shrink-0 ml-1"
                documentType={document.type}
              />
            )} */}

            {/* Simplified file icon display - all documents are files */}
            <FileTypeIcon
              filename={document.file_name || document.name}
              className="h-4 w-4 flex-shrink-0 ml-1"
              documentType={document.type}
            />

            <div className="flex-1 min-w-0 ml-2">
              <div className="flex items-center gap-2">
                <div className={cn(
                  "break-all",
                  // COMMENTED OUT: Folder functionality disabled - all documents are treated as files
                  // document.hasChildren ? "font-medium text-foreground" : "font-normal text-foreground"
                  "font-normal text-foreground"
                )} title={document.name}>
                  {highlightMatch(document.name, searchQuery)}
                </div>
                {document.url && (
                  // COMMENTED OUT: Folder functionality disabled - hasChildren check removed
                  // document.url && !document.hasChildren && (
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="h-5 w-5 p-0 opacity-60 hover:opacity-100 transition-opacity"
                          onClick={(e) => {
                            e.stopPropagation();
                            if (document.url) {
                              window.open(document.url, '_blank', 'noopener,noreferrer');
                            }
                          }}
                        >
                          <ExternalLink className="h-3 w-3" />
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent>
                        Open URL: {document.url}
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                )}
              </div>
              {document.url && (
                // COMMENTED OUT: Folder functionality disabled - hasChildren check removed
                // document.url && !document.hasChildren && (
                <div className="text-xs text-muted-foreground break-all mt-0.5" title={document.url}>
                  {document.url}
                </div>
              )}
            </div>
          </div>
        );
      }
    },
    {
      accessorKey: 'type',
      header: () => <div className="text-center">Type</div>,
      size: 100,
      cell: ({ row }) => (
        <div className="flex justify-center">
          {/* COMMENTED OUT: Folder functionality disabled - all documents are treated as files */}
          {/* {row.original.hasChildren ? (
            <Badge variant="outline" className="text-xs bg-blue-50 text-blue-700 border-blue-200 dark:bg-blue-950 dark:text-blue-300 flex items-center gap-1">
              <Folder className="h-3 w-3 flex-shrink-0" />
              <span>Folder</span>
            </Badge>
          ) : (
            <DocumentTypeBadge type={row.original.type} isDeepCrawl={row.original.deep_crawl} />
          )} */}

          {/* Simplified type display - all documents are files */}
          <DocumentTypeBadge type={row.original.type} isDeepCrawl={row.original.deep_crawl} />
        </div>
      )
    },
    {
      accessorKey: 'embed_status',
      header: () => <div className="text-center">Status</div>,
      size: 120,
      cell: ({ row }) => {
        const document = row.original;
        // COMMENTED OUT: Folder functionality disabled - all documents show status
        // Don't show status for folders
        // if (document.hasChildren) {
        //   return <div className="flex justify-center">-</div>;
        // }

        return (
          <div className="flex justify-center">
            <EmbedStatusBadge status={document.embed_status} />
          </div>
        );
      }
    },
    {
      accessorKey: 'created_at',
      header: 'Created',
      size: 120,
      cell: ({ row }) => (
        <span className="text-sm text-muted-foreground">
          {formatUtcDate(row.original.created_at, DateFormat.SHORT_DATE)}
        </span>
      )
    },
    {
      id: 'actions',
      size: 250,
      cell: ({ row }) => {
        const document = row.original;
        return (
          <div className="flex items-center justify-end gap-2">
            {/* COMMENTED OUT: Folder functionality disabled - all documents show actions */}
            {/* {!document.hasChildren && ( */}
              <>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => onView(document)}
                >
                  <Eye className="h-4 w-4 mr-2" />
                  View
                </Button>

                {document.url && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={(e) => {
                      e.stopPropagation();
                      if (document.url) {
                        window.open(document.url, '_blank', 'noopener,noreferrer');
                      }
                    }}
                  >
                    <ExternalLink className="h-4 w-4 mr-2" />
                    Open
                  </Button>
                )}

                <Button
                  variant="ghost"
                  size="sm"
                  className="text-destructive hover:text-destructive hover:bg-destructive/10"
                  onClick={() => onDelete(document)}
                  disabled={document.isDeleting}
                >
                  {document.isDeleting ? (
                    <div className="h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent mr-2" />
                  ) : (
                    <Trash2 className="h-4 w-4 mr-2" />
                  )}
                  Delete
                </Button>
              </>
            {/* )} */}
          </div>
        );
      }
    }
  ];

  const currentPage = Math.floor(skip / limit);

  // Calculate total pages based on actual document count, not flattened data
  const totalPages = Math.ceil(totalDocuments / limit);

  // COMMENTED OUT: Folder functionality disabled
  // Get folder statistics
  // const folderStats = useMemo(() => {
  //   const allFolders = documents.filter(doc => doc.children && doc.children.length > 0);
  //   return {
  //     total: allFolders.length,
  //     expanded: expandedFolders.size
  //   };
  // }, [documents, expandedFolders]);

  // Simplified folder statistics - no folders in flattened view
  const folderStats = useMemo(() => ({
    total: 0,
    expanded: 0
  }), []);

  return (
    <div className="flex flex-col flex-1 min-h-0">
      {/* COMMENTED OUT: Folder functionality disabled */}
      {/* Expand/Collapse controls are now passed up to parent component */}
      {/* {showExpandControls && folderStats.total > 0 && (
        <div className="hidden">
          {/* Hidden controls - functionality moved to search area */}
          {/* <Button onClick={expandAll}>Expand All</Button>
          <Button onClick={collapseAll}>Collapse All</Button>
        </div>
      )} */}

      <DataTable
        columns={columns}
        data={flattenedData}
        totalItems={totalDocuments}
        loading={isLoading}
        customScrollClass="h-[calc(100vh-370px)] rounded-xl border overflow-hidden"
        currentPage={currentPage + 1}
        pageSize={limit}
        onPageChange={(page) => onPaginate((page - 1) * limit, limit)}
        onPageSizeChange={(newSize) => onPaginate(0, newSize)}
        displayPagination={true}
      />
    </div>
  );
}
