import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Switch } from '@/components/ui/switch';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Globe, Plus, X, Link as LinkIcon, Settings } from 'lucide-react';

const urlSchema = z
  .string()
  .min(1, 'URL is required')
  .max(2048, 'URL is too long (maximum 2048 characters)')
  .url('Must be a valid URL')
  .refine((url) => url.startsWith('http://') || url.startsWith('https://'), {
    message: 'URL must start with http:// or https://'
  })
  .refine((url) => {
    try {
      const urlObj = new URL(url);

      // Block localhost and private IP ranges for security
      const hostname = urlObj.hostname.toLowerCase();

      // Block localhost variations
      if (hostname === 'localhost' || hostname === '127.0.0.1' || hostname === '::1') {
        return false;
      }

      // Block private IP ranges (basic check)
      if (hostname.match(/^10\./) ||
          hostname.match(/^192\.168\./) ||
          hostname.match(/^172\.(1[6-9]|2[0-9]|3[0-1])\./) ||
          hostname.match(/^169\.254\./)) {
        return false;
      }

      // Block file:// and other dangerous protocols
      if (!['http:', 'https:'].includes(urlObj.protocol)) {
        return false;
      }

      // Block URLs with suspicious patterns
      const suspiciousPatterns = [
        /javascript:/i,
        /data:/i,
        /vbscript:/i,
        /<script/i,
        /onload=/i,
        /onerror=/i
      ];

      for (const pattern of suspiciousPatterns) {
        if (pattern.test(url)) {
          return false;
        }
      }

      return true;
    } catch {
      return false;
    }
  }, {
    message: 'URL is not allowed (localhost, private IPs, or suspicious content detected)'
  })
  .refine((url) => {
    // Additional length check for different URL parts
    try {
      const urlObj = new URL(url);

      // Check hostname length
      if (urlObj.hostname.length > 253) {
        return false;
      }

      // Check path length
      if (urlObj.pathname.length > 1000) {
        return false;
      }

      return true;
    } catch {
      return false;
    }
  }, {
    message: 'URL components are too long'
  });

const formSchema = z.object({
  url: urlSchema,
  deep_crawl: z.boolean().default(false)
});

type FormValues = z.infer<typeof formSchema>;

interface WebsiteItem {
  id: string;
  url: string;
  deep_crawl: boolean;
}

interface KBWebsiteIngestionDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSubmit: (values: { urls: string[]; deep_crawls: boolean[] }) => void;
  isProcessing: boolean;
}

export function KBWebsiteIngestionDialog({
  open,
  onOpenChange,
  onSubmit,
  isProcessing
}: KBWebsiteIngestionDialogProps) {
  const [websites, setWebsites] = useState<WebsiteItem[]>([]);

  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      url: '',
      deep_crawl: false
    }
  });

  // Reset state when dialog is opened/closed
  useEffect(() => {
    if (!open && !isProcessing) {
      setWebsites([]);
      form.reset({
        url: '',
        deep_crawl: false
      });
    }
  }, [open, isProcessing, form]);

  const addWebsite = (values: FormValues) => {
    const newWebsite: WebsiteItem = {
      id: Math.random().toString(36).substring(2, 9),
      url: values.url,
      deep_crawl: values.deep_crawl
    };

    setWebsites(prev => [...prev, newWebsite]);
    form.reset();
  };

  const removeWebsite = (id: string) => {
    if (!isProcessing) {
      setWebsites(prev => prev.filter(w => w.id !== id));
    }
  };

  const toggleDeepCrawl = (id: string) => {
    if (!isProcessing) {
      setWebsites(prev =>
        prev.map(w =>
          w.id === id ? { ...w, deep_crawl: !w.deep_crawl } : w
        )
      );
    }
  };

  const handleFinalSubmit = () => {
    if (websites.length === 0) return;

    const urls = websites.map(w => w.url);
    const deep_crawls = websites.map(w => w.deep_crawl);

    onSubmit({ urls, deep_crawls });
  };

  // Handler to prevent closing dialog while processing
  const handleOpenChange = (newOpen: boolean) => {
    if (!isProcessing || newOpen) {
      onOpenChange(newOpen);
      if (!newOpen) {
        // Reset form and websites when dialog closes
        setWebsites([]);
        form.reset({
          url: '',
          deep_crawl: false
        });
      }
    }
  };

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogContent className="w-[95vw] max-w-2xl max-h-[90vh] flex flex-col">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Globe className="h-5 w-5" />
            Add Website Content
          </DialogTitle>
          <DialogDescription>
            Add website URLs to extract their content into your knowledge base.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
                    {/* Add URL Form */}
          <div className="card p-4 rounded-3xl">
            <Form {...form}>
              <form onSubmit={form.handleSubmit(addWebsite)} className="space-y-4">
                <FormField
                  control={form.control}
                  name="url"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Website URL</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="https://example.com/documentation"
                          {...field}
                          disabled={isProcessing}
                          className="rounded-3xl"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="flex items-center justify-between">
                  <FormField
                    control={form.control}
                    name="deep_crawl"
                    render={({ field }) => (
                      <FormItem className="flex items-center space-x-3">
                        <FormControl>
                          <Switch
                            checked={field.value}
                            onCheckedChange={field.onChange}
                            id="deep-crawl"
                            disabled={isProcessing}
                          />
                        </FormControl>
                        <div className="space-y-0.5">
                          <FormLabel htmlFor="deep-crawl" className="text-sm font-medium cursor-pointer">
                            Deep crawl
                          </FormLabel>
                          <FormDescription className="text-xs">
                            {field.value ? 'Extract content from linked pages' : 'Extract only this page'}
                          </FormDescription>
                        </div>
                      </FormItem>
                    )}
                  />

                  <Button
                    type="submit"
                    disabled={isProcessing}
                    className="flex items-center gap-2 rounded-3xl"
                  >
                    <Plus className="h-4 w-4" />
                    Add URL
                  </Button>
                </div>
              </form>
            </Form>
          </div>

          {/* Website List */}
          {websites.length > 0 && (
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <h3 className="text-sm font-medium flex items-center gap-2">
                  <LinkIcon className="h-4 w-4" />
                  Ready to Process ({websites.length})
                </h3>
                <Badge variant="secondary" className="text-xs">
                  {websites.filter(w => w.deep_crawl).length} deep crawl
                </Badge>
              </div>

              <ScrollArea className="max-h-[300px] border rounded-3xl">
                <div className="p-3 space-y-2">
                  {websites.map((website) => (
                    <div
                      key={website.id}
                      className="flex items-center gap-3 p-3 border rounded-3xl bg-card hover:bg-muted/50 transition-colors"
                    >
                      <Globe className="h-4 w-4 text-muted-foreground flex-shrink-0" />

                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium truncate" title={website.url}>
                          {website.url}
                        </p>
                        <div className="flex items-center gap-2 mt-1">
                          <Badge variant={website.deep_crawl ? "default" : "outline"} className="text-xs rounded-3xl">
                            {website.deep_crawl ? "Deep crawl" : "Single page"}
                          </Badge>
                        </div>
                      </div>

                      <div className="flex items-center gap-1">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => toggleDeepCrawl(website.id)}
                          disabled={isProcessing}
                          className="h-8 px-2 text-xs rounded-3xl"
                        >
                          <Settings className="h-3 w-3 mr-1" />
                          Toggle
                        </Button>

                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => removeWebsite(website.id)}
                          disabled={isProcessing}
                          className="h-8 w-8 text-muted-foreground hover:text-destructive rounded-3xl"
                          aria-label="Remove website"
                        >
                          <X className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </ScrollArea>
            </div>
          )}

          {websites.length === 0 && (
            <div className="text-center py-8 text-muted-foreground">
              <LinkIcon className="h-8 w-8 mx-auto mb-2 opacity-50" />
              <p className="text-sm">No websites added yet</p>
            </div>
          )}
        </div>

        <DialogFooter className="flex-shrink-0">
          <Button
            type="button"
            variant="outline"
            onClick={() => handleOpenChange(false)}
            disabled={isProcessing}
            className="rounded-3xl"
          >
            Cancel
          </Button>
          <Button
            onClick={handleFinalSubmit}
            disabled={isProcessing || websites.length === 0}
            className="rounded-3xl"
          >
            {isProcessing ? 'Processing...' : `Process ${websites.length} Website${websites.length === 1 ? '' : 's'}`}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
