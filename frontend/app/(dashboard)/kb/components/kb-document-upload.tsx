import { useState, useCallback } from 'react';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { UploadCloud, X, File, AlertCircle, CheckCircle } from 'lucide-react';
import { formatBytes } from '@/lib/utils';
import { useDropzone } from 'react-dropzone';
import { KnowledgeBaseService } from '@/client';
import type { FileInfo, UploadedFileInfo } from '@/client';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { FileTypeIcon } from './FileTypeIcon';
import { toast } from 'sonner';

interface UploadFile {
  id: string;
  file: File;
  progress: number;
  status: 'pending' | 'uploading' | 'success' | 'error';
  error?: string;
  presignedUrl?: string;
  storageKey?: string;
}

interface KBDocumentUploadProps {
  kbId: string;
  onUpload: (uploadedFiles: UploadedFileInfo[]) => Promise<void>;
  isUploading: boolean;
  onClose?: () => void;
}

export function KBDocumentUpload({ kbId, onUpload, isUploading, onClose }: KBDocumentUploadProps) {
  const [uploadFiles, setUploadFiles] = useState<UploadFile[]>([]);

  const onDrop = useCallback((acceptedFiles: File[]) => {
    const newFiles = acceptedFiles.map((file) => ({
      id: Math.random().toString(36).substring(2, 9),
      file,
      progress: 0,
      status: 'pending' as const,
    }));
    setUploadFiles((prev) => [...prev, ...newFiles]);
  }, []);

  const onDropRejected = useCallback((rejectedFiles: any[]) => {
    // Group errors by type
    const tooLargeFiles: { name: string; size: number }[] = [];
    const invalidTypeFiles: string[] = [];
    const otherErrorFiles: { name: string; message: string }[] = [];

    rejectedFiles.forEach((rejectedFile) => {
      const { file, errors } = rejectedFile;
      errors.forEach((error: any) => {
        if (error.code === 'file-too-large') {
          tooLargeFiles.push({ name: file.name, size: file.size });
        } else if (error.code === 'file-invalid-type') {
          invalidTypeFiles.push(file.name);
        } else {
          otherErrorFiles.push({ name: file.name, message: error.message });
        }
      });
    });

    // Show grouped toast messages with slight delays to prevent overlap
    let delay = 0;

    if (tooLargeFiles.length > 0) {
      setTimeout(() => {
        if (tooLargeFiles.length === 1) {
          const file = tooLargeFiles[0];
          toast.error(`File "${file.name}" is too large`, {
            description: `Maximum file size is ${formatBytes(10 * 1024 * 1024)}. This file is ${formatBytes(file.size)}.`
          });
        } else {
          const fileNames = tooLargeFiles.map(f => f.name).join(', ');
          toast.error(`${tooLargeFiles.length} files are too large`, {
            description: `Maximum file size is ${formatBytes(10 * 1024 * 1024)}. Files: ${fileNames}`
          });
        }
      }, delay);
      delay += 100;
    }

    if (invalidTypeFiles.length > 0) {
      setTimeout(() => {
        if (invalidTypeFiles.length === 1) {
          toast.error(`File "${invalidTypeFiles[0]}" has an invalid type`, {
            description: 'Supported formats: PDF, DOCX, DOC, TXT, MD, HTML, RTF, CSV, JSON, EPUB, IPYNB, HWP, MP3, MP4, PPT, PPTX, PPTM'
          });
        } else {
          const fileNames = invalidTypeFiles.join(', ');
          toast.error(`${invalidTypeFiles.length} files have invalid types`, {
            description: `Supported formats: PDF, DOCX, DOC, TXT, MD, HTML, RTF, CSV, JSON, EPUB, IPYNB, HWP, MP3, MP4, PPT, PPTX, PPTM. Files: ${fileNames}`
          });
        }
      }, delay);
      delay += 100;
    }

    if (otherErrorFiles.length > 0) {
      setTimeout(() => {
        if (otherErrorFiles.length === 1) {
          const file = otherErrorFiles[0];
          toast.error(`File "${file.name}" was rejected`, {
            description: file.message
          });
        } else {
          const fileNames = otherErrorFiles.map(f => f.name).join(', ');
          toast.error(`${otherErrorFiles.length} files were rejected`, {
            description: `Files: ${fileNames}`
          });
        }
      }, delay);
    }
  }, []);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    onDropRejected,
    accept: {
      // Documents
      'application/pdf': ['.pdf'],
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx'],
      'application/msword': ['.doc'],
      'text/plain': ['.txt'],
      'text/markdown': ['.md'],
      'text/html': ['.html', '.htm'],
      'application/rtf': ['.rtf'],

      // Data formats
      'text/csv': ['.csv'],
      'application/json': ['.json'],

      // E-books and notebooks
      'application/epub+zip': ['.epub'],
      'application/x-ipynb+json': ['.ipynb'],

      // Korean documents
      'application/x-hwp': ['.hwp'],
      'application/haansofthwp': ['.hwp'],

      // Audio/Video (if supported)
      'audio/mpeg': ['.mp3'],
      'video/mp4': ['.mp4'],

      // Presentations
      'application/vnd.ms-powerpoint': ['.ppt'],
      'application/vnd.openxmlformats-officedocument.presentationml.presentation': ['.pptx'],
      'application/vnd.ms-powerpoint.presentation.macroEnabled.12': ['.pptm']
    },
    maxSize: 10 * 1024 * 1024, // 10MB
    disabled: isUploading
  });

  const removeFile = (id: string) => {
    if (!isUploading) {
      setUploadFiles((prev) => prev.filter((file) => file.id !== id));
    }
  };

  const handleUpload = async () => {
    const filesToUpload = uploadFiles.filter((file) => file.status === 'pending');

    if (filesToUpload.length === 0) return;

    try {
      // Step 1: Generate presigned URLs
      const fileInfos: FileInfo[] = filesToUpload.map((file) => ({
        file_id: file.id,
        filename: file.file.name,
        content_type: file.file.type,
        file_size: file.file.size,
      }));

      const presignedResponse = await KnowledgeBaseService.generatePresignedUrls({
        kbId,
        requestBody: {
          kb_id: kbId,
          files: fileInfos,
        },
      });

      // Update files with presigned URLs
      const updatedFiles = filesToUpload.map((file) => {
        const presignedInfo = presignedResponse.presigned_urls.find(
          (info) => info.file_id === file.id
        );
        return {
          ...file,
          presignedUrl: presignedInfo?.presigned_url,
          storageKey: presignedInfo?.storage_key,
          status: 'uploading' as const,
        };
      });

      setUploadFiles((prev) =>
        prev.map((file) => {
          const updated = updatedFiles.find((u) => u.id === file.id);
          return updated || file;
        })
      );

      // Step 2: Upload files directly to S3
      const uploadPromises = updatedFiles.map(async (file) => {
        if (!file.presignedUrl || !file.storageKey) {
          throw new Error(`No presigned URL for file ${file.file.name}`);
        }

        return new Promise<UploadedFileInfo>((resolve, reject) => {
          if (!file.presignedUrl) {
            reject(new Error(`No presigned URL for file ${file.file.name}`));
            return;
          }

          const xhr = new XMLHttpRequest();

          // Track upload progress
          xhr.upload.addEventListener('progress', (event) => {
            if (event.lengthComputable) {
              const progress = Math.round((event.loaded / event.total) * 100);
              setUploadFiles((prev) =>
                prev.map((f) =>
                  f.id === file.id ? { ...f, progress } : f
                )
              );
            }
          });

          xhr.addEventListener('load', () => {
            if (xhr.status >= 200 && xhr.status < 300) {
              setUploadFiles((prev) =>
                prev.map((f) =>
                  f.id === file.id
                    ? { ...f, status: 'success', progress: 100 }
                    : f
                )
              );
              resolve({
                file_id: file.id,
                filename: file.file.name,
                storage_key: file.storageKey!,
                content_type: file.file.type,
                file_size: file.file.size,
              });
            } else {
              setUploadFiles((prev) =>
                prev.map((f) =>
                  f.id === file.id
                    ? { ...f, status: 'error', error: `Upload failed: ${xhr.status} ${xhr.statusText}` }
                    : f
                )
              );
              reject(new Error(`Upload failed: ${xhr.status} ${xhr.statusText}`));
            }
          });

          xhr.addEventListener('error', () => {
            setUploadFiles((prev) =>
              prev.map((f) =>
                f.id === file.id
                  ? { ...f, status: 'error', error: 'Upload failed: Network error' }
                  : f
              )
            );
            reject(new Error('Upload failed: Network error'));
          });

          // Open the request
          xhr.open('PUT', file.presignedUrl);

          // Set Content-Type header to match what was used in presigned URL generation
          if (file.file.type) {
            xhr.setRequestHeader('Content-Type', file.file.type);
          }

          // Send the file
          xhr.send(file.file);
        });
      });

      const uploadedFiles = await Promise.all(uploadPromises);

      // Step 3: Confirm uploads and start ingestion
      await onUpload(uploadedFiles);

      // Clear successfully uploaded files
      setUploadFiles((prev) =>
        prev.filter((file) => !uploadedFiles.some((uploaded) => uploaded.file_id === file.id))
      );

    } catch (error) {
      console.error('Upload failed:', error);

      // Mark all uploading files as failed
      setUploadFiles((prev) =>
        prev.map((file) =>
          file.status === 'uploading'
            ? { ...file, status: 'error', error: 'Upload failed' }
            : file
        )
      );
    }
  };

  const getStatusIcon = (status: UploadFile['status'], filename: string) => {
    switch (status) {
      case 'error':
        return <AlertCircle className="h-4 w-4 text-destructive" />;
      case 'uploading':
        return <FileTypeIcon filename={filename} className="h-4 w-4 animate-pulse" />;
      case 'success':
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      default:
        return <FileTypeIcon filename={filename} className="h-4 w-4" />;
    }
  };

  const filesPendingCount = uploadFiles.filter(f => f.status === 'pending').length;
  const hasAnyFiles = uploadFiles.length > 0;

  const clearAllFiles = () => {
    setUploadFiles([]);
  };

  return (
    <div className="flex flex-col h-full max-h-[80vh] space-y-6 w-full">
      {/* Dropzone Area */}
      <div
        {...getRootProps()}
        className={`card p-8 text-center cursor-pointer transition-all duration-200 rounded-3xl ${
          isDragActive
            ? 'border-primary bg-primary/5'
            : 'border-border hover:border-primary/50 hover:bg-muted/50'
        } ${
          isUploading ? 'opacity-60 cursor-not-allowed' : ''
        }`}
      >
        <input {...getInputProps()} />
        <UploadCloud className={`h-12 w-12 text-muted-foreground mx-auto mb-4 transition-all duration-200 ${
          isDragActive ? 'scale-110 text-primary' : ''
        }`} />
        <div className="space-y-2">
          <p className="text-base font-medium">
            {isDragActive ? (
              <span className="text-primary">Drop files to upload</span>
            ) : (
              <span>
                Drag & drop files, or <span className="text-primary font-semibold">click to browse</span>
              </span>
            )}
          </p>
          <p className="text-sm text-muted-foreground">
            Max 10MB per file • Supported: PDF, DOCX, DOC, TXT, MD, HTML, RTF, CSV, JSON, EPUB, IPYNB, HWP, MP3, MP4, PPT, PPTX, PPTM
          </p>
        </div>
      </div>

      {/* File List Area - Made scrollable with flex-1 */}
      {uploadFiles.length > 0 && (
        <div className="flex-1 min-h-0 flex flex-col space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="text-sm font-medium flex items-center gap-2">
              <File className="h-4 w-4" />
              Files to Upload ({uploadFiles.length})
            </h3>
            <Button
              variant="ghost"
              size="sm"
              onClick={clearAllFiles}
              className="text-xs text-muted-foreground hover:text-foreground rounded-3xl"
              disabled={isUploading}
            >
              Clear All
            </Button>
          </div>

          <ScrollArea className="flex-1 border rounded-xl">
            <div className="p-3 space-y-2">
              {uploadFiles.map((item, index) => (
                <div
                  key={item.id}
                  className="flex items-center gap-3 p-3 border rounded-3xl bg-card hover:bg-muted/50 transition-colors"
                >
                  <div className="flex items-center justify-center w-8 h-8 rounded-full bg-muted text-xs font-medium text-muted-foreground">
                    {index + 1}
                  </div>

                  {getStatusIcon(item.status, item.file.name)}

                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium truncate break-words" title={item.file.name}>
                      {item.file.name}
                    </p>
                    <div className="flex items-center gap-2 mt-1">
                      <span className="text-xs text-muted-foreground">{formatBytes(item.file.size)}</span>
                      {item.status === 'uploading' && (
                        <Badge variant="secondary" className="text-xs rounded-3xl">
                          {item.progress}%
                        </Badge>
                      )}
                      {item.status === 'error' && item.error && (
                        <Badge variant="destructive" className="text-xs rounded-3xl">
                          Failed
                        </Badge>
                      )}
                      {item.status === 'success' && (
                        <Badge variant="default" className="text-xs rounded-3xl">
                          Uploaded
                        </Badge>
                      )}
                    </div>
                    {item.status === 'error' && item.error && (
                      <p className="text-xs text-destructive mt-1 truncate">{item.error}</p>
                    )}
                  </div>

                  <div className="flex-shrink-0">
                    {item.status === 'uploading' ? (
                      <Progress value={item.progress} className="w-20 h-2 rounded-3xl" />
                    ) : (
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-8 w-8 text-muted-foreground hover:text-destructive rounded-3xl"
                        onClick={() => removeFile(item.id)}
                        disabled={isUploading}
                        aria-label="Remove file"
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </ScrollArea>
        </div>
      )}

      {/* Action Button Area - Fixed at bottom */}
      {hasAnyFiles && (
        <div className="flex-shrink-0 space-y-3">
          <div className="flex items-center justify-between text-sm text-muted-foreground">
            <span>Ready to upload {filesPendingCount} file{filesPendingCount === 1 ? '' : 's'}</span>
            <span>Total: {formatBytes(uploadFiles.reduce((sum, f) => sum + f.file.size, 0))}</span>
          </div>
          <Button
            onClick={handleUpload}
            disabled={isUploading || filesPendingCount === 0}
            className="w-full rounded-3xl"
          >
            {isUploading
              ? 'Uploading...'
              : `Upload ${filesPendingCount} File${filesPendingCount === 1 ? '' : 's'}`}
          </Button>
        </div>
      )}

      {!uploadFiles.length && (
        <div className="text-center py-8 text-muted-foreground">
          <File className="h-8 w-8 mx-auto mb-2 opacity-50" />
          <p className="text-sm">No files selected</p>
        </div>
      )}
    </div>
  );
}
