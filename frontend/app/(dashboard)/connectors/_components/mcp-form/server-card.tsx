import React, { useState, useEffect } from 'react';
import { BadgeCheck, ExternalLink, Terminal, Power, Wrench, Loader2, AlertCircle, RefreshCw, BellOff, Shield, ChevronDown, ChevronUp, Database, Trash2 } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardFooter } from '@/components/ui/card';
import { MCPServer } from './types';
import { useToggleServerActiveState } from '@/hooks/use-set-servers';
import { Button } from '@/components/ui/button';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { toast } from 'sonner';
import { cn } from '@/lib/utils';
import { ToolPermissions } from './tool-permissions';
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Dialog, DialogContent, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';

export interface ServerCardProps {
  server: MCPServer;
  onEdit?: (server: MCPServer) => void;
  onRemove?: (server: MCPServer) => void;
  isEditMode?: boolean;
  onRefresh?: () => void;
  isRefreshing?: boolean;
  workspaceId: string;
  toolsPermissions: string[];
  attachedAgents?: Array<{ agentId: string; agentName: string }>;
}

const ServerCard = ({
  server,
  onEdit,
  onRemove,
  isEditMode = false,
  onRefresh,
  isRefreshing = false,
  workspaceId,
  toolsPermissions,
  attachedAgents = []
}: ServerCardProps) => {
  const { mutate: toggleServerActive, isPending } = useToggleServerActiveState();
  const [isActive, setIsActive] = useState(server.is_active !== false);
  const [isToggling, setIsToggling] = useState(false);
  const [showPermissions, setShowPermissions] = useState(false);
  const [showResources, setShowResources] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const MAX_VISIBLE_AVATARS = 3;
  const visibleAgents = attachedAgents.slice(0, MAX_VISIBLE_AVATARS);
  const hiddenAgentCount = attachedAgents.length - visibleAgents.length;

  // Update local state when server prop changes
  useEffect(() => {
    setIsActive(server.is_active !== false);
  }, [server.is_active]);

  // Ensure required properties exist with defaults
  const serverWithDefaults = {
    ...server,
    connected: server.connected ?? false,
    connection_error: server.connection_error ?? null,
    is_active: isActive,
    is_builtin: server.is_builtin ?? false
  };

  // Check if server is a stdio type (not fully supported yet)
  const isStdioServer = serverWithDefaults.type === 'stdio';
  // Check if the server is built-in
  const isBuiltinServer = serverWithDefaults.is_builtin === true;

  const getActivityStatus = () => {
    if (isToggling) {
      return {
        label: 'Updating...',
        icon: <Loader2 className="h-3 w-3 animate-spin mr-1" />
      };
    }

    if (!serverWithDefaults.is_active) {
      return {
        label: 'Inactive',
        icon: <AlertCircle className="h-3 w-3 mr-1" />
      };
    }

    return {
      label: 'Active',
      icon: <Power className="h-3 w-3 mr-1" />
    };
  };

  const getConnectionStatus = () => {
    if (!serverWithDefaults.is_active || isStdioServer) {
      return null;
    }

    if (serverWithDefaults.connected) {
      return {
        label: 'Available',
        icon: <BadgeCheck className="h-3 w-3 mr-1" />
      };
    }

    return {
      label: 'Unavailable',
      icon: <AlertCircle className="h-3 w-3 mr-1" />
    };
  };

  const activityStatus = getActivityStatus();
  const connectionStatus = getConnectionStatus();

  const handleToggleActive = () => {
    if (isStdioServer) {
      toast.info("StdIO servers are not yet fully supported. Please use SSE servers for now.");
      return;
    }

    setIsActive(!isActive);
    setIsToggling(true);

    toggleServerActive(server.name, {
      onSuccess: () => {
        setIsToggling(false);
        toast.success(`Server ${isActive ? 'deactivated' : 'activated'} successfully`);
      },
      onError: (error) => {
        setIsActive(isActive);
        setIsToggling(false);
        toast.error(`Failed to ${isActive ? 'deactivate' : 'activate'} server: ${error.message}`);
      }
    });
  };

  const cardBorderStyle = cn(
    "overflow-hidden h-full flex flex-col hover:shadow-sm transition-all",
    isActive
      ? "hover:border-primary/50"
      : "hover:border-destructive/50"
  );

  return (
    <Card className={cardBorderStyle}>
      <CardContent className="p-6 pb-4 flex-1 overflow-hidden">
        <div className="flex flex-col h-full">
          <div className="flex items-center justify-between gap-2 mb-3">
            <div className="flex items-center gap-2">
              <div>
                {isStdioServer ? (
                  <Badge variant="secondary" className="flex items-center px-2 py-0.5">
                    <Terminal className="h-3.5 w-3.5 mr-1" />
                    STDIO
                  </Badge>
                ) : (
                  <Badge variant={isActive ? "ghost-primary" : "ghost-destructive"} className="flex items-center px-2 py-0.5">
                    <ExternalLink className="h-3.5 w-3.5 mr-1" />
                    SSE
                  </Badge>
                )}
              </div>
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <h3 className="text-base font-medium truncate max-w-[80%]">{server.name}</h3>
                  </TooltipTrigger>
                  <TooltipContent side="top">
                    <p>{server.name}</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div>

            <div className="flex items-center gap-2">
              <Badge variant={isActive ? "ghost-primary" : "ghost-destructive"} className="flex items-center">
                {activityStatus.icon}
                {activityStatus.label}
              </Badge>

              {connectionStatus && (
                <Badge variant={serverWithDefaults.connected ? "ghost-success" : "ghost-destructive"} className="flex items-center">
                  {connectionStatus.icon}
                  {connectionStatus.label}
                </Badge>
              )}
            </div>
          </div>

          <div className="flex-1 overflow-y-auto">
            <div className="flex flex-col gap-3">
              <div className="flex flex-col gap-1">
                {isStdioServer ? (
                  <div className="text-xs text-muted-foreground flex items-center gap-1">
                    <span className="font-medium text-purple-600 dark:text-purple-400">Command:</span>
                    <code className="bg-muted px-1.5 py-0.5 rounded text-xs font-mono">
                      {serverWithDefaults.command} {serverWithDefaults.args?.join(' ') || ''}
                    </code>
                  </div>
                ) : (
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <div className="text-xs text-muted-foreground flex items-center gap-1 cursor-default">
                          <span className="font-medium text-blue-600 dark:text-blue-400">URL:</span>
                          <code className="bg-muted px-1.5 py-0.5 rounded text-xs font-mono truncate max-w-full">
                            {serverWithDefaults.url}
                          </code>
                        </div>
                      </TooltipTrigger>
                      <TooltipContent side="bottom">
                        <p>{serverWithDefaults.url}</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                )}
              </div>

              <div className="flex flex-col gap-1">
                <div className="flex items-center text-xs text-muted-foreground mb-2">
                  <Wrench className="h-3 w-3 mr-1" />
                  <span className="font-medium">
                    {server.tool_list && server.tool_list.length > 0
                      ? `Available Tools (${server.tool_list.length}):`
                      : 'No tools available'}
                  </span>
                </div>

                {server.tool_list && server.tool_list.length > 0 && (
                  <div className="flex flex-wrap gap-1 mb-3">
                    {server.tool_list?.map((tool) => (
                      <Badge key={tool} variant="outline" size="sm">
                        {tool}
                      </Badge>
                    ))}
                  </div>
                )}

                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowPermissions(!showPermissions)}
                  className="flex items-center justify-between w-full p-2 hover:bg-secondary/50 transition-colors"
                >
                  <div className="flex items-center gap-2 text-xs text-muted-foreground">
                    <Shield className="h-3.5 w-3.5" />
                    <span className="font-medium">Manage Tool Permissions</span>
                  </div>
                  {showPermissions ? (
                    <ChevronUp className="h-4 w-4" />
                  ) : (
                    <ChevronDown className="h-4 w-4" />
                  )}
                </Button>

                {showPermissions && (
                  <div className="mt-2 max-h-[200px] overflow-y-auto border rounded-xl bg-secondary/20">
                    <ToolPermissions
                      server={server}
                      workspaceId={workspaceId}
                      onPermissionChange={onRefresh}
                      toolsPermissions={toolsPermissions}
                    />
                  </div>
                )}

                {server.resource_list && server.resource_list.length > 0 && (
                  <>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => setShowResources(!showResources)}
                      className="flex items-center justify-between w-full p-2 hover:bg-secondary/50 transition-colors mt-2"
                    >
                      <div className="flex items-center gap-2 text-xs text-muted-foreground">
                        <Database className="h-3.5 w-3.5" />
                        <span className="font-medium">Resources ({server.resource_list.length})</span>
                      </div>
                      {showResources ? (
                        <ChevronUp className="h-4 w-4" />
                      ) : (
                        <ChevronDown className="h-4 w-4" />
                      )}
                    </Button>

                    {showResources && (
                      <div className="mt-2 max-h-[200px] overflow-y-auto border rounded-3xl bg-secondary/20 p-2">
                        <div className="space-y-1">
                          {server.resource_list?.map((resource, index) => (
                            <div
                              key={index}
                              className="text-xs text-muted-foreground bg-background/50 rounded px-2 py-1 break-words"
                            >
                              {resource}
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </>
                )}

                {attachedAgents.length > 0 && (
                  <div className="mt-1 pt-1">
                    <div className="flex items-center gap-1.5">
                      {visibleAgents?.map((agent) => {
                        const agentNameInitial = agent.agentName.charAt(0).toUpperCase();
                        const agentFirstName = agent.agentName.split(' ')[0].toLowerCase();
                        const avatarPath = `/avatars/${agentFirstName}.webp`;
                        return (
                          <TooltipProvider key={agent.agentId} delayDuration={100}>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <Avatar className="h-6 w-6 border border-muted hover:border-primary/50 transition-all cursor-pointer">
                                  <AvatarImage
                                    src={avatarPath}
                                    alt={agent.agentName}
                                    onError={(e) => { const target = e.target as HTMLImageElement; target.style.display = 'none'; }}
                                  />
                                  <AvatarFallback className="text-xs">{agentNameInitial}</AvatarFallback>
                                </Avatar>
                              </TooltipTrigger>
                              <TooltipContent side="bottom"><p className="text-sm">{agent.agentName}</p></TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                        );
                      })}
                      {hiddenAgentCount > 0 && (
                        <TooltipProvider delayDuration={100}>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <Badge variant="secondary" size="sm" className="cursor-help">+{hiddenAgentCount}</Badge>
                            </TooltipTrigger>
                            <TooltipContent side="bottom" className="max-w-[200px]">
                              <p className="text-sm font-medium mb-1">Additional Agents:</p>
                              <ul className="list-disc list-inside text-sm">
                                {attachedAgents.slice(MAX_VISIBLE_AVATARS)?.map(agent => (<li key={agent.agentId}>{agent.agentName}</li>))}
                              </ul>
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                      )}
                    </div>
                  </div>
                )}
              </div>

              {serverWithDefaults.connection_error && serverWithDefaults.is_active && !isStdioServer && (
                <div className="text-xs text-destructive flex items-start gap-1.5 bg-destructive/10 rounded-3xl p-2">
                  <AlertCircle className="h-4 w-4 mt-0.5 shrink-0" />
                  <p>{serverWithDefaults.connection_error}</p>
                </div>
              )}
            </div>
          </div>
        </div>
      </CardContent>

      <CardFooter className="p-6 pt-4 flex justify-between items-center gap-2 border-t">
        <div className="flex gap-2">
          <Button
            size="sm"
            variant={isActive ? "default" : "outline"}
            onClick={handleToggleActive}
            disabled={isToggling || isPending}
            className={cn(
              "gap-1.5",
              !isActive && "text-destructive hover:text-destructive hover:bg-destructive/10 border-destructive/50"
            )}
          >
            {isToggling ? (
              <Loader2 className="h-3.5 w-3.5 animate-spin" />
            ) : (
              <Power className="h-3.5 w-3.5" />
            )}
            {isActive ? 'Deactivate' : 'Activate'}
          </Button>

          {onRefresh && !isStdioServer && (
            <Button
              size="sm"
              variant="outline"
              onClick={onRefresh}
              disabled={isRefreshing || !serverWithDefaults.is_active}
              className="h-8 w-8 p-0"
            >
              <RefreshCw className={cn("h-3.5 w-3.5", isRefreshing && "animate-spin")} />
            </Button>
          )}
        </div>

        <div className="flex gap-2">
          {onEdit && !isBuiltinServer && (
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => onEdit(server)}
                    disabled={isToggling}
                    className="h-8 w-8 p-0"
                  >
                    <svg className="h-3.5 w-3.5" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path strokeLinecap="round" strokeLinejoin="round" d="M16.862 3.487a2.06 2.06 0 0 1 2.915 2.914L7.5 18.678l-4 1 1-4 14.362-14.191ZM19 7l-2-2" /></svg>
                  </Button>
                </TooltipTrigger>
                <TooltipContent side="top">
                  Edit
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          )}
          {onRemove && !isBuiltinServer && (
            <>
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      size="sm"
                      variant="destructive"
                      onClick={() => setShowDeleteDialog(true)}
                      disabled={isToggling}
                      className="h-8 w-8 p-0"
                    >
                      <Trash2 className="h-3.5 w-3.5" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent side="top" className="bg-destructive text-destructive-foreground">
                    Delete
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
              <Dialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
                <DialogContent>
                  <DialogHeader>
                    <DialogTitle>Delete MCP Server</DialogTitle>
                  </DialogHeader>
                  <p>Are you sure you want to delete ? This action cannot be undone.</p>
                  <DialogFooter>
                    <Button variant="outline" onClick={() => setShowDeleteDialog(false)}>
                      Cancel
                    </Button>
                    <Button variant="destructive" onClick={() => { setShowDeleteDialog(false); onRemove(server); }}>
                      Delete
                    </Button>
                  </DialogFooter>
                </DialogContent>
              </Dialog>
            </>
          )}
          {isBuiltinServer && (
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Badge variant="outline" className="flex items-center">
                    <Shield className="h-3.5 w-3.5 mr-1" />
                    Built-in
                  </Badge>
                </TooltipTrigger>
                <TooltipContent side="top">
                  Built-in servers cannot be edited or removed, but can be activated/deactivated
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          )}
        </div>
      </CardFooter>
    </Card>
  );
};

export default ServerCard;
