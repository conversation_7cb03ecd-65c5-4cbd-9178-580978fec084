'use client';

import React, { useState, useEffect } from 'react';
import { Shield, Power, Loader2, Lock, Info } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardFooter } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { toast } from 'sonner';
import { cn } from '@/lib/utils';
import { useToggleToolActiveState, useToolPermissions } from '@/hooks/use-tool-mutations';
import { ConnectorWithStatusResponse } from '@/client/types.gen';
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";

export interface ToolCardProps {
  tool: ConnectorWithStatusResponse;
  isBuiltin?: boolean;
  onView?: (tool: ConnectorWithStatusResponse) => void;
  isEditMode?: boolean;
  attachedAgents?: Array<{ agentId: string; agentName: string }>;
}

const ToolCard = ({
  tool,
  isBuiltin = true,
  onView,
  isEditMode = false,
  attachedAgents = []
}: ToolCardProps) => {
  const [mounted, setMounted] = useState(false);
  const { mutate: toggleToolActive, isPending } = useToggleToolActiveState();
  const { mutate: togglePermission, isPending: isPermissionPending } = useToolPermissions();
  const [isActive, setIsActive] = useState(tool.is_active);
  const [isToggling, setIsToggling] = useState(false);
  const [requiresPermission, setRequiresPermission] = useState(tool.required_permission);

  const displayName = tool.display_name || tool.name;

  useEffect(() => {
    setMounted(true);
    setIsActive(tool.is_active);
    setRequiresPermission(tool.required_permission);
  }, [tool.is_active, tool.required_permission]);

  const cardBorderStyle = cn(
    "overflow-hidden transition-all duration-200 hover:shadow-soft",
    "min-h-[280px] flex flex-col justify-between",
    isActive
      ? "hover:border-primary/50 dark:hover:border-primary/70"
      : "hover:border-destructive/50 dark:hover:border-destructive/70"
  );

  const handleToggleActive = () => {
    if (!tool.id) return;

    setIsActive(!isActive);
    setIsToggling(true);

    toggleToolActive(
      { toolId: tool.id, isActive: !isActive },
      {
        onSuccess: () => {
          setIsToggling(false);
          toast.success(`Tool ${isActive ? 'deactivated' : 'activated'} successfully`);
        },
        onError: (error) => {
          setIsActive(isActive);
          setIsToggling(false);
          toast.error(`Failed to ${isActive ? 'deactivate' : 'activate'} tool: ${error.message}`);
        }
      }
    );
  };

  const handleTogglePermission = () => {
    if (!tool.id) return;

    const newRequiresPermission = !requiresPermission;
    setRequiresPermission(newRequiresPermission);

    togglePermission(
      {
        toolId: tool.id,
        requiredPermission: newRequiresPermission
      },
      {
        onSuccess: () => {
          toast.success(`Permission requirement ${newRequiresPermission ? 'added' : 'removed'}`);
        },
        onError: (error) => {
          setRequiresPermission(!newRequiresPermission); // Revert on error
          toast.error(`Failed to update permission requirement: ${error.message}`);
        }
      }
    );
  };

  // Return empty div during SSR
  if (!mounted) {
    return <div className="min-h-[280px]" />;
  }

  const MAX_VISIBLE_AVATARS = 3;
  const visibleAgents = attachedAgents.slice(0, MAX_VISIBLE_AVATARS);
  const hiddenAgentCount = attachedAgents.length - visibleAgents.length;

  return (
    <Card className={cardBorderStyle}>
      <CardContent className="p-6 pb-4 flex-1">
        <div className="flex flex-col gap-4 h-full">
          <div className="flex flex-col gap-3">
            <div className="flex flex-wrap items-center gap-2">
              {isBuiltin && (
                <Badge
                  variant={isActive ? "ghost-primary" : "ghost-destructive"}
                  className="flex items-center h-7 px-2.5 transition-colors"
                >
                  <Shield className="h-3.5 w-3.5 mr-1.5 shrink-0" />
                  <span className="text-xs font-medium">Builtin</span>
                </Badge>
              )}
              {requiresPermission && (
                <Badge
                  variant="outline"
                  className={cn(
                    "flex items-center h-7 px-2.5 transition-colors",
                    "bg-[hsl(var(--warning)/0.1)] text-[hsl(var(--warning))] border-[hsl(var(--warning)/0.2)]",
                    "dark:bg-[hsl(var(--warning)/0.15)] dark:text-[hsl(var(--warning)/0.9)] dark:border-[hsl(var(--warning)/0.3)]"
                  )}
                >
                  <Lock className="h-3.5 w-3.5 mr-1.5 shrink-0" />
                  <span className="text-xs font-medium">Requires Approval</span>
                </Badge>
              )}
            </div>

            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <h3 className="text-base font-semibold truncate cursor-default">
                    {displayName}
                  </h3>
                </TooltipTrigger>
                <TooltipContent side="top" className="max-w-[300px]">
                  <p>{displayName}</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>

          <div className="text-sm text-muted-foreground line-clamp-2 flex-1">
            {tool.description || 'No description available'}
          </div>

          {attachedAgents.length > 0 && mounted && (
            <div className="mt-auto">
              <div className="flex items-center gap-2">
                <div className="flex -space-x-2">
                  {visibleAgents?.map((agent) => {
                    const agentNameInitial = agent.agentName.charAt(0).toUpperCase();
                    const agentFirstName = agent.agentName.split(' ')[0].toLowerCase();
                    const avatarPath = `/avatars/${agentFirstName}.webp`;

                    return (
                      <TooltipProvider key={agent.agentId} delayDuration={100}>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <Avatar className="h-8 w-8 border-2 border-background transition-all hover:scale-105">
                              <AvatarImage
                                src={avatarPath}
                                alt={agent.agentName}
                                onError={(e) => {
                                  const target = e.target as HTMLImageElement;
                                  target.style.display = 'none';
                                }}
                              />
                              <AvatarFallback className="text-xs bg-muted">{agentNameInitial}</AvatarFallback>
                            </Avatar>
                          </TooltipTrigger>
                          <TooltipContent side="bottom">
                            <p className="text-sm">{agent.agentName}</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    );
                  })}
                </div>

                {hiddenAgentCount > 0 && (
                  <TooltipProvider delayDuration={100}>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Badge
                          variant="secondary"
                          className="h-6 px-2 cursor-help ml-2"
                        >
                          +{hiddenAgentCount}
                        </Badge>
                      </TooltipTrigger>
                      <TooltipContent side="bottom" className="max-w-[250px]">
                        <p className="text-sm font-medium mb-1.5">Additional Agents:</p>
                        <ul className="list-disc list-inside text-sm space-y-0.5">
                          {attachedAgents.slice(MAX_VISIBLE_AVATARS)?.map(agent => (
                            <li key={agent.agentId}>{agent.agentName}</li>
                          ))}
                        </ul>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                )}
              </div>
            </div>
          )}
        </div>
      </CardContent>

      <CardFooter className="p-6 pt-4 flex justify-between gap-2 items-center border-t bg-muted/5">
        <div className="flex gap-1">
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  size="sm"
                  variant={isActive ? "default" : "outline"}
                  onClick={handleToggleActive}
                  disabled={isToggling || isPending || !mounted}
                  className={cn(
                    "h-7 px-2 text-xs transition-colors",
                    !isActive && "text-destructive hover:text-destructive-foreground hover:bg-destructive hover:border-destructive"
                  )}
                >
                  {isToggling ? (
                    <Loader2 className="h-3 w-3 animate-spin mr-1" />
                  ) : (
                    <Power className="h-3 w-3 mr-1" />
                  )}
                  <span>{isActive ? 'Deactivate' : 'Activate'}</span>
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                {isActive ? 'Deactivate this tool' : 'Activate this tool'}
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>

          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  size="sm"
                  variant={requiresPermission ? "default" : "outline"}
                  onClick={handleTogglePermission}
                  disabled={isPermissionPending || !mounted}
                  className={cn(
                    "h-7 px-2 text-xs transition-colors",
                    requiresPermission && "bg-[hsl(var(--warning))] hover:bg-[hsl(var(--warning))]"
                  )}
                >
                  {isPermissionPending ? (
                    <Loader2 className="h-3 w-3 animate-spin mr-1" />
                  ) : (
                    <Lock className="h-3 w-3 mr-1" />
                  )}
                  <span>{requiresPermission ? 'Remove Approval' : 'Require Approval'}</span>
                </Button>
              </TooltipTrigger>
              <TooltipContent
                className={requiresPermission ? "bg-[hsl(var(--warning))] text-white" : undefined}
              >
                {requiresPermission ? 'Remove approval requirement' : 'Require approval for this tool'}
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>

        {onView && (
          <Button
            size="sm"
            variant="ghost"
            onClick={() => onView(tool)}
            disabled={!mounted}
            className="h-7 px-2 text-xs"
          >
            <Info className="h-3 w-3 mr-1" />
            <span>Details</span>
          </Button>
        )}
      </CardFooter>
    </Card>
  );
};

export default ToolCard;
