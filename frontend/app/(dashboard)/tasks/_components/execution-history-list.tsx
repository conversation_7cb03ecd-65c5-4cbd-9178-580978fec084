'use client';

import { useState, useEffect, useMemo, useCallback, memo } from 'react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  CheckCircle,
  XCircle,
  RefreshCcw,
  StopCircleIcon,
  Clock,
  ExternalLink,
  Calendar,
  Timer,
  AlertCircle,
  ArrowUp,
  ArrowDown,
  ChevronLeft,
  ChevronRight,
  Filter,
  SortAsc,
  HandIcon
} from 'lucide-react';
import { TaskHistory, TaskExecutionStatus } from '@/client/types.gen';
import { formatRelativeTime, formatLocalDateTime } from '@/lib/date-utils';
import { cn } from '@/lib/utils';
import Link from 'next/link';

interface ExecutionHistoryListProps {
  taskHistory?: TaskHistory[];
  taskId: string;
  agentId?: string;
  highlightExecutionId?: string;
  itemsPerPage?: number;        // Configurable page size
  enableAdvancedFilters?: boolean; // Enable filtering and sorting
  enablePagination?: boolean;   // Enable pagination
}

type SortOption = 'date' | 'duration';
type SortOrder = 'asc' | 'desc';
type FilterOption = 'all' | 'succeeded' | 'failed' | 'running' | 'cancelled' | 'required_approval';

const getStatusConfig = (status: TaskExecutionStatus) => {
  switch (status) {
    case 'succeeded':
      return {
        icon: CheckCircle,
        color: 'text-green-600',
        bgColor: 'bg-green-500',
        cardBg: 'bg-green-50/50 dark:bg-green-950/10',
        badgeVariant: 'default' as const,
        label: 'Succeeded'
      };
    case 'failed':
      return {
        icon: XCircle,
        color: 'text-red-700 dark:text-red-400',
        bgColor: 'bg-red-500 hover:bg-red-600 shadow-red-500/20',
        cardBg: 'bg-red-50/80 dark:bg-red-950/15 border-red-100/50 dark:border-red-900/20',
        badgeVariant: 'destructive' as const,
        label: 'Failed'
      };
    case 'running':
      return {
        icon: RefreshCcw,
        color: 'text-blue-600',
        bgColor: 'bg-blue-500',
        cardBg: 'bg-blue-50/50 dark:bg-blue-950/10',
        badgeVariant: 'default' as const,
        label: 'Running'
      };
    case 'cancelled':
      return {
        icon: StopCircleIcon,
        color: 'text-orange-600',
        bgColor: 'bg-orange-500',
        cardBg: 'bg-orange-50/50 dark:bg-orange-950/10',
        badgeVariant: 'secondary' as const,
        label: 'Cancelled'
      };
    case 'required_approval':
      return {
        icon: HandIcon,
        color: 'text-purple-700',
        bgColor: 'bg-purple-600',
        cardBg: 'bg-purple-50/30 dark:bg-purple-950/5',
        badgeVariant: 'secondary' as const,
        label: 'Requires Approval'
      };
    default:
      return {
        icon: Clock,
        color: 'text-gray-600',
        bgColor: 'bg-gray-500',
        cardBg: 'bg-gray-50/50 dark:bg-gray-950/10',
        badgeVariant: 'outline' as const,
        label: 'Pending'
      };
  }
};

const calculateDuration = (startTime?: string, endTime?: string | null) => {
  if (!startTime) return 0;

  const start = new Date(startTime);

  // For running tasks (no end time), use current time for duration calculation
  // But return 0 for sorting purposes to handle them specially
  if (!endTime) {
    return 0; // Running tasks will be handled specially in sorting
  }

  const end = new Date(endTime);
  const duration = end.getTime() - start.getTime();

  // Ensure positive duration (handle any data inconsistencies)
  return Math.max(0, duration);
};

const formatDuration = (durationMs: number) => {
  if (durationMs < 1000) return `${durationMs}ms`;
  if (durationMs < 60000) return `${Math.round(durationMs / 1000)}s`;
  if (durationMs < 3600000) return `${Math.round(durationMs / 60000)}m`;
  return `${Math.round(durationMs / 3600000)}h`;
};

// Optimized ExecutionCard component with memo
const ExecutionCard = memo(function ExecutionCard({
  execution,
  index,
  isHighlighted,
  agentId
}: {
  execution: TaskHistory;
  index: number;
  isHighlighted: boolean;
  agentId?: string;
}) {
  const config = getStatusConfig(execution.status);
  const StatusIcon = config.icon;
  const durationMs = calculateDuration(execution.start_time, execution.end_time);
  const duration = formatDuration(durationMs);
  const shortId = execution.conversation_id?.split('-')[0] || 'N/A';

  return (
    <Card
      id={`execution-${execution.conversation_id}`}
      className={cn(
        "transition-all duration-300 hover:shadow-md",
        isHighlighted && "ring-2 ring-primary/50 shadow-lg scale-[1.02] execution-highlight",
        config.cardBg
      )}
    >
      <CardContent className="p-6">
        <div className="flex items-start justify-between">
          <div className="flex items-start gap-4 flex-1">
            <div className={cn(
              "w-10 h-10 rounded-lg flex items-center justify-center shadow-sm",
              config.bgColor
            )}>
              <StatusIcon className="w-5 h-5 text-white" />
            </div>

            <div className="flex-1 space-y-3">
              <div className="flex items-center gap-3">
                <h3 className="font-semibold text-lg">Execution #{shortId}</h3>
                <Badge variant={config.badgeVariant} className="h-6">
                  {config.label}
                </Badge>
                {/* {isHighlighted && (
                  <Badge variant="outline" className="h-6 bg-primary/10 border-primary/30">
                    Highlighted
                  </Badge>
                )} */}
              </div>

              <div className="grid grid-cols-1 sm:grid-cols-3 gap-3 text-sm">
                <div className="flex items-center gap-2">
                  <Calendar className="w-4 h-4 text-muted-foreground" />
                  <div>
                    <span className="text-muted-foreground">Started:</span>
                    <div className="font-medium">
                      {execution.start_time
                        ? formatLocalDateTime(execution.start_time)
                        : 'Unknown'
                      }
                    </div>
                    {execution.start_time && (
                      <div className="text-xs text-muted-foreground">
                        {formatRelativeTime(execution.start_time)}
                      </div>
                    )}
                  </div>
                </div>

                {execution.end_time && (
                  <div className="flex items-center gap-2">
                    <Clock className="w-4 h-4 text-muted-foreground" />
                    <div>
                      <span className="text-muted-foreground">Ended:</span>
                      <div className="font-medium">
                        {formatLocalDateTime(execution.end_time)}
                      </div>
                    </div>
                  </div>
                )}

                {execution.status !== 'running' && (
                  <div className="flex items-center gap-2">
                    <Timer className="w-4 h-4 text-muted-foreground" />
                    <div>
                      <span className="text-muted-foreground">Duration:</span>
                      <div className="font-medium">{duration}</div>
                    </div>
                  </div>
                )}
              </div>

              {execution.message && execution.status === 'failed' && (
                <div className="p-3 rounded-lg bg-red-50 dark:bg-red-950/20 border border-red-200 dark:border-red-800">
                  <div className="flex items-start gap-2">
                    <AlertCircle className="w-4 h-4 text-red-500 mt-0.5 flex-shrink-0" />
                    <div className="flex-1">
                      <p className="text-sm font-medium text-red-700 dark:text-red-400">
                        Error Details
                      </p>
                      <p className="text-sm text-red-600 dark:text-red-300 mt-1 break-words">
                        {execution.message}
                      </p>
                    </div>
                  </div>
                </div>
              )}

              {execution.message && execution.status === 'required_approval' && (() => {
                const parseApprovalMessage = (message: string) => {
                  // Parse the structured approval message
                  const operationMatch = message.match(/Operation:\s*([^\n]+)/);
                  const detailsMatch = message.match(/Details:\s*([\s\S]*?)(?:\n\nPlease confirm|$)/);

                  return {
                    toolName: operationMatch?.[1]?.trim() || 'Unknown Operation',
                    toolArgs: detailsMatch?.[1]?.trim() || message
                  };
                };

                const { toolName, toolArgs } = parseApprovalMessage(execution.message);

                return (
                  <div className="p-4 rounded-lg bg-slate-50 dark:bg-slate-950/20 border border-slate-200 dark:border-slate-700">
                    <div className="flex items-start gap-3">
                      <HandIcon className="w-5 h-5 text-purple-700 mt-0.5 flex-shrink-0" />
                      <div className="flex-1 space-y-3">
                        <div>
                          <p className="text-sm font-semibold text-purple-800 dark:text-purple-300 flex items-center gap-2">
                            Approval Required
                          </p>
                        </div>

                        <div className="space-y-2">
                          <div className="p-2 rounded-md bg-slate-100/50 dark:bg-slate-900/20 border border-slate-200/50 dark:border-slate-700/30">
                            <p className="text-xs font-medium text-slate-700 dark:text-slate-300 mb-1">Operation:</p>
                            <p className="text-xs text-purple-800 dark:text-purple-300 font-mono bg-slate-50 dark:bg-slate-900/30 px-2 py-1 rounded border">
                              {toolName}
                            </p>
                          </div>

                          <div className="p-2 rounded-md bg-slate-100/50 dark:bg-slate-900/20 border border-slate-200/50 dark:border-slate-700/30">
                            <p className="text-xs font-medium text-slate-700 dark:text-slate-300 mb-2">Details:</p>
                            <pre className="text-xs text-slate-600 dark:text-slate-400 whitespace-pre-wrap bg-slate-50 dark:bg-slate-900/30 p-2 rounded border overflow-x-auto max-h-32">
                              {toolArgs}
                            </pre>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                );
              })()}
            </div>
          </div>

          <div className="flex items-center gap-2 ml-4">
            {execution.conversation_id && agentId && (
              <Button
                variant="outline"
                size="sm"
                asChild
                className="hover:bg-primary/5 hover:border-primary/30"
              >
                <Link
                  href={`/agents/${agentId}?autonomous=true&conversationId=${execution.conversation_id}`}
                  className="flex items-center gap-2"
                >
                  <ExternalLink className="w-4 h-4" />
                  View Details
                </Link>
              </Button>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
});

const ExecutionHistoryList = memo(function ExecutionHistoryList({
  taskHistory,
  taskId,
  agentId,
  highlightExecutionId,
  itemsPerPage = 5,
  enableAdvancedFilters = true,
  enablePagination = true
}: ExecutionHistoryListProps) {
  // Pagination and filter state
  const [currentPage, setCurrentPage] = useState(0);
  const [pageSize, setPageSize] = useState(itemsPerPage);
  const [sortBy, setSortBy] = useState<SortOption>('date');
  const [sortOrder, setSortOrder] = useState<SortOrder>('desc'); // Default to desc for most recent first
  const [filterBy, setFilterBy] = useState<FilterOption>('all');
  const [highlightedExecutionId, setHighlightedExecutionId] = useState<string | undefined>(undefined);
  const [updateKey, setUpdateKey] = useState(0); // Force update mechanism

  // Force update mechanism for immediate feedback
  const forceUpdate = useCallback(() => {
    setUpdateKey(prev => prev + 1);
  }, []);

  const toggleSortOrder = useCallback(() => {
    setSortOrder(prev => prev === 'asc' ? 'desc' : 'asc');
    forceUpdate(); // Immediate visual feedback
  }, [forceUpdate]);

  const handleSortByChange = useCallback((newSortBy: SortOption) => {
    setSortBy(newSortBy);
    // Reset to desc when changing sort field for better defaults
    setSortOrder('desc');
    forceUpdate(); // Immediate visual feedback
  }, [forceUpdate]);

  // Debug logging to track state changes
  useEffect(() => {
    console.log('🔄 Sort state changed:', { sortBy, sortOrder, filterBy, updateKey });
  }, [sortBy, sortOrder, filterBy, updateKey]);

  // Simple highlight with instant scroll
  useEffect(() => {
    if (highlightedExecutionId) {
      // Immediate scroll with delay for DOM
      const scrollTimer = setTimeout(() => {
        const element = document.getElementById(`execution-${highlightedExecutionId}`);
        if (element) {
          // Simple persistent highlight - no flash animation
          element.classList.add('execution-highlight');

          // Instant smooth scroll to center
          element.scrollIntoView({
            behavior: 'smooth',
            block: 'center',
            inline: 'nearest'
          });

          // Clear highlight after reasonable time
          setTimeout(() => {
            element.classList.remove('execution-highlight');
            setHighlightedExecutionId(undefined);
          }, 3000);
        }
      }, 150); // Delay for DOM readiness

      return () => clearTimeout(scrollTimer);
    }
  }, [highlightedExecutionId]);

  // Filter and sort data with debug logging
  const filteredAndSortedHistory = useMemo(() => {
    console.log('📊 Recalculating sorted data...', { sortBy, sortOrder, filterBy });

    let filtered = taskHistory || [];

    // Apply status filter
    if (filterBy !== 'all') {
      filtered = filtered.filter(h => h.status === filterBy);
    }

    // Apply sorting with order direction
    filtered.sort((a, b) => {
      let comparison = 0;

      switch (sortBy) {
        case 'date':
          const dateA = new Date(a.start_time || 0).getTime();
          const dateB = new Date(b.start_time || 0).getTime();
          comparison = dateB - dateA; // Default desc (newest first)
          break;

        case 'duration':
          // Enhanced duration calculation for proper sorting
          const durationA = calculateDuration(a.start_time, a.end_time);
          const durationB = calculateDuration(b.start_time, b.end_time);

          // Handle cases where duration might be 0 (running tasks or invalid data)
          if (durationA === 0 && durationB === 0) {
            // If both have no duration, sort by start time instead
            const fallbackDateA = new Date(a.start_time || 0).getTime();
            const fallbackDateB = new Date(b.start_time || 0).getTime();
            comparison = fallbackDateB - fallbackDateA; // Newest first for fallback
          } else if (durationA === 0) {
            comparison = 1; // Put zero-duration items at the end
          } else if (durationB === 0) {
            comparison = -1; // Put zero-duration items at the end
          } else {
            comparison = durationB - durationA; // Default desc (longest first)
          }
          break;

        default:
          comparison = 0;
      }

      // Apply sort order (flip comparison for ascending)
      return sortOrder === 'asc' ? -comparison : comparison;
    });

    console.log('✅ Sorted data calculated:', {
      original: taskHistory?.length,
      filtered: filtered.length,
      sortBy,
      sortOrder
    });

    return filtered;
  }, [taskHistory, filterBy, sortBy, sortOrder, updateKey]); // Include updateKey for force updates

  // Paginate data with update key
  const paginatedHistory = useMemo(() => {
    if (!enablePagination) return filteredAndSortedHistory;

    const startIndex = currentPage * pageSize;
    const result = filteredAndSortedHistory.slice(startIndex, startIndex + pageSize);
    console.log('📄 Paginated data:', { startIndex, pageSize, resultCount: result.length });
    return result;
  }, [filteredAndSortedHistory, currentPage, pageSize, enablePagination, updateKey]);

  // Calculate statistics
  const stats = useMemo(() => ({
    succeeded: taskHistory?.filter(h => h.status === 'succeeded').length || 0,
    failed: taskHistory?.filter(h => h.status === 'failed').length || 0,
    running: taskHistory?.filter(h => h.status === 'running').length || 0,
    cancelled: taskHistory?.filter(h => h.status === 'cancelled').length || 0,
    required_approval: taskHistory?.filter(h => h.status === 'required_approval').length || 0,
    total: taskHistory?.length || 0
  }), [taskHistory]);

  // Pagination calculations
  const totalPages = Math.ceil(filteredAndSortedHistory.length / pageSize);
  const hasNext = (currentPage + 1) * pageSize < filteredAndSortedHistory.length;
  const hasPrev = currentPage > 0;

  // Handle page changes
  const handlePageChange = useCallback((newPage: number) => {
    setCurrentPage(Math.max(0, Math.min(totalPages - 1, newPage)));
  }, [totalPages]);

  const handlePageSizeChange = useCallback((newSize: string) => {
    setPageSize(Number(newSize));
    setCurrentPage(0); // Reset to first page
  }, []);



  // Reset page when filters change
  useEffect(() => {
    setCurrentPage(0);
  }, [filterBy, sortBy, sortOrder]);

  // Direct navigation function - bypasses complex validation
  const navigateToExecutionPage = useCallback((executionId: string) => {
    const executionIndex = filteredAndSortedHistory.findIndex(
      exec => exec.conversation_id === executionId
    );

    if (executionIndex !== -1) {
      const targetPage = Math.floor(executionIndex / pageSize);

      // DIRECT page update - bypass handlePageChange validation
      setCurrentPage(targetPage);

      // Set highlighting after page change
      setTimeout(() => {
        setHighlightedExecutionId(executionId);
      }, 100);
    } else {
      // Still set highlighting even if not found
      setHighlightedExecutionId(executionId);
    }
  }, [filteredAndSortedHistory, pageSize]);

  // Simple navigation effect - ONLY handles navigation
  useEffect(() => {
    if (highlightExecutionId !== highlightedExecutionId && highlightExecutionId) {
      if (enablePagination && taskHistory?.length) {
        navigateToExecutionPage(highlightExecutionId);
      } else {
        // No pagination, just highlight
        setHighlightedExecutionId(highlightExecutionId);
      }
    }
  }, [highlightExecutionId, highlightedExecutionId, enablePagination, taskHistory?.length, navigateToExecutionPage]);



  if (!taskHistory || taskHistory.length === 0) {
    return (
      <Card className="border-dashed border-2">
        <CardContent className="p-12 text-center">
          <div className="flex flex-col items-center gap-4">
            <div className="w-16 h-16 rounded-full bg-muted/30 flex items-center justify-center">
              <Clock className="w-8 h-8 text-muted-foreground" />
            </div>
            <div className="space-y-2">
              <h3 className="text-lg font-medium text-muted-foreground">No Execution History</h3>
              <p className="text-sm text-muted-foreground/80 max-w-md">
                Task executions will appear here once the task has been run.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      {/* Unified Header with Filters and Statistics */}
      <Card>
        <CardHeader className="pb-4">
          {/* Header Row: Title + Filters */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 rounded-lg bg-primary/10 flex items-center justify-center">
                <Clock className="w-5 h-5 text-primary" />
              </div>
              <div>
                <CardTitle className="text-xl">Execution History</CardTitle>
                <p className="text-sm text-muted-foreground mt-1">
                  {enablePagination ? (
                    <>
                      Showing {paginatedHistory.length} of {filteredAndSortedHistory.length} executions
                      {filteredAndSortedHistory.length !== stats.total && (
                        <span className="text-muted-foreground/70"> • {stats.total} total</span>
                      )}
                      {totalPages > 1 && (
                        <span className="text-muted-foreground/70"> • Page {currentPage + 1} of {totalPages}</span>
                      )}
                    </>
                  ) : (
                    <>{stats.total} total execution{stats.total !== 1 ? 's' : ''}</>
                  )}
                </p>
              </div>
            </div>

            {/* Inline Advanced Filters */}
            {enableAdvancedFilters ? (
              <div className="flex items-center gap-4">
                {/* Status Filter */}
                <div className="flex items-center gap-2">
                  <Filter className="w-4 h-4 text-muted-foreground" />
                  <span className="text-sm font-medium">Filter:</span>
                  <Select value={filterBy} onValueChange={(value) => setFilterBy(value as FilterOption)}>
                    <SelectTrigger className="w-32">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All ({stats.total})</SelectItem>
                      <SelectItem value="succeeded">Success ({stats.succeeded})</SelectItem>
                      <SelectItem value="failed">Failed ({stats.failed})</SelectItem>
                      <SelectItem value="running">Running ({stats.running})</SelectItem>
                      <SelectItem value="cancelled">Cancelled ({stats.cancelled})</SelectItem>
                      <SelectItem value="required_approval">Approval Required ({taskHistory?.filter(h => h.status === 'required_approval').length || 0})</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* Sort Options */}
                <div className="flex items-center gap-2">
                  <SortAsc className="w-4 h-4 text-muted-foreground" />
                  <span className="text-sm font-medium">Sort:</span>
                  <Select value={sortBy} onValueChange={(value) => handleSortByChange(value as SortOption)}>
                    <SelectTrigger className="w-32">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="date">By Date</SelectItem>
                      <SelectItem value="duration">By Duration</SelectItem>
                    </SelectContent>
                  </Select>

                  {/* Sort Order Toggle */}
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={toggleSortOrder}
                    className="flex items-center gap-1.5 px-3 h-8"
                    title={`Sort ${sortOrder === 'desc' ? 'Descending' : 'Ascending'} - Click to toggle`}
                  >
                    {sortOrder === 'desc' ? (
                      <ArrowDown className="w-3.5 h-3.5" />
                    ) : (
                      <ArrowUp className="w-3.5 h-3.5" />
                    )}
                    <span className="text-xs font-medium">
                      {sortOrder === 'desc' ? 'Desc' : 'Asc'}
                    </span>
                  </Button>
                </div>
              </div>
            ) : (
              /* Legacy Sort Button (for backwards compatibility) */
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleSortByChange(sortBy === 'date' ? 'duration' : 'date')}
                  className="flex items-center gap-2"
                >
                  <SortAsc className="w-4 h-4" />
                  {sortBy === 'date' ? 'By Date' : 'By Duration'}
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={toggleSortOrder}
                  className="flex items-center gap-1.5 px-3"
                  title={`Sort ${sortOrder === 'desc' ? 'Descending' : 'Ascending'} - Click to toggle`}
                >
                  {sortOrder === 'desc' ? (
                    <ArrowDown className="w-3.5 h-3.5" />
                  ) : (
                    <ArrowUp className="w-3.5 h-3.5" />
                  )}
                  <span className="text-xs">
                    {sortOrder === 'desc' ? 'Desc' : 'Asc'}
                  </span>
                </Button>
              </div>
            )}
          </div>

          {/* Statistics Bar */}
          <div className="flex items-center gap-4 mt-4 pt-4 border-t">
            <div className="flex items-center gap-2 text-sm">
              <div className="w-3 h-3 rounded-full bg-green-500" />
              <span className="text-muted-foreground">Succeeded:</span>
              <span className="font-medium">{stats.succeeded}</span>
            </div>
            <div className="flex items-center gap-2 text-sm">
              <div className="w-3 h-3 rounded-full bg-red-500" />
              <span className="text-muted-foreground">Failed:</span>
              <span className="font-medium">{stats.failed}</span>
            </div>
            {stats.running > 0 && (
              <div className="flex items-center gap-2 text-sm">
                <div className="w-3 h-3 rounded-full bg-blue-500" />
                <span className="text-muted-foreground">Running:</span>
                <span className="font-medium">{stats.running}</span>
              </div>
            )}
            {stats.cancelled > 0 && (
              <div className="flex items-center gap-2 text-sm">
                <div className="w-3 h-3 rounded-full bg-orange-500" />
                <span className="text-muted-foreground">Cancelled:</span>
                <span className="font-medium">{stats.cancelled}</span>
              </div>
            )}
            {stats.required_approval > 0 && (
              <div className="flex items-center gap-2 text-sm">
                <div className="w-3 h-3 rounded-full bg-purple-700" />
                <span className="text-muted-foreground">Approval Required:</span>
                <span className="font-medium">{stats.required_approval}</span>
              </div>
            )}
          </div>
        </CardHeader>
      </Card>

      {/* Execution Cards with forced update key */}
      <div className="space-y-3" key={`execution-list-${updateKey}`}>
        {paginatedHistory.map((execution, index) => (
          <ExecutionCard
            key={`${execution.id || `execution-${index}`}-${updateKey}`}
            execution={execution}
            index={index}
            isHighlighted={highlightedExecutionId === execution.conversation_id}
            agentId={agentId}
          />
        ))}
      </div>

      {/* Advanced Pagination Controls */}
      {enablePagination && filteredAndSortedHistory.length > pageSize && (
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              {/* Items per page selector */}
              <div className="flex items-center gap-2">
                <span className="text-sm text-muted-foreground">Items per page:</span>
                <Select value={pageSize.toString()} onValueChange={handlePageSizeChange}>
                  <SelectTrigger className="w-20">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="5">5</SelectItem>
                    <SelectItem value="10">10</SelectItem>
                    <SelectItem value="20">20</SelectItem>
                    <SelectItem value="50">50</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Page navigation */}
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handlePageChange(0)}
                  disabled={!hasPrev}
                  className="pagination-button"
                >
                  First
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handlePageChange(currentPage - 1)}
                  disabled={!hasPrev}
                  className="pagination-button"
                >
                  <ChevronLeft className="w-4 h-4" />
                </Button>
                <span className="text-sm text-muted-foreground px-3">
                  Page {currentPage + 1} of {totalPages}
                </span>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handlePageChange(currentPage + 1)}
                  disabled={!hasNext}
                  className="pagination-button"
                >
                  <ChevronRight className="w-4 h-4" />
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handlePageChange(totalPages - 1)}
                  disabled={!hasNext}
                  className="pagination-button"
                >
                  Last
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
});

export default ExecutionHistoryList;
