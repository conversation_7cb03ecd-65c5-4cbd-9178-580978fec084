'use client';
import { ColumnDef } from '@tanstack/react-table';
import { TaskResponse, TaskHistory, TasksService, TaskScheduledStatus } from '@/client';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import {
  CalendarClock,
  CheckCircle,
  Clock,
  RefreshCcw,
  XCircle,
  Timer,
  Activity,
  Eye,
  Trash2,
  Info,
  PlayCircle,
  Pause,
  StopCircle,
  AlertTriangle,
  Square,
  Power,
  PowerOff,
  HandIcon
} from 'lucide-react';
import { DateFormat, formatUtcDate, formatRelativeTime } from '@/lib/date-utils';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { CacheKey } from '@/components/utils/cache-key';
import { toast } from 'sonner';
import { useRouter } from 'next/navigation';
import { useState } from 'react';
import { AlertModal } from '@/components/modal/alert-modal';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';

// Helper function to calculate execution duration in human readable format
const formatDuration = (startTime?: string | null, endTime?: string | null): string => {
  if (!startTime) return 'N/A';

  const start = new Date(startTime);
  const end = endTime ? new Date(endTime) : new Date();
  const durationMs = end.getTime() - start.getTime();

  if (durationMs < 1000) return `${durationMs}ms`;
  if (durationMs < 60000) return `${Math.round(durationMs / 1000)}s`;
  if (durationMs < 3600000) return `${Math.round(durationMs / 60000)}m`;
  return `${Math.round(durationMs / 3600000)}h`;
};

// Unified status color mapping - consistent across dots and blocks
const getStatusColor = (status: string): {
  bg: string;
  hover: string;
  border: string;
  text: string;
  lightBg: string;
  lightText: string;
} => {
  const normalizedStatus = status.toLowerCase();
  switch (normalizedStatus) {
    case 'succeeded':
    case 'completed':
      return {
        bg: 'bg-emerald-500',
        hover: 'hover:bg-emerald-600',
        border: 'border-emerald-500',
        text: 'text-emerald-600',
        lightBg: 'bg-emerald-50 dark:bg-emerald-950/50',
        lightText: 'text-emerald-700 dark:text-emerald-300'
      };
    case 'running':
    case 'in_progress':
      return {
        bg: 'bg-blue-500',
        hover: 'hover:bg-blue-600',
        border: 'border-blue-500',
        text: 'text-blue-600',
        lightBg: 'bg-blue-50 dark:bg-blue-950/50',
        lightText: 'text-blue-700 dark:text-blue-300'
      };
    case 'failed':
      return {
        bg: 'bg-red-500',
        hover: 'hover:bg-red-600',
        border: 'border-red-500',
        text: 'text-red-600',
        lightBg: 'bg-red-50 dark:bg-red-950/20',
        lightText: 'text-red-700 dark:text-red-400'
      };
    case 'cancelled':
      return {
        bg: 'bg-orange-500',
        hover: 'hover:bg-orange-600',
        border: 'border-orange-500',
        text: 'text-orange-600',
        lightBg: 'bg-orange-50 dark:bg-orange-950/50',
        lightText: 'text-orange-700 dark:text-orange-300'
      };
    case 'required_approval':
    case 'required approval':
      return {
        bg: 'bg-purple-500',
        hover: 'hover:bg-purple-600',
        border: 'border-purple-500',
        text: 'text-purple-600',
        lightBg: 'bg-purple-50 dark:bg-purple-950/50',
        lightText: 'text-purple-700 dark:text-purple-300'
      };
    default:
      return {
        bg: 'bg-gray-400',
        hover: 'hover:bg-gray-500',
        border: 'border-gray-400',
        text: 'text-gray-600',
        lightBg: 'bg-gray-50 dark:bg-gray-950/50',
        lightText: 'text-gray-700 dark:text-gray-300'
      };
  }
};

// Helper function for scheduled status badge colors
const getScheduledStatusBadgeClasses = (status?: TaskScheduledStatus | null): string => {
  switch (status) {
    case 'scheduled':
      return 'text-blue-700 dark:text-blue-300 bg-blue-50 dark:bg-blue-950/50 border-blue-200 dark:border-blue-800/50';
    case 'pending':
      return 'text-amber-700 dark:text-amber-300 bg-amber-50 dark:bg-amber-950/50 border-amber-200 dark:border-amber-800/50';
    default:
      return 'text-gray-700 dark:text-gray-300 bg-gray-50 dark:bg-gray-950/50 border-gray-200 dark:border-gray-800/50';
  }
};

// Enhanced helper function to get proper status text with better failed status handling
const getStatusText = (status?: string | null): string => {
  if (!status) return 'pending';

  const normalizedStatus = status.toLowerCase().replace(/_/g, ' ').trim();

  // Handle common failed status variations
  if (normalizedStatus.includes('fail') || normalizedStatus.includes('error')) {
    return 'failed';
  }

  // Handle success variations
  if (normalizedStatus.includes('success') || normalizedStatus === 'completed') {
    return 'succeeded';
  }

  // Handle running variations
  if (normalizedStatus.includes('running') || normalizedStatus.includes('progress')) {
    return 'running';
  }

  // Handle cancelled variations
  if (normalizedStatus.includes('cancel')) {
    return 'cancelled';
  }

  // Handle approval variations
  if (normalizedStatus.includes('approval')) {
    return 'required approval';
  }

  return normalizedStatus;
};



// Compact Task Enable/Disable Toggle for Table
const CompactTaskToggle = ({ task }: { task: TaskResponse }) => {
  const queryClient = useQueryClient();
  const [loading, setLoading] = useState(false);
  const isRunning = task.execution_status === 'running';

  const enableMutation = useMutation({
    mutationFn: ({ id, enable }: { id: string; enable: boolean }) =>
      TasksService.updateTaskEnable({ taskId: id, enable }),
    onSuccess: (updatedTask, { enable }) => {
      if (enable) {
        toast.success('Task Enabled', {
          description: `"${task.title}" is now enabled and will run according to its schedule.`,
          duration: 4000,
        });
      } else {
        toast.success('Task Disabled', {
          description: `"${task.title}" has been disabled. Scheduled executions have been cancelled.`,
          duration: 4000,
        });
      }
    },
    onError: (error: any) => {
      const errorMessage = error?.body?.detail || error?.message || 'Failed to update task status';
      toast.error('Operation Failed', {
        description: `Could not ${task.enable ? 'disable' : 'enable'} task: ${errorMessage}`,
        duration: 6000,
      });
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: ['task', task.id] });
      queryClient.invalidateQueries({ queryKey: [CacheKey.Items] });
    }
  });

  const handleToggle = async () => {
    if (isRunning && task.enable) {
      toast.warning('Task Currently Running', {
        description: `"${task.title}" is currently running. Disabling will prevent future executions.`,
        duration: 5000,
      });
    }

    setLoading(true);
    try {
      await enableMutation.mutateAsync({ id: task.id, enable: !task.enable });
    } finally {
      setLoading(false);
    }
  };

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <button
            onClick={handleToggle}
            disabled={loading}
            className={cn(
              "relative inline-flex h-5 w-9 items-center rounded-full transition-all duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1",
              task.enable
                ? "bg-emerald-600 hover:bg-emerald-700"
                : "bg-gray-300 hover:bg-gray-400 dark:bg-gray-600 dark:hover:bg-gray-500",
              loading && "opacity-50 cursor-not-allowed",
              isRunning && task.enable && "ring-1 ring-blue-400/50"
            )}
            role="switch"
            aria-checked={task.enable}
            aria-label={task.enable ? "Disable task" : "Enable task"}
          >
            <span
              className={cn(
                "inline-block h-3 w-3 transform rounded-full bg-white transition-all duration-200 ease-in-out shadow flex items-center justify-center",
                task.enable ? "translate-x-5" : "translate-x-1"
              )}
            >
              {loading ? (
                <RefreshCcw className="h-1.5 w-1.5" />
              ) : task.enable ? (
                <Power className="h-1.5 w-1.5 text-emerald-600" />
              ) : (
                <PowerOff className="h-1.5 w-1.5 text-gray-400" />
              )}
            </span>
          </button>
        </TooltipTrigger>
        <TooltipContent
          side="top"
          className="max-w-xs bg-card/95 border-border/60 shadow-lg backdrop-blur-sm"
        >
          <div className="text-center space-y-1.5">
            <div className="font-medium text-card-foreground">
              {task.enable ? "Disable Task" : "Enable Task"}
            </div>
            <div className="text-xs text-muted-foreground">
              {task.enable
                ? "Stop scheduled and running executions"
                : "Allow task to run according to schedule"
              }
            </div>
          </div>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
};

// Enhanced Recent Runs Display with Unified Colors and Task Controls
const RecentRunsDisplay = ({
  task,
  taskHistory
}: {
  task: TaskResponse;
  taskHistory?: TaskHistory[]
}) => {
  if (!taskHistory || taskHistory.length === 0) {
    return (
      <div className="flex items-center justify-center">
        <div className="text-center">
        <div className="text-sm text-muted-foreground font-medium italic">No history</div>
        </div>
      </div>
    );
  }

  // Show the most recent 8 task runs for better visual balance
  const recentRuns = taskHistory.slice(0, 8);
  const totalRuns = taskHistory.length;

  // Enhanced status calculation with better failed status handling
  const successfulRuns = recentRuns.filter(run => {
    const status = getStatusText(run.status);
    return status === 'succeeded' || status === 'completed';
  }).length;

  const failedRuns = recentRuns.filter(run => {
    const status = getStatusText(run.status);
    return status === 'failed' || status === 'error';
  }).length;

  const runningRuns = recentRuns.filter(run => {
    const status = getStatusText(run.status);
    return status === 'running' || status === 'in progress';
  }).length;

  const cancelledRuns = recentRuns.filter(run => {
    const status = getStatusText(run.status);
    return status === 'cancelled' || status === 'canceled';
  }).length;

  const successRate = recentRuns.length > 0 ? Math.round((successfulRuns / recentRuns.length) * 100) : 0;

  return (
    <div className="flex flex-col items-center gap-1.5">
      {/* Enhanced Status Legend with counts */}
      <div className="flex items-center gap-2 text-xs text-muted-foreground">
        <div className="flex items-center gap-0.5">
          {successfulRuns > 0 && (
            <div className="flex items-center gap-1">
              <div className="w-2 h-2 rounded-full bg-emerald-500 border border-background/20 shadow-sm" />
              <span className="text-emerald-600 font-medium">{successfulRuns}</span>
            </div>
          )}
          {failedRuns > 0 && (
            <div className="flex items-center gap-1">
              <div className="w-2 h-2 rounded-full bg-red-500 border border-background/20 shadow-sm" />
              <span className="text-red-600 font-medium">{failedRuns}</span>
            </div>
          )}
          {runningRuns > 0 && (
            <div className="flex items-center gap-1">
              <div className="w-2 h-2 rounded-full bg-blue-500 border border-background/20 shadow-sm" />
              <span className="text-blue-600 font-medium">{runningRuns}</span>
            </div>
          )}
          {cancelledRuns > 0 && (
            <div className="flex items-center gap-1">
              <div className="w-2 h-2 rounded-full bg-orange-500 border border-background/20 shadow-sm" />
              <span className="text-orange-600 font-medium">{cancelledRuns}</span>
            </div>
          )}
        </div>
        <span className="text-xs">Recent ({recentRuns.length})</span>
      </div>

      {/* Enhanced Execution History Dots with consistent colors */}
      <div className="flex items-center justify-center gap-1">
        <div className="flex items-center gap-1">
          {recentRuns.map((run: TaskHistory, index: number) => {
            const status = getStatusText(run.status);
            const colors = getStatusColor(status);

            return (
              <div key={run.id || `recent-${index}`} className="flex items-center gap-1">
                <div
                  className={cn(
                    "w-4 h-4 rounded-full border-2 shadow-sm",
                    colors.bg,
                    colors.border
                  )}
                  style={{
                    boxShadow: status === 'running' ? `0 0 8px ${colors.bg.replace('bg-', '')}` : undefined
                  }}
                />
              </div>
            );
          })}
        </div>

        {totalRuns > 8 && (
          <div className="text-xs text-muted-foreground ml-1">
            +{totalRuns - 8}
          </div>
        )}
      </div>

      {/* Enhanced Success Rate with better visual feedback */}
      <div className="text-xs font-medium">
        <span className={cn(
          "px-2 py-0.5 rounded-full text-xs font-medium",
          successRate >= 80 ? "bg-emerald-100 text-emerald-700 dark:bg-emerald-950/50 dark:text-emerald-300" :
          successRate >= 60 ? "bg-amber-100 text-amber-700 dark:bg-amber-950/50 dark:text-amber-300" :
          "bg-red-100 text-red-700 dark:bg-red-950/50 dark:text-red-300"
        )}>
          {successRate}% success
        </span>
      </div>
    </div>
  );
};

// Enhanced Action Buttons Component with enhanced notifications
const TaskActionButtons = ({ task }: { task: TaskResponse }) => {
  const queryClient = useQueryClient();
  const router = useRouter();
  const [deleteModalOpen, setDeleteModalOpen] = useState(false);
  const [loading, setLoading] = useState(false);

  const deleteMutation = useMutation({
    mutationFn: (id: string) => TasksService.deleteTask({ taskId: id }),
    onSuccess: () => {
      // Enhanced success notification
      toast.success('Task Deleted', {
        description: `Task "${task.title}" has been permanently deleted. Any scheduled executions have been cancelled.`,
        duration: 5000,
      });
      setDeleteModalOpen(false);
    },
    onError: (error: any) => {
      // Enhanced error notification
      const errorMessage = error?.body?.detail || error?.message || 'Failed to delete task';
      toast.error('Delete Failed', {
        description: `Could not delete task "${task.title}": ${errorMessage}`,
        duration: 6000,
      });
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: [CacheKey.Items] });
    }
  });

  const handleDelete = async () => {
    setLoading(true);
    try {
      await deleteMutation.mutateAsync(task.id);
    } finally {
      setLoading(false);
    }
  };

  const handleViewOverview = () => {
    router.push(`/tasks/${task.id}`);
  };

  // Show warning if task has scheduled/running executions
  const hasActiveExecutions = task.scheduled_status === 'scheduled' || task.execution_status === 'running';

  return (
    <>
      <AlertModal
        isOpen={deleteModalOpen}
        onClose={() => setDeleteModalOpen(false)}
        onConfirm={handleDelete}
        loading={loading}
        title="Delete Task"
        description={hasActiveExecutions
          ? `Are you sure you want to delete "${task.title}"? This task has active scheduled executions that will be cancelled. This action cannot be undone.`
          : `Are you sure you want to delete "${task.title}"? This action cannot be undone.`
        }
      />

      <div className="flex items-center gap-2">
        {/* Overview Button - Enhanced with better styling */}
        <Button
          variant="outline"
          size="sm"
          onClick={handleViewOverview}
          className={cn(
            "h-8 px-3 flex items-center gap-1.5",
            "border-blue-200 text-blue-700 hover:bg-blue-50 hover:border-blue-300",
            "dark:border-blue-800/50 dark:text-blue-300 dark:hover:bg-blue-950/50 dark:hover:border-blue-700",
            "transition-all duration-200 shadow-sm hover:shadow-md"
          )}
          title="View task overview and details"
        >
          <Eye className="h-3.5 w-3.5" />
          <span className="text-xs font-medium">View</span>
        </Button>

        {/* Delete Button - Enhanced with red styling and warning for active tasks */}
        <Button
          variant="outline"
          size="sm"
          onClick={() => setDeleteModalOpen(true)}
          disabled={loading}
          className={cn(
            "h-8 px-3 flex items-center gap-1.5",
            "border-red-200 text-red-700 hover:bg-red-50 hover:border-red-300",
            "dark:border-red-800/50 dark:text-red-300 dark:hover:bg-red-950/50 dark:hover:border-red-700",
            "transition-all duration-200 shadow-sm hover:shadow-md",
            loading && "opacity-50 cursor-not-allowed",
            hasActiveExecutions && "border-orange-200 text-orange-700 hover:bg-orange-50 hover:border-orange-300"
          )}
          title={hasActiveExecutions
            ? "Delete this task (has active executions that will be cancelled)"
            : "Delete this task permanently"
          }
        >
          {hasActiveExecutions ? (
            <AlertTriangle className="h-3.5 w-3.5" />
          ) : (
            <Trash2 className="h-3.5 w-3.5" />
          )}
          <span className="text-xs font-medium">Delete</span>
        </Button>
      </div>
    </>
  );
};

// TypeScript interface extension for TaskResponse with task_history
interface TaskResponseWithHistory extends TaskResponse {
  task_history?: TaskHistory[];
}

export const columns: ColumnDef<TaskResponseWithHistory>[] = [
  {
    accessorKey: 'title',
    header: () => (
      <div className="flex items-center gap-2 px-4">
        <span className="font-semibold text-foreground text-xs tracking-wider uppercase">Task Name</span>
      </div>
    ),
    size: 350,
    cell: ({ row }) => {
      const task = row.original;
      const title = row.getValue('title') as string;
      const description = task.description;
      const priority = task.priority;

      return (
        <div className="flex flex-col space-y-1 px-4">
          <div className="flex items-center gap-2">
            <div className="font-semibold text-foreground text-sm truncate max-w-[300px]">
              {title}
            </div>
          </div>
          {description && (
            <div className="text-xs text-muted-foreground line-clamp-2 max-w-[320px]">
              {description}
            </div>
          )}
        </div>
      );
    },
    sortingFn: 'text'
  },
  {
    accessorKey: 'execution_status',
    header: () => (
      <div className="flex items-center justify-center gap-2 px-3">
        <Activity className="h-4 w-4 text-muted-foreground" />
        <span className="font-semibold text-foreground text-xs tracking-wider uppercase">Status</span>
      </div>
    ),
    size: 120,
    cell: ({ row }) => {
      const status = row.getValue('execution_status') as string;
      const statusText = getStatusText(status);
      const colors = getStatusColor(statusText);

      const getStatusIcon = () => {
        switch (statusText) {
          case 'succeeded':
            return <CheckCircle className="h-3.5 w-3.5" />;
          case 'failed':
            return <XCircle className="h-3.5 w-3.5" />;
          case 'running':
            return <RefreshCcw className="h-3.5 w-3.5" />;
          case 'cancelled':
            return <StopCircle className="h-3.5 w-3.5" />;
          case 'required approval':
            return <HandIcon className="h-3.5 w-3.5" />;
          default:
            return <Clock className="h-3.5 w-3.5" />;
        }
      };

      if (!status) {
        return (
          <div className="flex items-center justify-center px-3">
            <Badge variant="outline" className="text-xs font-medium">
              <Clock className="h-3 w-3 mr-1" />
              Pending
            </Badge>
          </div>
        );
      }

      return (
        <div className="flex items-center justify-center px-3">
          <Badge
            variant="outline"
            className={cn(
              "text-xs font-medium border-2 transition-all duration-200",
              colors.lightText,
              colors.lightBg,
              colors.border.replace('border-', 'border-') + '/30'
            )}
          >
            {getStatusIcon()}
            <span className="ml-1 capitalize">{statusText}</span>
          </Badge>
        </div>
      );
    },
    sortingFn: 'basic'
  },
  {
    accessorKey: 'scheduled_status',
    header: () => (
      <div className="flex items-center justify-center gap-2 px-3">
        <CalendarClock className="h-4 w-4 text-muted-foreground" />
        <span className="font-semibold text-foreground text-xs tracking-wider uppercase">Scheduled</span>
      </div>
    ),
    size: 160,
    cell: ({ row }) => {
      const scheduledStatus = row.getValue('scheduled_status') as TaskScheduledStatus;
      const enabled = row.original.enable;
      const task = row.original;

      const getScheduledIcon = () => {
        switch (scheduledStatus) {
          case 'scheduled':
            return <CalendarClock className="h-3.5 w-3.5" />;
          case 'pending':
            return <Clock className="h-3.5 w-3.5" />;
          default:
            return <Clock className="h-3.5 w-3.5" />;
        }
      };

      return (
        <div className="flex flex-col items-center gap-2 px-3">
          {/* Status Badge */}
          <div className="flex items-center justify-center">
            {!enabled ? (
              <Badge variant="outline" className="text-xs font-medium text-muted-foreground">
                <PowerOff className="h-3 w-3 mr-1" />
                Disabled
              </Badge>
            ) : (
              <Badge variant="outline" className="text-xs font-medium text-muted-foreground bg-green-100 text-green-700">
                <Power className="h-3 w-3 mr-1" />
                Enabled
              </Badge>
            )}
          </div>

          {/* Compact Toggle */}
          <CompactTaskToggle task={task} />
        </div>
      );
    },
    sortingFn: 'basic'
  },
  {
    accessorKey: 'average_runtime',
    header: () => (
      <div className="flex items-center justify-center gap-2 px-3">
        <Timer className="h-4 w-4 text-muted-foreground" />
        <span className="font-semibold text-foreground text-xs tracking-wider uppercase">Duration</span>
      </div>
    ),
    size: 120,
    cell: ({ row }) => {
      const taskHistory = row.original.task_history;

      if (!taskHistory || taskHistory.length === 0) {
        return (
          <div className="text-center px-3">
            <div className="text-sm text-muted-foreground font-medium italic">No data</div>
          </div>
        );
      }

      // Calculate average runtime from completed executions
      const completedRuns = taskHistory.filter(run =>
        run.start_time && run.end_time &&
        (getStatusText(run.status) === 'succeeded' || getStatusText(run.status) === 'failed' || getStatusText(run.status) === 'cancelled')
      );

      if (completedRuns.length === 0) {
        return (
          <div className="text-center px-3">
            <div className="text-sm text-muted-foreground font-medium italic">No completed runs</div>
          </div>
        );
      }

      // Calculate average duration in milliseconds
      const totalDuration = completedRuns.reduce((sum, run) => {
        const start = new Date(run.start_time!);
        const end = new Date(run.end_time!);
        return sum + (end.getTime() - start.getTime());
      }, 0);

      const averageDurationMs = totalDuration / completedRuns.length;

      // Format average duration
      const formatAverageDuration = (durationMs: number): string => {
        if (durationMs < 1000) return `${Math.round(durationMs)}ms`;
        if (durationMs < 60000) return `${Math.round(durationMs / 1000)}s`;
        if (durationMs < 3600000) return `${Math.round(durationMs / 60000)}m`;
        return `${Math.round(durationMs / 3600000)}h`;
      };

      const averageDuration = formatAverageDuration(averageDurationMs);

      return (
        <div className="text-center px-3">
          <div className="font-mono text-sm font-semibold text-foreground">
            {averageDuration}
          </div>
          <div className="text-xs text-muted-foreground">
            {completedRuns.length} run{completedRuns.length !== 1 ? 's' : ''}
          </div>
        </div>
      );
    },
    sortingFn: 'basic'
  },
  {
    accessorKey: 'recent_tasks',
    header: () => (
      <div className="flex items-center justify-center gap-2 px-3">
        <Activity className="h-4 w-4 text-muted-foreground" />
        <span className="font-semibold text-foreground text-xs tracking-wider uppercase">Recent Activity</span>
      </div>
    ),
    size: 200,
    cell: ({ row }) => (
      <div className="px-3">
        <RecentRunsDisplay
          task={row.original}
          taskHistory={row.original.task_history}
        />
      </div>
    ),
    sortingFn: 'basic'
  },
  {
    accessorKey: 'last_run',
    header: () => (
      <div className="flex items-center justify-center gap-2 px-3">
        <Clock className="h-4 w-4 text-muted-foreground" />
        <span className="font-semibold text-foreground text-xs tracking-wider uppercase">Last Run</span>
      </div>
    ),
    size: 150,
    cell: ({ row }) => {
      const lastRun = row.getValue('last_run');
      const taskHistory = row.original.task_history;
      const latestExecution = taskHistory && taskHistory.length > 0 ? taskHistory[0] : null;

      if (!lastRun) {
        return (
          <div className="text-center text-sm text-muted-foreground font-medium italic px-3">
            Never executed
          </div>
        );
      }

      const formattedDate = formatUtcDate(lastRun as string, DateFormat.DATE_TIME);

      return (
        <div className="text-center px-3">
          <div className="text-sm font-semibold">
            {formattedDate}
          </div>
          {latestExecution && latestExecution.status && (
            <div className={cn(
              "text-xs capitalize font-medium",
              getStatusColor(getStatusText(latestExecution.status)).text
            )}>
              {getStatusText(latestExecution.status)}
            </div>
          )}
        </div>
      );
    },
    sortingFn: 'datetime'
  },
  {
    accessorKey: 'next_run',
    header: () => (
      <div className="flex items-center justify-center gap-2 px-3">
        <CalendarClock className="h-4 w-4 text-muted-foreground" />
        <span className="font-semibold text-foreground text-xs tracking-wider uppercase">Next Run</span>
      </div>
    ),
    size: 150,
    cell: ({ row }) => {
      const nextRun = row.getValue('next_run');
      const enabled = row.original.enable;

      if (!enabled) {
        return (
          <div className="text-center px-3">
            <span className="text-sm text-muted-foreground font-medium italic">Disabled</span>
          </div>
        );
      }

      if (!nextRun) {
        return (
          <div className="text-center px-3">
            <span className="text-sm text-muted-foreground font-medium italic">Disabled</span>
          </div>
        );
      }

      return (
        <div className="text-center px-3">
          <div className="text-sm font-semibold">
            {formatUtcDate(nextRun as string, DateFormat.DATE_TIME)}
          </div>
        </div>
      );
    },
    sortingFn: 'datetime'
  },
  {
    id: 'actions',
    header: () => (
      <div className="flex items-center justify-center gap-2 px-4">
        <span className="font-semibold text-foreground text-xs tracking-wider uppercase">Actions</span>
      </div>
    ),
    size: 170,
    cell: ({ row }) => (
      <div className="flex items-center justify-center px-4">
        <TaskActionButtons task={row.original} />
      </div>
    )
  }
];
