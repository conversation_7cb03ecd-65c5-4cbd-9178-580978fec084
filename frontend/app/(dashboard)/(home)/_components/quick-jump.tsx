'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useQueryClient } from '@tanstack/react-query';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle
} from '@/components/ui/dialog';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import {
  AutonomousAgentsService,
  TasksService,
  TaskTemplatesService
} from '@/client';
import { Bot, Calendar, Check, Loader, Play, Globe, Save } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { cn } from '@/lib/utils';
import { QuickJumpDialogProps } from '@/types/quick-view';
import { useAgents } from '@/hooks/use-agents';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { TaskCategory, TaskService } from '@/constants/task';
import { TaskCouldEnumSchema } from '@/client/schemas.gen';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from '@/components/ui/form';
import { CronInput } from '@/components/ui/cron-input';
import cronstrue from 'cronstrue';

// Utility function to convert local cron expression to UTC based on selected timezone
const convertCronToUTC = (
  cronExpression: string,
  timezoneOffset?: number
): string => {
  // Parse the cron expression
  const parts = cronExpression.trim().split(/\s+/);

  // If it's not a valid cron expression with 5 parts, return as is
  if (parts.length !== 5) {
    return cronExpression;
  }

  // Get timezone offset in minutes - use provided offset or default to local
  const offsetMinutes =
    timezoneOffset !== undefined
      ? timezoneOffset
      : new Date().getTimezoneOffset();

  // Only need to adjust the hour part (parts[1]) if it's a specific hour
  // If the hour part is not a specific hour (e.g., */1, *), we don't need to adjust
  if (
    parts[1] !== '*' &&
    !parts[1].includes('*/') &&
    !parts[1].includes(',') &&
    !parts[1].includes('-')
  ) {
    // Convert hour to number, adjust for UTC, and ensure it's within 0-23 range
    let hour = parseInt(parts[1], 10);
    hour = (hour + Math.floor(offsetMinutes / 60)) % 24;
    if (hour < 0) hour += 24;
    parts[1] = hour.toString();
  }

  // For more complex hour expressions, we would need more sophisticated parsing
  // This simple implementation handles the basic case of a specific hour

  return parts.join(' ');
};

// Comprehensive list of timezones organized by region
const allTimezones = [
  // Local timezone
  {
    label: 'Local',
    value: 'local',
    offset: new Date().getTimezoneOffset(),
    region: 'System'
  },
  { label: 'UTC', value: 'UTC', offset: 0, region: 'UTC' },

  // North America
  {
    label: 'Atlantic (UTC-4)',
    value: 'AST',
    offset: -4 * 60,
    region: 'Americas'
  },
  {
    label: 'Eastern (UTC-5)',
    value: 'EST',
    offset: -5 * 60,
    region: 'Americas'
  },
  {
    label: 'Central (UTC-6)',
    value: 'CST',
    offset: -6 * 60,
    region: 'Americas'
  },
  {
    label: 'Mountain (UTC-7)',
    value: 'MST',
    offset: -7 * 60,
    region: 'Americas'
  },
  {
    label: 'Pacific (UTC-8)',
    value: 'PST',
    offset: -8 * 60,
    region: 'Americas'
  },
  {
    label: 'Alaska (UTC-9)',
    value: 'AKST',
    offset: -9 * 60,
    region: 'Americas'
  },
  {
    label: 'Hawaii (UTC-10)',
    value: 'HST',
    offset: -10 * 60,
    region: 'Americas'
  },

  // Europe & Africa
  {
    label: 'Western European (UTC+0)',
    value: 'WET',
    offset: 0,
    region: 'Europe/Africa'
  },
  {
    label: 'Central European (UTC+1)',
    value: 'CET',
    offset: 1 * 60,
    region: 'Europe/Africa'
  },
  {
    label: 'Eastern European (UTC+2)',
    value: 'EET',
    offset: 2 * 60,
    region: 'Europe/Africa'
  },
  {
    label: 'Moscow (UTC+3)',
    value: 'MSK',
    offset: 3 * 60,
    region: 'Europe/Africa'
  },
  {
    label: 'South Africa (UTC+2)',
    value: 'SAST',
    offset: 2 * 60,
    region: 'Europe/Africa'
  },
  {
    label: 'East Africa (UTC+3)',
    value: 'EAT',
    offset: 3 * 60,
    region: 'Europe/Africa'
  },

  // Asia
  { label: 'Dubai (UTC+4)', value: 'GST', offset: 4 * 60, region: 'Asia' },
  { label: 'Pakistan (UTC+5)', value: 'PKT', offset: 5 * 60, region: 'Asia' },
  { label: 'India (UTC+5:30)', value: 'IST', offset: 5.5 * 60, region: 'Asia' },
  { label: 'Bangladesh (UTC+6)', value: 'BST', offset: 6 * 60, region: 'Asia' },
  {
    label: 'Thailand/Vietnam (UTC+7)',
    value: 'ICT',
    offset: 7 * 60,
    region: 'Asia'
  },
  {
    label: 'China/Philippines (UTC+8)',
    value: 'CST',
    offset: 8 * 60,
    region: 'Asia'
  },
  {
    label: 'Japan/Korea (UTC+9)',
    value: 'JST',
    offset: 9 * 60,
    region: 'Asia'
  },

  // Oceania
  {
    label: 'Australia Western (UTC+8)',
    value: 'AWST',
    offset: 8 * 60,
    region: 'Oceania'
  },
  {
    label: 'Australia Central (UTC+9:30)',
    value: 'ACST',
    offset: 9.5 * 60,
    region: 'Oceania'
  },
  {
    label: 'Australia Eastern (UTC+10)',
    value: 'AEST',
    offset: 10 * 60,
    region: 'Oceania'
  },
  {
    label: 'New Zealand (UTC+12)',
    value: 'NZST',
    offset: 12 * 60,
    region: 'Oceania'
  },

  // South America
  {
    label: 'Argentina (UTC-3)',
    value: 'ART',
    offset: -3 * 60,
    region: 'South America'
  },
  {
    label: 'Brazil Eastern (UTC-3)',
    value: 'BRT',
    offset: -3 * 60,
    region: 'South America'
  },
  {
    label: 'Chile (UTC-4)',
    value: 'CLT',
    offset: -4 * 60,
    region: 'South America'
  },
  {
    label: 'Peru (UTC-5)',
    value: 'PET',
    offset: -5 * 60,
    region: 'South America'
  }
];

// Group timezones by region for the dropdown
const groupedTimezones = allTimezones.reduce(
  (acc, timezone) => {
    if (!acc[timezone.region]) {
      acc[timezone.region] = [];
    }
    acc[timezone.region].push(timezone);
    return acc;
  },
  {} as Record<string, typeof allTimezones>
);

// Order of regions in the dropdown
const regionOrder = [
  'System',
  'UTC',
  'Americas',
  'Europe/Africa',
  'Asia',
  'Oceania',
  'South America'
];

// Simplified Agent Card for selection
const SelectableAgentCard = ({
  agent,
  selected,
  onSelect
}: {
  agent: any;
  selected: boolean;
  onSelect: () => void;
}) => (
  <Card
    className={cn(
      'cursor-pointer transition-colors hover:border-primary',
      selected && 'border-primary bg-primary/5'
    )}
    onClick={onSelect}
  >
    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
      <div className="flex min-w-0 flex-1 items-center gap-2">
        <Bot
          className={cn(
            'h-6 w-6',
            selected ? 'text-primary' : 'text-muted-foreground'
          )}
        />
        <p className="truncate font-medium tracking-tight">{agent.title}</p>
      </div>
      {selected && <Check className="h-5 w-5 text-primary" />}
    </CardHeader>
    <CardContent className="pt-2">
      <p className="line-clamp-2 text-sm text-muted-foreground">
        {agent.description}
      </p>
      <div className="mt-2 flex flex-wrap gap-2">
        <Badge variant="outline">{agent.type}</Badge>
        <Badge variant="secondary">{agent.status}</Badge>
      </div>
    </CardContent>
  </Card>
);

// Loading skeleton for agent cards
const LoadingSkeleton = () => (
  <div className="space-y-4">
    {[1, 2, 3].map((i) => (
      <Card key={i}>
        <CardHeader className="flex flex-row items-center space-y-0 pb-2">
          <div className="flex flex-1 items-center gap-2">
            <Skeleton className="h-6 w-6 rounded" />
            <Skeleton className="h-4 w-[150px]" />
          </div>
        </CardHeader>
        <CardContent className="pt-2">
          <Skeleton className="mb-2 h-4 w-full" />
          <Skeleton className="h-4 w-2/3" />
        </CardContent>
      </Card>
    ))}
  </div>
);

// Add form schema
const formSchema = z.object({
  message: z.string().min(1, 'Message is required'),
  schedule: z.string().optional(),
  timezone: z
    .object({
      label: z.string(),
      value: z.string(),
      offset: z.number(),
      region: z.string()
    })
    .default(allTimezones[0]),
  service_name: z.string().default('cloudthinker'),
  cloud: z.enum(TaskCouldEnumSchema.enum).default('AWS')
});

type FormData = z.infer<typeof formSchema>;

export function QuickJumpDialog({
  open,
  onOpenChange,
  initialMessage = '',
  title = 'Start a New Chat',
  task_title = '',
  isGenerated = false
}: QuickJumpDialogProps) {
  const router = useRouter();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [startLoading, setStartLoading] = useState(false);
  const [scheduleLoading, setScheduleLoading] = useState(false);
  const [saveLoading, setSaveLoading] = useState(false);

  const [isCronValid, setIsCronValid] = useState(true);

  // Initialize form
  const form = useForm<FormData>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      message: initialMessage,
      schedule: '*/30 * * * *',
      timezone: allTimezones[0],
      service_name: 'cloudthinker',
      cloud: 'AWS'
    }
  });

  // Use our custom hook with caching - always enabled to ensure data is available
  const { data: agentsResponse, isLoading } = useAgents({
    limit: 10,
    refetchOnWindowFocus: false
  });

  // Update message when initialMessage changes
  useEffect(() => {
    if (open) {
      form.setValue('message', initialMessage);
    }
  }, [open, initialMessage, form]);

  // Reset dialog state when dialog opens/closes
  useEffect(() => {
    if (!open) {
      // Reset state when dialog closes
      form.reset({
        message: '',
        schedule: '*/30 * * * *',
        timezone: allTimezones[0],
        service_name: 'cloudthinker',
        cloud: 'AWS'
      });
      setIsCronValid(true);
    }
  }, [open, form]);

  const selectedAgent = agentsResponse?.data?.find(
    (agent) => agent.type === 'autonomous_agent'
  );
  const isAutonomousAgent = selectedAgent?.type === 'autonomous_agent';

  const handleStartNow = async (e: React.FormEvent) => {
    e.preventDefault();
    const values = form.getValues() as FormData;

    if (!selectedAgent?.id) {
      toast({
        variant: 'destructive',
        title: 'Error',
        description: 'No agent available'
      });
      return;
    }

    setStartLoading(true);

    try {
      const conversation = await AutonomousAgentsService.createConversation({
        requestBody: {
          agent_id: selectedAgent?.id
        }
      });

      const baseUrl = `/agents/${selectedAgent?.id}`;
      const params = new URLSearchParams();
      params.append('conversation', conversation.id);

      if (values.message.trim()) {
        params.append('initialMessage', values.message.trim());
      }

      if (selectedAgent?.type === 'autonomous_agent') {
        params.append('autonomous', 'true');
      }

      toast({
        title: 'Loading...',
        description: 'Preparing your conversation...'
      });

      onOpenChange(false);
      router.push(`${baseUrl}?${params.toString()}`);
    } catch (error) {
      toast({
        variant: 'destructive',
        title: 'Error',
        description: 'Failed to create conversation'
      });
    } finally {
      setStartLoading(false);
    }
  };

  const handleSchedule = async (e: React.FormEvent) => {
    e.preventDefault();
    const values = form.getValues() as FormData;

    if (!selectedAgent?.id || !values.schedule) {
      toast({
        variant: 'destructive',
        title: 'Error',
        description: !selectedAgent?.id
          ? 'No agent available'
          : 'Please set a schedule'
      });
      return;
    }

    if (!isCronValid) {
      toast({
        variant: 'destructive',
        title: 'Invalid Schedule',
        description: 'Please enter a valid cron expression'
      });
      return;
    }

    setScheduleLoading(true);

    try {
      const utcSchedule = convertCronToUTC(
        values.schedule,
        values.timezone.offset
      );

      await TasksService.createTask({
        requestBody: {
          title: task_title || 'Scheduled Task',
          description: values.message,
          schedule: utcSchedule,
          agent_config: {
            agent_id: selectedAgent?.id,
            message: values.message,
            conversation_name: task_title || 'Scheduled Task'
          }
        }
      });

      toast({
        title: 'Success',
        description: 'Task scheduled successfully'
      });
      onOpenChange(false);
      router.push('/tasks?refresh=true');
    } catch (error) {
      toast({
        variant: 'destructive',
        title: 'Error',
        description: 'Failed to schedule task'
      });
    } finally {
      setScheduleLoading(false);
    }
  };

  const handleSaveTask = async (e: React.FormEvent) => {
    e.preventDefault();
    const values = form.getValues() as FormData;

    if (!values.message.trim()) {
      toast({
        variant: 'destructive',
        title: 'Error',
        description: 'Please enter a message'
      });
      return;
    }

    // Validate cron expression if present
    if (values.schedule) {
      try {
        cronstrue.toString(values.schedule, { verbose: true });
      } catch (err) {
        console.error(err);
        toast({
          variant: 'destructive',
          title: 'Invalid cron expression',
          description: 'Please enter a valid cron expression.'
        });
        return;
      }
    }

    setSaveLoading(true);

    try {
      await TaskTemplatesService.createTemplate({
        requestBody: {
          task: task_title || values.message.split('\n')[0] || 'New Task',
          category: TaskCategory.OTHER,
          context: values.message.trim(),
          run_mode: 'agent',
          // service: TaskService.OTHER,
          service_name: values.service_name,
          cloud: values.cloud,
          schedule: values.schedule
        }
      });

      queryClient.invalidateQueries({ queryKey: ['task_templates'] });

      toast({
        title: 'Task saved successfully',
        description: 'Your task has been saved and will appear in saved tasks.'
      });

      onOpenChange(false);
    } catch (error) {
      console.error('Failed to save task:', error);
      toast({
        variant: 'destructive',
        title: 'Error',
        description: 'Failed to save the task. Please try again.'
      });
    } finally {
      setSaveLoading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>{title}</DialogTitle>
          <DialogDescription>
            Enter your message below.
            {isAutonomousAgent &&
              ' You can also schedule tasks with this autonomous agent.'}
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form className="grid gap-4 py-4">
            {isLoading ? (
              <div className="flex justify-center p-4">
                <Loader className="h-6 w-6 animate-spin text-primary" />
              </div>
            ) : !selectedAgent || !isAutonomousAgent ? (
              <div className="flex justify-center p-4 text-muted-foreground">
                {agentsResponse?.data?.length === 0
                  ? 'No agents available'
                  : 'No autonomous agent available'}
              </div>
            ) : (
              <>
                <FormField
                  control={form.control}
                  name="message"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Message</FormLabel>
                      <FormControl>
                        <Textarea
                          {...field}
                          disabled={startLoading || scheduleLoading}
                          className="custom-scrollbar resize-vertical max-h-[300px] min-h-[100px] overflow-y-auto"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {isAutonomousAgent && (
                  <div className="space-y-2">
                    <FormField
                      control={form.control}
                      name="schedule"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Schedule</FormLabel>
                          <FormControl>
                            <CronInput
                              value={field.value ?? ''}
                              onChange={field.onChange}

                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="timezone"
                      render={({ field }) => (
                        <FormItem>
                          <div className="mt-1 flex items-center gap-2">
                            <div className="flex items-center text-xs text-muted-foreground">
                              <Globe className="mr-1 h-3 w-3" />
                              <span>Timezone:</span>
                            </div>
                            <Select
                              value={field.value.value}
                              onValueChange={(value) => {
                                const timezone = allTimezones.find(
                                  (tz) => tz.value === value
                                );
                                if (timezone) {
                                  field.onChange(timezone);
                                }
                              }}
                            >
                              <FormControl>
                                <SelectTrigger className="h-7 w-[140px] text-xs">
                                  <SelectValue placeholder="Select timezone" />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent className="max-h-[300px]">
                                {regionOrder.map((region) => (
                                  <div key={region}>
                                    {region !== 'System' &&
                                      region !== 'UTC' && (
                                        <div className="px-2 py-1.5 text-xs font-semibold text-muted-foreground">
                                          {region}
                                        </div>
                                      )}
                                    {groupedTimezones[region]?.map(
                                      (timezone) => (
                                        <SelectItem
                                          key={timezone.value}
                                          value={timezone.value}
                                          className="text-xs"
                                        >
                                          {timezone.label}
                                        </SelectItem>
                                      )
                                    )}
                                    {region !==
                                      regionOrder[regionOrder.length - 1] && (
                                      <div className="my-1 h-px bg-border" />
                                    )}
                                  </div>
                                ))}
                              </SelectContent>
                            </Select>
                            <div className="text-xs text-muted-foreground">
                              (Schedule is in {field.value.label} time)
                            </div>
                          </div>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                )}
              </>
            )}

            <DialogFooter>
              <Button
                onClick={handleSaveTask}
                disabled={
                  saveLoading ||
                  !form.getValues().message.trim() ||
                  startLoading ||
                  scheduleLoading
                }
                title={
                  !form.getValues().message.trim()
                    ? 'Please enter a message'
                    : saveLoading
                    ? 'Saving...'
                    : 'Save this as a task'
                }
                variant="outline"
              >
                {saveLoading ? (
                  <>
                    <Loader className="mr-2 h-4 w-4 animate-spin" />
                    Saving...
                  </>
                ) : (
                  <>
                    <Save className="mr-2 h-4 w-4" />
                    Save Task
                  </>
                )}
              </Button>

              {isAutonomousAgent && (
                <Button
                  onClick={handleSchedule}
                  disabled={
                    scheduleLoading ||
                    startLoading ||
                    saveLoading ||
                    !selectedAgent?.id ||
                    !form.getValues().schedule ||
                    !isCronValid
                  }
                  title={
                    !selectedAgent?.id
                      ? 'No agent available'
                      : !form.getValues().schedule
                      ? 'Please set a schedule'
                      : !isCronValid
                      ? 'Invalid cron expression'
                      : scheduleLoading
                      ? 'Processing...'
                      : 'Schedule this task'
                  }
                >
                  {scheduleLoading ? (
                    <>
                      <Loader className="mr-2 h-4 w-4 animate-spin" />
                      Scheduling...
                    </>
                  ) : (
                    <>
                      <Calendar className="mr-2 h-4 w-4" />
                      Schedule Task
                    </>
                  )}
                </Button>
              )}

              {isAutonomousAgent && !isGenerated && (
                <Button
                  onClick={handleStartNow}
                  disabled={
                    startLoading ||
                    scheduleLoading ||
                    saveLoading ||
                    !selectedAgent?.id ||
                    !isAutonomousAgent
                  }
                  title={
                    !selectedAgent?.id
                      ? 'No agent available'
                      : !isAutonomousAgent
                      ? 'Selected agent is not autonomous'
                      : startLoading
                      ? 'Creating...'
                      : 'Start a new conversation now'
                  }
                >
                  {startLoading ? (
                    <>
                      <Loader className="mr-2 h-4 w-4 animate-spin" />
                      Creating...
                    </>
                  ) : (
                    <>
                      <Play className="mr-2 h-4 w-4" />
                      Start Now
                    </>
                  )}
                </Button>
              )}
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
