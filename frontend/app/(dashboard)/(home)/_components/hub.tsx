'use client';

import { use<PERSON><PERSON>back, useMemo, useState, useRef, useEffect } from 'react';
import { QuickStartCard } from './quick-start-card';
import { Icons } from '@/components/icons';
import debounce from 'lodash/debounce';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { RecentTask } from './quick-start';
import { TaskCategory } from '@/constants/task';
import { getCategoryLabel } from '@/utils/task-labels';
import { getCategoryIcon } from '@/utils/task-labels';
import { getServiceLabel } from '@/utils/task-labels';
import { getServiceIcon } from '@/utils/task-labels';
import { TaskService } from '@/constants/task';
import { type TaskTemplate } from '@/types/task-template';
import { InfiniteData } from '@tanstack/react-query';
import { ListTemplatesResponse } from '@/client';
import {
  Popover,
  PopoverContent,
  PopoverTrigger
} from '@/components/ui/popover';
import { Checkbox } from '@/components/ui/checkbox';
import { Button } from '@/components/ui/button';
import React from 'react';
import type { LucideIcon } from 'lucide-react';
import { useInView } from 'react-intersection-observer';
import { useInfiniteTaskTemplates } from '@/hooks/use-infinite-task-templates';
import { Skeleton } from '@/components/ui/skeleton';

export interface QuickStartCardType {
  icon: LucideIcon;
  title: string;
  description: string;
  category?: string;
  service?: string;
  functionType: 'agent' | 'autonomous';
  isPopular?: boolean;
  isRecentTask?: boolean;
  id?: string;
}

interface HubProps {
  onCardClick: (card: QuickStartCardType) => void;
  isLoading: Boolean;
  recentTasks?: RecentTask[];
}

export function Hub({ onCardClick }: HubProps) {
  const [selectedCategory, setSelectedCategory] = useState<TaskCategory | null>(
    null
  );
  const [selectedServices, setSelectedServices] = useState<TaskService[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [includeDefaults, setIncludeDefaults] = useState(true);

  const { ref, inView } = useInView({
    threshold: 0.1
  });

  const { data, fetchNextPage, hasNextPage, isFetchingNextPage, isPending } =
    useInfiniteTaskTemplates({
      searchQuery,
      category: selectedCategory || undefined,
      services: selectedServices.length > 0 ? selectedServices : undefined,
      pageSize: 12,
      includeDefaults
    });

  // Load more when the load more element comes into view
  useEffect(() => {
    if (inView && hasNextPage && !isFetchingNextPage) {
      fetchNextPage();
    }
  }, [inView, hasNextPage, isFetchingNextPage, fetchNextPage]);

  // Debounced search handler
  const handleSearch = useMemo(
    () =>
      debounce((value: string) => {
        setSearchQuery(value);
      }, 100),
    []
  );

  const onSearchChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      handleSearch(e.target.value);
    },
    [handleSearch]
  );

  const toggleCategory = (categoryValue: TaskCategory) => {
    if (categoryValue === selectedCategory) {
      setSelectedCategory(null);
    } else {
      setSelectedCategory(categoryValue);
    }
  };

  const categoryOptions = Object.values(TaskCategory).map((category) => ({
    value: category,
    label: getCategoryLabel(category),
    icon: getCategoryIcon(category)
  }));

  const serviceOptions = Object.values(TaskService).map((service) => ({
    value: service,
    label: getServiceLabel(service),
    icon: getServiceIcon(service)
  }));

  const allTemplates = useMemo(() => {
    if (!data) return [];
    let templates = (data as InfiniteData<ListTemplatesResponse>).pages.flatMap(
      (page) => page.data
    );
    return templates;
  }, [data]);

  // Count active advanced filters
  const advancedFilterCount = useMemo(() => {
    let count = 0;
    if (selectedServices.length > 0) count += selectedServices.length;
    if (includeDefaults !== true) count += 1;
    return count;
  }, [selectedServices, includeDefaults]);

  // Reset advanced filters
  const resetAdvancedFilters = () => {
    setSelectedServices([]);
    setIncludeDefaults(true);
  };

  return (
    <div className="relative min-h-[calc(100dvh-4rem)]">
      {/* Title section - not sticky */}
      <div className="mx-auto max-w-screen-lg px-4 py-4">
        <h1 className="text-center text-3xl font-semibold">Operations Hub</h1>
      </div>

      <div className="sticky top-0 z-10 bg-background/50 backdrop-blur-sm py-2 flex flex-col items-center">
        <div className="flex w-full max-w-screen-lg justify-center gap-2">
          <div className="relative w-3/5">
            <Icons.search className="absolute left-3 top-1/2 h-5 w-5 -translate-y-1/2 transform text-muted-foreground" />
            <Input
              type="text"
              placeholder="Search operations..."
              className="h-12 w-full rounded-3xl pl-10"
              onChange={onSearchChange}
              aria-label="Search operations"
            />
          </div>

          {/* Advanced Filters Popover */}
          <Popover modal>
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                className="group relative flex h-12 items-center gap-2"
                aria-label="Advanced Filters"
              >
                <Icons.settings className="h-5 w-5 transition-colors group-hover:text-primary" />
                <span className="font-medium text-primar text-foreground">
                  Advanced Filters
                </span>
                {advancedFilterCount > 0 && (
                  <span className="absolute -right-1 -top-1 flex h-6 min-w-6 items-center justify-center rounded-full bg-primary/10 px-1 py-0 text-[10px] text-primary">
                    {advancedFilterCount}
                  </span>
                )}
              </Button>
            </PopoverTrigger>
            <PopoverContent
              className="z-[100] max-h-80 overflow-auto rounded-xl border-border/50 p-0 shadow-md"
              align="end"
              sideOffset={8}
            >
              <div className="flex flex-col">
                <div className="space-y-4 p-4">
                  {/* Default Templates Checkbox */}
                  <div className="flex items-center space-x-2 px-2 py-1">
                    <Checkbox
                      id="show-only-default"
                      checked={includeDefaults}
                      onCheckedChange={(checked) =>
                        setIncludeDefaults(!!checked)
                      }
                    />
                    <label
                      htmlFor="show-only-default"
                      className="cursor-pointer text-xs text-foreground"
                    >
                      Default templates
                    </label>
                  </div>
                  {/* Service Filter */}
                  <div>
                    <div className="mb-2 text-xs font-medium text-muted-foreground">
                      Services
                    </div>
                    <div className="flex flex-col gap-1 pr-1">
                      {serviceOptions.map((service) => (
                        <label
                          key={service.value}
                          className="flex cursor-pointer items-center gap-2 rounded px-2 py-1 transition-colors focus-within:ring-2 focus-within:ring-primary/30 hover:bg-accent"
                        >
                          <Checkbox
                            checked={selectedServices.includes(service.value)}
                            onCheckedChange={(checked) => {
                              setSelectedServices((prev) =>
                                checked
                                  ? [...prev, service.value]
                                  : prev.filter((s) => s !== service.value)
                              );
                            }}
                            id={`service-${service.value}`}
                          />
                          <span className="flex items-center gap-1 text-xs text-foreground">
                            <service.icon className="h-5 w-5" />
                            {service.label}
                          </span>
                        </label>
                      ))}
                    </div>
                  </div>
                </div>
                <div className="border-t border-border/50" />
                <div className="p-2">
                  <Button
                    variant="ghost"
                    size="sm"
                    className="w-full text-muted-foreground hover:text-primary"
                    onClick={resetAdvancedFilters}
                  >
                    Reset Filters
                  </Button>
                </div>
              </div>
            </PopoverContent>
          </Popover>
        </div>

        {/* Category badges */}
        <div className="mt-2 flex w-full flex-wrap justify-center gap-2">
          {categoryOptions.map((category) => (
            <Badge
              size="lg"
              key={category.value}
              variant={
                selectedCategory === category.value ? 'default' : 'outline'
              }
              className="cursor-pointer gap-1.5"
              onClick={() => toggleCategory(category.value)}
            >
              <category.icon />
              {category.label}
            </Badge>
          ))}
        </div>
      </div>

      {/* Main content */}
      <div className="px-24 py-6">
        {isPending ? (
          <div className="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-3">
            {[...Array(6)].map((_, i) => (
              <div key={i} className="transition-all duration-300">
                <div className="rounded-3xl border border-border/50 bg-gradient-to-br from-card to-card/40 shadow">
                  <div className="flex h-[120px] flex-col justify-between p-6">
                    <div className="mb-4 flex items-center space-x-3">
                      <Skeleton className="h-8 w-8 rounded-lg bg-primary/10" />
                      <Skeleton className="h-5 w-32" />
                    </div>
                    <Skeleton className="h-4 w-full" />
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : allTemplates.length === 0 ? (
          <div className="h-full rounded-lg text-center">
            <Icons.search className="mx-auto mb-4 h-12 w-12 py-3 text-muted-foreground/50" />
            <h3 className="mb-1 text-xl font-medium">
              No matching operations found
            </h3>
            <p className="text-muted-foreground">
              Try adjusting your filter criteria
            </p>
          </div>
        ) : (
          <>
            <div className="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-3">
              {allTemplates.map((taskTemplate: TaskTemplate) => (
                <div
                  key={taskTemplate.id}
                  className="transition-all duration-200 hover:translate-y-[-2px]"
                >
                  <QuickStartCard
                    icon={getCategoryIcon(taskTemplate.category)}
                    title={taskTemplate.task}
                    description={taskTemplate.context}
                    onClick={() =>
                      onCardClick({
                        icon: getCategoryIcon(taskTemplate.category),
                        title: taskTemplate.task,
                        description: taskTemplate.context,
                        category: taskTemplate.category,
                        functionType: taskTemplate.run_mode,
                        id: taskTemplate.id,
                        service: taskTemplate.service
                      })
                    }
                  />
                </div>
              ))}
            </div>

            {/* Infinite scroll trigger */}
            <div
              ref={ref}
              className="mt-8 flex h-16 items-center justify-center"
            >
              {isFetchingNextPage && (
                <Icons.spinner className="h-6 w-6 animate-spin text-muted-foreground" />
              )}
            </div>
          </>
        )}
      </div>
    </div>
  );
}
