'use client';

export function HeroHeading() {
  return (
    <div className="relative py-8">
      {/* Background Decorative Elements */}
      <div className="absolute inset-0 overflow-hidden">
        {/* Top gradient beam - animated version */}
        <div className="absolute -top-4 left-1/2 h-1.5 w-32 -translate-x-1/2 animate-pulse bg-gradient-to-r from-transparent via-primary/30 to-transparent blur-sm" />

        {/* Glowing orb - animated version */}
        <div
          className="absolute -top-8 left-1/2 h-24 w-24 -translate-x-1/2 rounded-full bg-primary/5 blur-xl"
          style={{
            animation: 'float 6s ease-in-out infinite'
          }}
        />

        {/* Additional decorative beams - animated version */}
        <div className="absolute left-1/2 top-1/2 h-32 w-full -translate-x-1/2 -translate-y-1/2">
          <div
            className="absolute inset-0 rotate-[-35deg] animate-pulse bg-gradient-to-r from-transparent via-primary/5 to-transparent"
            style={{ animationDuration: '4s' }}
          />
          <div
            className="absolute inset-0 rotate-[35deg] animate-pulse bg-gradient-to-r from-transparent via-secondary/5 to-transparent"
            style={{ animationDuration: '4s', animationDelay: '1s' }}
          />
        </div>
      </div>

      {/* Main Content */}
      <div className="relative space-y-4">
        <h1
          className="animate-reveal translate-y-4 cursor-default pb-1 text-center text-4xl font-bold opacity-0 transition-all duration-300 hover:scale-[1.02] sm:text-5xl md:text-6xl"
          style={{
            background: `linear-gradient(
                            135deg,
                            hsl(var(--foreground)) 0%,
                            hsl(var(--foreground)/0.9) 25%,
                            hsl(var(--primary)/0.9) 50%,
                            hsl(var(--foreground)/0.9) 75%,
                            hsl(var(--foreground)) 100%
                        )`,
            backgroundSize: '200% auto',
            backgroundClip: 'text',
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent',
            animation:
              'gradient 8s linear infinite, reveal 1s ease-out forwards'
          }}
        >
          Agentic cloud operation for modern teams
        </h1>

        <p className="animate-reveal-delayed mx-auto max-w-2xl translate-y-4 px-4 text-center text-lg text-muted-foreground opacity-0 sm:text-xl">
          Automate, optimize, and scale your cloud operations with intelligent
          AI agents that work 24/7
        </p>

        {/* Animated underline */}
        <div className="animate-reveal-delayed-2 absolute -bottom-2 left-1/2 h-0.5 w-48 -translate-x-1/2 animate-pulse bg-gradient-to-r from-transparent via-primary/20 to-transparent opacity-0 blur-sm" />
      </div>

      <style jsx global>{`
        @keyframes float {
          0%,
          100% {
            transform: translate(-50%, 0);
          }
          50% {
            transform: translate(-50%, -10px);
          }
        }
        @keyframes gradient {
          0% {
            background-position: 0% 50%;
          }
          50% {
            background-position: 100% 50%;
          }
          100% {
            background-position: 0% 50%;
          }
        }
        @keyframes reveal {
          0% {
            opacity: 0;
            transform: translateY(1rem);
          }
          100% {
            opacity: 1;
            transform: translateY(0);
          }
        }
        .animate-reveal {
          animation: reveal 1s ease-out forwards;
        }
        .animate-reveal-delayed {
          animation: reveal 1s ease-out 0.3s forwards;
        }
        .animate-reveal-delayed-2 {
          animation: reveal 1s ease-out 0.6s forwards;
        }
      `}</style>
    </div>
  );
}
